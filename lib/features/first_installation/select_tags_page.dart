import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/DependencyManager/TextProviders/TextProvider.dart';
import 'package:modarby/Providers/SearchProvider.dart';
import 'package:modarby/Student/Screens/Home.dart';
import 'package:modarby/Widgets/custom_button_widget.dart';
import 'package:modarby/Widgets/custom_text_widget.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/config/themes/images.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';
import 'package:modarby/core/utilities/navigation_service.dart';
import 'package:modarby/core/utilities/shard.dart';
import 'package:modarby/core/utilities/storage.dart';
import 'package:modarby/features/auth/arguments/auth_argument.dart';
import 'package:modarby/features/auth/widgets/custom_app_bar_auth_widget.dart';
import 'package:modarby/features/first_installation/arguments/first_installation_arguments.dart';
import 'package:provider/provider.dart';

import '../../Enums/FontWeights.dart';
import '../../Models/Content/SubSubject.dart';
import '../../Models/Content/Subject.dart';
import '../../Models/Search/SearchListDTO.dart';
import '../../Providers/CollectDataProvider.dart';
import '../../SharedScreens/NotificationsPermission.dart';
import '../../Widgets/sticky_bottom_appbar.dart';
import '../../core/utilities/routing_user.dart';

class SelectTagsPage extends StatefulWidget {
  static const routeName = '/SelectTagsPage';
  final AuthArgument? argument;
  const SelectTagsPage({
    this.argument,
  });

  @override
  State<SelectTagsPage> createState() => _SelectTagsPageState();
}

class _SelectTagsPageState extends State<SelectTagsPage> {
  late TextProvider _textProvider;

  late LocalizationProvider _localizationProvider;
  late SearchProvider _searchProvider;
  Map<int, List<int>> _selected = {};
  late bool _dataFilled;

  final _internalMargin = 10.0;
  final _elevation = 0.0;
  // final _dividerThickness = 1.0;
  final PageController _pageController = PageController(initialPage: 0);
  int index = 0;
  @override
  void initState() {
    _initializeData();
    _loadData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _body(),
      bottomNavigationBar: _buildSubmitButtons(),
    );
  }

  Widget _body() {
    return SafeArea(
      child: SizedBox(
        height: double.infinity,
        width: double.infinity,
        child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _appBar(),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    _note(),
                    SizedBox(height: _internalMargin * 2),
                    _buildPageView(),
                  ],
                ),
              ),
            ]),
      ),
    );
  }

  Widget _buildPageView() {
    return Expanded(
      child: PageView.builder(
        onPageChanged: (i) {
          setState(() {
            index = i;
          });
        },
        physics: const NeverScrollableScrollPhysics(),
        itemCount: widget.argument?.firstInstallationArguments?.listSubSubject
            ?.where((element) => element.tags?.isNotEmpty ?? false)
            .toList()
            .length,
        controller: _pageController,
        itemBuilder: (context, pageIndex) {
          final subSubject = widget
              .argument?.firstInstallationArguments?.listSubSubject
              ?.where((element) => element.tags?.isNotEmpty ?? false)
              .toList()[pageIndex];
          return _buildSubSubjectTagsChip(subSubject);
        },
      ),
    );
  }

  Widget _buildSubSubjectTagsChip(SubSubject? subSubject) {
    if (subSubject?.tags?.isNotEmpty ?? false) {
      return SingleChildScrollView(
        child: Column(
          children: [
            _buildSubtitle(
                isAr ? subSubject?.nameAr : subSubject?.nameEn, subSubject?.id),
            Wrap(
              alignment: WrapAlignment.start,
              spacing: _internalMargin,
              runSpacing: _internalMargin / 2,
              children: (subSubject?.tags ?? []).map((tag) {
                return _buildSubjectChip(tag, subSubject?.id);
              }).toList(),
            ),
            // _buildDivider()
          ],
        ),
      );
    }
    return const SizedBox();
  }

  _note() {
    return Container(
      margin: EdgeInsetsDirectional.only(
        start: 16.w,
        end: 16.w,
        top: 10.sp,
      ),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: ThemeColors.buttonsBackgroundColor,
          border:
              Border.all(color: ThemeColors.buttonsBackgroundColor, width: 1)),
      padding: EdgeInsetsDirectional.symmetric(
        horizontal: 8.w,
        vertical: 8.sp,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(
            Images.infoSignUpTagsNoteIcon,
            width: 16.w,
            height: 16.w,
            color: ThemeColors.matchingRateGood,
          ),
          Flexible(
            child: CustomTextWidget(
              title: STUDENT_TAGS_NOTE.translate(),
              size: 11,
              paddingStart: 4.w,
              fontWeight: FontWeight.w600,
              color: ThemeColors.color1C1C1E,
              textAlign: TextAlign.start,
            ),
          ),
        ],
      ),
    );
  }

  bool get fromEditSearch => widget.argument?.fromEditSearch ?? false;

  Widget _appBar() {
    return CustomAppBarAuthWidget(
      title: STUDENT_TAGS_TITLE.translate(),
      showBackTitle: false,
      showBack: true,
      showClose: false,
      showProgress: true,
      valueProgress: 4.5 / 5,
      onClickBack: () {
        if (_pageController.page! > 0) {
          _pageController.previousPage(
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
          return;
        } else {
          Navigator.pop(context);
        }
      },
    );
  }

  Widget _buildSubtitle(title, int? subjectId) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            CustomTextWidget(
              title: INSTRUCTOR_TAGS_PREVIEW_TITLE.translate(),
              fontWeight: FontWeight.bold,
              size: 15,
              paddingStart: 16.w,
              color: ThemeColors.black,
            ),
            CustomTextWidget(
              title: title,
              fontWeight: FontWeight.bold,
              size: 15,
              paddingStart: 3.w,
              color: ThemeColors.accentColor,
            ),
          ],
        ),
        Visibility(
          visible: (_selected[subjectId] ?? []).isNotEmpty,
          replacement: SizedBox(
            height: 27.sp,
          ),
          child: Container(
            margin: EdgeInsetsDirectional.only(
              end: 16.w,
            ),
            padding: EdgeInsetsDirectional.only(
              start: 10.w,
              end: 10.w,
              top: 5.sp,
              bottom: 5.sp,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50),
              border: Border.all(
                color: ThemeColors.color26467A,
                width: 1,
              ),
              color: ThemeColors.color26467A.withOpacity(0.10),
            ),
            child: Row(
              children: [
                CustomTextWidget(
                  title: INSTRUCTOR_TAGS_SELECTED_TITLE.translate(),
                  fontWeight: FontWeight.w400,
                  size: 12,
                  paddingEnd: 3.w,
                  color: ThemeColors.color1C1C1E,
                ),
                CustomTextWidget(
                  title: Shard()
                      .padLeftNumber((_selected[subjectId] ?? []).length)
                      .toString(),
                  fontWeight: FontWeight.w400,
                  size: 12,
                  color: ThemeColors.color1C1C1E,
                ),
              ],
            ),
          ),
        )
      ],
    );
  }

  bool get isAr => _localizationProvider.locals.language == 'ar';

  Widget _buildSubjectChip(Tag tag, int? subjectId) {
    return ChoiceChip(
      padding: EdgeInsets.zero,
      labelPadding: const EdgeInsets.symmetric(horizontal: 5),
      pressElevation: _elevation,
      backgroundColor: ThemeColors.colorF2F2F7,
      selectedColor: ThemeColors.color26467A.withOpacity(.10),
      shape: _buildChipShape(tag, subjectId),
      label: _buildSubjectName(isAr ? tag.nameAr : tag.nameEn),
      selected:
          (_selected[subjectId] ?? []).any((element) => element == tag.id),
      onSelected: (value) => _onSelect(value, subjectId, tag),
      showCheckmark: false,
    );
  }

  StadiumBorder _buildChipShape(Tag tag, int? subjectId) {
    return StadiumBorder(
      side: BorderSide(
        color: (_selected[subjectId] ?? []).any((element) => element == tag.id)
            ? ThemeColors.color26467A
            : ThemeColors.defaultBorderColor,
      ),
    );
  }

  Widget _buildSubjectName(String? name) {
    return _textProvider.buildNormalText4(
      name,
      spEnabled: true,
      weight: FontWeights.medium,
    );
  }

  Widget _buildSubmitButtons() {
    return StickyBottomAppBar(
      child: Row(
        children: [
          Expanded(
            child: _buildSubmit(),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmit() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CustomButtonWidget(
          colorButton: ThemeColors.color26467A,
          onPressed: _submit,
          title: BTN_NEXT.translate(),
          titleColor: ThemeColors.white,
          padding: EdgeInsetsDirectional.only(
            top: 14.sp,
            bottom: 14.sp,
          ),
        ),
      ],
    );
  }

  void _initializeData() {
    _textProvider = context.read<DependencyManager>().text;
    _localizationProvider = context.read<DependencyManager>().localization;

    _searchProvider = context.read<SearchProvider>();
    _selected = {};
    _dataFilled = false;
  }

  void _fillData() {
    _fillSelectedTags();
    _dataFilled = true;
  }

  void _fillSelectedTags() {
    if (widget.argument?.firstInstallationArguments?.tags != null &&
        (widget.argument?.firstInstallationArguments?.tags?.isNotEmpty ??
            false)) {
      widget.argument?.firstInstallationArguments?.tags?.forEach((key, value) {
        List<String> values = value.toString().split("-");

        List<int> filteredValues = values
            .map(int.parse)
            .where((id) =>
                widget.argument?.firstInstallationArguments?.listSubSubject
                    ?.any((subSubject) =>
                        subSubject.tags?.any((tag) => tag.id == id) ?? false) ??
                false)
            .toSet()
            .toList();

        _selected[int.parse(key)] = filteredValues;
      });
    }
  }

  _loadData() {
    if (!_dataFilled) _fillData();
  }

  void _onSelect(bool value, int? subjectId, Tag tag) {
    if (value) {
      if (_selected.containsKey(subjectId)) {
        _selected[subjectId]?.add(tag.id ?? 0);
      } else {
        _selected[subjectId ?? 0] = [tag.id ?? 0];
      }
    } else {
      _selected[subjectId]?.remove(tag.id);
      if (_selected[subjectId]?.isEmpty ?? false) {
        _selected.remove(subjectId);
      }
    }
    setState(() {});
  }

  bool? isTagSelected() {
    final SubSubject? currentSubSubject = widget
        .argument?.firstInstallationArguments?.listSubSubject
        ?.where((element) => element.tags?.isNotEmpty ?? false)
        .toList()[index];
    if (_selected.containsKey(currentSubSubject?.id)) {
      return true;
    }
    return false;
  }

  Future<void> _submit() async {
    if (_pageController.page! <
        (widget.argument?.firstInstallationArguments?.listSubSubject
                    ?.where((element) => element.tags?.isNotEmpty ?? false)
                    .toList()
                    .length ??
                0) -
            1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
      return;
    } else {
      final firstInstallationArguments = generatedFirstArgument(
          widget.argument?.firstInstallationArguments?.major);
      // here we need to check if come from register or not and if i need to check student info or not
      if (widget.argument?.fromEditSearch ?? false) {
        await _search(firstInstallationArguments, true);
      } else {
        if (getIt<Storage>().isComeRegistered) {
          RoutingUser().checkDataFromSubject(widget.argument, context);
        } else {
          final notificationEnabled = await context
              .read<CollectDataProvider>()
              .getNotificationsAllowed();
          if (notificationEnabled || StaticVar.isStudent.inverted) {
            if ((widget.argument?.fromSkip ?? false) ||
                getIt<Storage>().isComeFromGetStarted) {
              await _search(firstInstallationArguments, false);
            } else {
              RoutingUser().checkDataFromSubject(widget.argument, context);
            }
          } else {
            NavigationService.instance.navigateToAndRemove(
              NotificationsPermissionScreen.routeName,
              args: widget.argument,
            );
          }
        }
      }
    }
  }

  FirstInstallationArguments? generatedFirstArgument(
    Subject? major,
  ) {
    FirstInstallationArguments? firstInstallationArguments =
        widget.argument?.firstInstallationArguments;

    firstInstallationArguments?.selectFromSearchTags =
        _selected.values.expand((list) => list).join('-');

    final t = {
      for (SubSubject subSubject
          in widget.argument?.firstInstallationArguments?.listSubSubject ?? [])
        if (_selected.containsKey(subSubject.id))
          "${subSubject.id}": _selected[subSubject.id]?.join('-') ?? ''
    };

    firstInstallationArguments?.tags = t;

    widget.argument?.firstInstallationArguments = firstInstallationArguments;
    StaticVar.context
        .read<AuthenticationProvider>()
        .saveData(firstInstallationArguments);

    return firstInstallationArguments;
  }

  Future<void> _search(FirstInstallationArguments? firstInstallationArguments,
      bool clearSearch) async {
    final dto = _generateDTO(firstInstallationArguments);
    _searchProvider.setCurrentSearchQuery = dto!;
    if (clearSearch) {
      _searchProvider.resetFilter();
    }
    if (widget.argument?.comeFromOtherPlace ?? false) {
      NavigationService.instance.navigateToAndRemove(
        widget.argument?.routeScreen ?? StudentHomeScreen.routeName,
      );
    } else {
      NavigationService.instance
          .navigateToAndRemove(StudentHomeScreen.routeName);
    }
  }

  SearchListDTO? _generateDTO(
      FirstInstallationArguments? firstInstallationArguments) {
    firstInstallationArguments?.selectFromSearchTags =
        _selected.values.expand((list) => list).join('-');

    final t = {
      for (SubSubject subSubject
          in firstInstallationArguments?.listSubSubject ?? [])
        if (_selected.containsKey(subSubject.id))
          "${subSubject.id}": _selected[subSubject.id]?.join('-') ?? ''
    };

    firstInstallationArguments?.tags = t;

    firstInstallationArguments?.timeZone = _getTimeZone();
    return firstInstallationArguments?.toSearchListDTO();
  }

  int? _getTimeZone() {
    final code = getIt<Storage>().countryCode;
    final timeZoneID = Shard().getTimezoneWithIsoCountryCode(code);
    return timeZoneID;
  }
}
