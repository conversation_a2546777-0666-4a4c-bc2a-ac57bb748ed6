import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:modarby/Enums/FontWeights.dart';
import 'package:modarby/Models/Content/SubSubject.dart';
import 'package:modarby/Models/Content/Subject.dart';
import 'package:modarby/Models/Content/TraineeTypes.dart';
import 'package:modarby/Models/Search/SearchListDTO.dart';
import 'package:modarby/Providers/CollectDataProvider.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/DependencyManager/TextProviders/TextProvider.dart';
import 'package:modarby/Providers/SearchProvider.dart';
import 'package:modarby/SharedScreens/NotificationsPermission.dart';
import 'package:modarby/Student/Screens/Home.dart';
import 'package:modarby/UIDTOs/Search/SearchSubject.dart';
import 'package:modarby/Widgets/SearchCustomWidget.dart';
import 'package:modarby/Widgets/custom_text_widget.dart';
import 'package:modarby/Widgets/flip_if_rtl.dart';
import 'package:modarby/Widgets/image_loading.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/config/themes/images.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/utilities/extentions/safe_cast.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';
import 'package:modarby/core/utilities/navigation_service.dart';
import 'package:modarby/core/utilities/routing_user.dart';
import 'package:modarby/core/utilities/storage.dart';
import 'package:modarby/features/auth/arguments/auth_argument.dart';
import 'package:modarby/features/auth/widgets/custom_app_bar_auth_widget.dart';
import 'package:modarby/features/first_installation/arguments/first_installation_arguments.dart';
import 'package:modarby/features/first_installation/select_subjects_first_installation_page.dart';
import 'package:provider/provider.dart';

class FindExpertInstallationPage extends StatefulWidget {
  static const routeName = '/FindExpertInstallationPage';
  final AuthArgument? arguments;
  const FindExpertInstallationPage({
    Key? key,
    required this.arguments,
  }) : super(key: key);

  @override
  State<FindExpertInstallationPage> createState() =>
      _FindExpertInstallationPageState();
}

class _FindExpertInstallationPageState
    extends State<FindExpertInstallationPage> {
  List<Subject>? _subjects;
  Map? _subSubjects;
  List<SearchSubject>? _searchSuggestions;
  late LocalizationProvider _localizationProvider;
  late SearchProvider _searchProvider;
  late List<TraineeTypes> levels;

  late IconsProvider iconsProvider;
  TextEditingController searchController = TextEditingController();
  final gapPadding = 0.0;
  final widgetMargin = 10.0;
  late TextProvider textProvider;
  bool startSearch = false;
  @override
  void initState() {
    _initializeData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    initWatcher();
    return Scaffold(
      body: _body(),
    );
  }

  void _initializeData() {
    iconsProvider = context.read<DependencyManager>().icons;
    textProvider = context.read<DependencyManager>().text;
    _localizationProvider = context.read<DependencyManager>().localization;

    _subjects = safeCastOrDefault<List<Subject>?>(
            _localizationProvider.subjects.getAll(),
            defaultValue: [])
        ?.where((element) => element.levels
            .contains(widget.arguments?.firstInstallationArguments?.levelId))
        .toList();
    _subSubjects = _localizationProvider.subSubjects.getAll();
    levels = _localizationProvider.lookups
        .getWithKey(TRAINEETYPE)
        .cast<TraineeTypes>();

    _searchSuggestions = safeCastOrDefault<List<SearchSubject>?>(
            _localizationProvider.searchSuggestions.getAll(),
            defaultValue: [])
        ?.where((element) =>
            (getSubject(int.tryParse(element.subjectId.toString()))?.levels ??
                    [])
                .contains(
                    widget.arguments?.firstInstallationArguments?.levelId))
        .toList();
  }

  Subject? getSubject(int? id) {
    return _subjects?.firstWhereOrNull((element) => element.id == id);
  }

  void _dismissKeyboard() {
    FocusScope.of(context).unfocus();
  }

  Widget _body() {
    return SafeArea(
      child: SizedBox(
        height: double.infinity,
        width: double.infinity,
        child: GestureDetector(
          onTap: _dismissKeyboard,
          child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                _appBar(),
                _buildSearch(),
                // _description(),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        _listWidget(),
                      ],
                    ),
                  ),
                ),
              ]),
        ),
      ),
    );
  }

  bool get fromEditSearch => widget.arguments?.fromEditSearch ?? false;
  Widget _appBar() {
    return CustomAppBarAuthWidget(
      title: StaticVar.isStudent ||
              getIt<Storage>().instructorComeFromViewSimilarTutors.inverted
          ? findExpertTitle.translate()
          : areaViewSimilarTitle.translate(),
      showBackTitle: false,
      showBack: true,
      showClose: false,
      showProgress: fromEditSearch.inverted,
      valueProgress: 4 / 5,
    );
  }

  Widget _description() {
    return Container(
      padding: EdgeInsetsDirectional.only(bottom: 20.sp),
      color: ThemeColors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          CustomTextWidget(
            title: startSearch
                ? resultSearchDescription.translate()
                : firstInstallationFindExpertDescription.translate(),
            size: 15,
            paddingStart: 16.w,
            paddingEnd: 16.w,
            fontWeight: FontWeight.w500,
            color: ThemeColors.black,
          ),
        ],
      ),
    );
  }

  Widget _listWidget() {
    return Visibility(
      visible: startSearch.inverted,
      replacement: _listSearch(),
      child: ListView.separated(
        padding: EdgeInsetsDirectional.only(
          start: 16.w,
          end: 16.w,
        ),
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _subjects?.length ?? 0,
        separatorBuilder: _separatorBuilder,
        itemBuilder: _itemBuilder,
      ),
    );
  }

  Widget? _itemBuilder(BuildContext context, int index) {
    final subjectID =
        widget.arguments?.firstInstallationArguments?.subSubjectId;
    final item = _subjects?[index];
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => _onClickItem(item),
      child: Container(
        padding: EdgeInsetsDirectional.only(
          start: 10.w,
          end: 10.w,
          top: 9.sp,
          bottom: 9.sp,
        ),
        decoration: BoxDecoration(
          border: Border.all(
            color: subjectID == item?.id
                ? ThemeColors.color26467A
                : ThemeColors.colorF2F2F7,
          ),
          borderRadius: BorderRadius.circular(10),
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.06),
              spreadRadius: 0,
              blurRadius: 10,
              offset: Offset(0, 0),
            ),
          ],
          color: ThemeColors.white,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                ImageLoading(
                  imageUrl: item?.image ?? '',
                  width: 32.sp,
                  height: 32.sp,
                  colorCircle: ThemeColors.color26467A,
                ),
                SizedBox(width: 16.sp),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    CustomTextWidget(
                      title: item?.name ?? '',
                      textAlign: TextAlign.start,
                      color: ThemeColors.color1C1C1E,
                      size: 16,
                      maxLine: 2,
                      fontWeight: FontWeight.w600,
                    ),
                    CustomTextWidget(
                      title:
                          '${_getSubSubjectsCount(item?.id).toString()} ${LIST_SUBJECTS.translate()}',
                      textAlign: TextAlign.start,
                      color: ThemeColors.color1C1C1E,
                      size: 12,
                      paddingTop: 2.sp,
                      maxLine: 2,
                      fontWeight: FontWeight.w400,
                    )
                  ],
                )
              ],
            ),
            FlipIfRTL(builder: (context) {
              return SvgPicture.asset(
                Images.arrowLeftWithBorder,
                width: 24.sp,
                height: 24.sp,
              );
            }),
          ],
        ),
      ),
    );
  }

  int? _getSubSubjectsCount(int? subjectId) {
    return _subSubjects?['$subjectId'].length;
  }

  Widget _buildSearch() {
    return Padding(
      padding: EdgeInsetsDirectional.only(
        start: 16.w,
        end: 16.w,
        bottom: 16.sp,
      ),
      child: Container(
        decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(
              Radius.circular(30),
            ),
            color: ThemeColors.white),
        height: 40.sp,
        width: MediaQuery.of(context).size.width,
        margin: EdgeInsets.only(top: widgetMargin),
        child: TextField(
          controller: searchController,
          textInputAction: TextInputAction.unspecified,
          style: textProvider.buildStyle(
            14.sp,
            FontWeights.semiBold,
            ThemeColors.black1A1818,
            null,
            null,
          ),
          decoration: InputDecoration(
            hintText: COMINGSOON_SEARCHOPTION.translate(),
            hintStyle: textProvider.buildStyle(
              14.sp,
              FontWeights.semiBold,
              ThemeColors.color8E8E93,
              null,
              null,
            ),
            filled: true,
            contentPadding: EdgeInsets.zero,
            fillColor: ThemeColors.colorF2F2F7,
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: ThemeColors.primaryColor),
              borderRadius: BorderRadius.circular(30),
              gapPadding: gapPadding,
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: ThemeColors.primaryColor),
              borderRadius: BorderRadius.circular(30),
              gapPadding: gapPadding,
            ),
            prefixIcon: iconsProvider.parametrizedIcon(
              iconsProvider.search,
              width: 24.w,
              height: 24.w,
            ),
            suffixIcon: searchController.text.isNotEmpty
                ? GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: _removeText,
                    child: SizedBox(
                      width: 40.sp,
                      height: 40.sp,
                      child: iconsProvider.parametrizedIcon(
                        iconsProvider.closeIconWithBorder,
                        width: 16.w,
                        height: 16.w,
                      ),
                    ),
                  )
                : null,
          ),
          onChanged: (text) {
            if (searchController.text.isNotEmpty) {
              startSearch = true;
              setState(() {});
            } else {
              startSearch = false;
              setState(() {});
            }
          },
        ),
      ),
    );
  }

  FirstInstallationArguments? generatedFirstArgument(
    int? majorId,
    int? subjectId,
  ) {
    final List<SubSubject> subjects = _localizationProvider.subSubjects
        .getWithKey('$majorId')
        .cast<SubSubject>();
    final selectionSubject = subjects.where((e) => e.id == subjectId).toList();
    final major = _subjects?.firstWhereOrNull(
      (subject) => subject.id == majorId,
    );
    FirstInstallationArguments? firstInstallationArguments =
        widget.arguments?.firstInstallationArguments;
    firstInstallationArguments?.major = major;
    firstInstallationArguments?.listSubSubject = selectionSubject;
    widget.arguments?.firstInstallationArguments = firstInstallationArguments;

    final searchDto = firstInstallationArguments?.toSearchListDTO();
    context.read<SearchProvider>().saveData(searchDto!);
    return firstInstallationArguments;
  }

  Future<void> _search(SearchListDTO? searchListDTO, bool clearSearch) async {
    _searchProvider.setCurrentSearchQuery = searchListDTO;
    if (clearSearch) {
      _searchProvider.resetFilter();
    }
    if (widget.arguments?.comeFromOtherPlace ?? false) {
      NavigationService.instance.navigateToAndRemove(
        widget.arguments?.routeScreen ?? StudentHomeScreen.routeName,
      );
    } else {
      NavigationService.instance
          .navigateToAndRemove(StudentHomeScreen.routeName);
    }
  }

  void _onClickItem(Subject? item) {
    setState(() {});
    final argument = widget.arguments;
    argument?.firstInstallationArguments?.major = item;
    argument?.firstInstallationArguments?.subSubjectId = item?.id;
    NavigationService.instance.navigateTo(
      SelectSubjectsFirstInstallationPage.routeName,
      args: argument,
    );
  }

  void initWatcher() {
    _searchProvider = context.watch<SearchProvider>();
  }

  Widget _separatorBuilder(BuildContext context, int index) {
    return SizedBox(height: 16.sp);
  }

  void _removeText() {
    searchController.text = '';
    startSearch = false;
    setState(() {});
  }

  Widget _listSearch() {
    final filteredItems = _searchSuggestions?.where((item) {
      final String searchQuery = searchController.text.toLowerCase().trim();
      final String itemText = item.name.toLowerCase();

      // If search is empty, don't filter
      if (searchQuery.isEmpty) {
        return true;
      }

      // Split both the search query and item text into words
      final List<String> searchWords = searchQuery.split(' ');
      final List<String> itemWords = itemText.split(' ');

      // If we're searching for multiple words
      if (searchWords.length > 1) {
        // Try to match the exact phrase first
        if (itemText.contains(searchQuery)) {
          return true;
        }

        // Check if all search words appear in the item words in any order
        return searchWords.every((searchWord) =>
            itemWords.any((itemWord) => itemWord.contains(searchWord)));
      }
      // If searching for a single word
      else {
        return itemWords.any((word) => word.contains(searchQuery));
      }
    }).toList();

    return Visibility(
      visible: (filteredItems ?? []).isNotEmpty,
      replacement: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsetsDirectional.only(
              start: 16.w,
            ),
            child: NoItemFound(),
          ),
        ],
      ),
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: filteredItems?.length ?? 0,
        itemBuilder: (
          BuildContext context,
          int index,
        ) {
          final item = filteredItems?[index];
          return GestureDetector(
            onTap: () => _doSearch(item),
            behavior: HitTestBehavior.translucent,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Divider(
                  color: ThemeColors.grayE5E5EA,
                ),
                SizedBox(
                  height: 7.sp,
                ),
                CustomTextWidget(
                  title: item?.name,
                  color: ThemeColors.color1C1C1E,
                  fontWeight: FontWeight.w500,
                  size: 16,
                  paddingEnd: 16.w,
                  paddingStart: 16.w,
                ),
                SizedBox(
                  height: 7.sp,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  _doSearch(SearchSubject? item) async {
    _removeText();
    if (item != null) {
      final firstInstallationArguments = generatedFirstArgument(
        int.tryParse((item.subjectId).toString()),
        int.tryParse(item.subSubjectId.toString()),
      );

      if (widget.arguments?.fromEditSearch ?? false) {
        await _search(firstInstallationArguments?.toSearchListDTO(), true);
      } else {
        if (getIt<Storage>().isComeRegistered) {
          RoutingUser().checkDataFromSubject(widget.arguments, context);
        } else {
          final notificationEnabled = await context
              .read<CollectDataProvider>()
              .getNotificationsAllowed();
          if (notificationEnabled || StaticVar.isStudent.inverted) {
            if ((widget.arguments?.fromSkip ?? false) ||
                getIt<Storage>().isComeFromGetStarted) {
              await _search(
                  firstInstallationArguments?.toSearchListDTO(), false);
            } else {
              RoutingUser().checkDataFromSubject(widget.arguments, context);
            }
          } else {
            NavigationService.instance.navigateToAndRemove(
              NotificationsPermissionScreen.routeName,
              args: widget.arguments,
            );
          }
        }
      }
    }
  }
}
