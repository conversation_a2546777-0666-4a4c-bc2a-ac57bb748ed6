import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/InstructorDashboardProvider.dart';
import 'package:modarby/Providers/search_result_tutor_provider.dart';
import 'package:modarby/Widgets/custom_button_widget.dart';
import 'package:modarby/Widgets/custom_text_widget.dart';
import 'package:modarby/Widgets/sticky_bottom_appbar.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/config/themes/images.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/notifications/firebase_messaging_impl.dart';
import 'package:modarby/core/utilities/DeepLinking.dart';
import 'package:modarby/core/utilities/language/LanguagesKeys.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';
import 'package:modarby/core/utilities/navigation_service.dart';
import 'package:modarby/core/utilities/storage.dart';
import 'package:modarby/features/auth/arguments/auth_argument.dart';
import 'package:modarby/features/auth/login/login_page.dart';
import 'package:provider/provider.dart';

class StartInstallationPage extends StatefulWidget {
  const StartInstallationPage({Key? key}) : super(key: key);
  static const routeName = '/StartInstallationPage';

  @override
  State<StartInstallationPage> createState() => _StartInstallationPageState();
}

class _StartInstallationPageState extends State<StartInstallationPage> {
  late LocalizationProvider _localizationProvider;
  late AuthenticationProvider authProvider;
  late StreamSubscription<Uri> deepLinkStream;

  @override
  void initState() {
    super.initState();
    getIt<Storage>().role = '';
    setCurrencyDefault();
    _initDeepLink();
    _handleNotificationFirebaseWhenAppClosed();
    context.read<InstructorDashboardProvider>().clearNotification();
  }

  @override
  void dispose() {
    deepLinkStream.cancel();
    super.dispose();
  }

  setCurrencyDefault() async {
    const SAUDI_ARABIA_KEY = 120;
    dynamic defaultCurrency = StaticVar.context
        .read<DependencyManager>()
        .localization
        .currencies
        .getWithKey(SAUDI_ARABIA_KEY);
    if (getIt<Storage>().countryCode.isNotEmpty) {
      try {
        final countryCode = getIt<Storage>().countryCode;
        final currency = StaticVar.context
            .read<DependencyManager>()
            .localization
            .currencies
            .getWithCountryCode(countryCode);
        defaultCurrency = currency ?? defaultCurrency;
      } catch (error) {
        log(error.toString());
      }
    }
    dynamic jsonCurrency = context
        .read<DependencyManager>()
        .localization
        .currencies
        .getJsonWithKey(defaultCurrency.id);
    await context
        .read<DependencyManager>()
        .changeCurrency(json.encode(jsonCurrency));
  }

  void _initializeData() {
    _localizationProvider = context.watch<DependencyManager>().localization;
    _localizationProvider.currencies.getAll();
    authProvider = context.read<AuthenticationProvider>();
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    _initializeData();
    return Scaffold(
      backgroundColor: ThemeColors.white,
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Stack(
                    children: [
                      Image.asset(
                        Images.backgroundStartImage,
                        width: MediaQuery.of(context).size.width,
                        height: MediaQuery.of(context).size.height * 0.55,
                        fit: BoxFit.fill,
                      ),
                      Positioned(
                        left: 16.w,
                        top: 56.sp,
                        child: GestureDetector(
                          onTap: _changeLanguage,
                          child: Container(
                            padding: EdgeInsetsDirectional.all(8.sp),
                            decoration: BoxDecoration(
                              color: ThemeColors.white,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CustomTextWidget(
                                  title:
                                      _localizationProvider.locals.language ==
                                              LanguagesKeys.ARABIC
                                          ? MOBILE_LANGUAGE_ENGLISH.translate()
                                          : MOBILE_LANGUAGE_ARABIC.translate(),
                                  color: ThemeColors.black,
                                  size: 16,
                                  paddingEnd: 5.w,
                                  fontWeight: FontWeight.w600,
                                ),
                                SvgPicture.asset(
                                  _localizationProvider.locals.language ==
                                          LanguagesKeys.ARABIC
                                      ? Images.englishLangIcon
                                      : Images.sudiaArbiaLangIcon,
                                  width: 16.sp,
                                  height: 16.sp,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  _title(),
                  SizedBox(height: 10.sp),
                  _description(),
                  SizedBox(height: 30.sp),
                ],
              ),
            ),
          ),
          _bottomNavigation(),
          SizedBox(height: 100.sp),
        ],
      ),
    );
  }

  _bottomNavigation() {
    return StickyBottomAppBar(
        showDecoration: false,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomButtonWidget(
              // gradient: LinearGradient(
              //   colors: [
              //     ThemeColors.colorFF9500,
              //     ThemeColors.colorED6700,
              //   ],
              //   begin: Alignment.centerLeft,
              //   end: Alignment.centerRight,
              // ),
              colorButton: ThemeColors.color26467A,
              onPressed: _onClickStart,
              titleColor: ThemeColors.white,
              padding: EdgeInsetsDirectional.only(
                top: 14.sp,
                bottom: 14.sp,
              ),
              title: getStartTitle.translate(),
              radius: 10.sp,
            ),
            /*
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomTextWidget(
                  title: getStartATourTitle.translate(),
                  color: ThemeColors.color1C1C1E,
                  size: 13,
                  paddingTop: 10.sp,
                  paddingBottom: 10.sp,
                  fontWeight: FontWeight.w400,
                ),
                SizedBox(
                  width: 2.w,
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: _onClickRegister,
                  child: CustomTextWidget(
                    title: getStartRegisterNowTitle.translate(),
                    color: ThemeColors.matchingRateGood,
                    size: 13,
                    paddingTop: 10.sp,
                    paddingBottom: 10.sp,
                    decoration: TextDecoration.underline,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            )
*/
          ],
        ));
  }

  void _onClickStart() {
    FireBaseMessagingImpl.fireBaseMessagingImpl.deleteToken();
    context.read<SearchResultProvider>().closeRequest();
    context.read<SearchResultProvider>().closeTimer();
    getIt<Storage>().role = '';
    getIt<Storage>().isComeFromGetStarted = true;
    getIt<Storage>().isComeRegistered = false;
    final argument = AuthArgument(
      comeFromRegister: false,
      showBack: true,
    );

    NavigationService.instance.navigateTo(
      LoginPage.routeName,
      args: argument,
    );
  }

  Future<void> _onClickRegister() async {
    FireBaseMessagingImpl.fireBaseMessagingImpl.deleteToken();

    getIt<Storage>().role = '';
    _confirmLanguage();
    getIt<Storage>().isComeFromGetStarted = false;
    getIt<Storage>().isComeRegistered = true;
    context.read<SearchResultProvider>().closeRequest();
    context.read<SearchResultProvider>().closeTimer();

    final argument = AuthArgument(
      showBack: true,
      routeScreen: StartInstallationPage.routeName,
      comeFromRegister: true,
    );
    NavigationService.instance.navigateTo(
      LoginPage.routeName,
      args: argument,
    );
  }

  Future<void> _changeLanguage() async {
    final code = _localizationProvider.locals.language;
    await context.read<DependencyManager>().changeLanguageWithoutRout(
          code == LanguagesKeys.ENGLISH
              ? LanguagesKeys.ARABIC
              : LanguagesKeys.ENGLISH,
        );
    setState(() {});
  }

  Future<void> _confirmLanguage() async {
    final code = _localizationProvider.locals.language;
    await context.read<DependencyManager>().changeLanguageWithoutRout(code);
    setState(() {});
  }

  Widget _title() {
    return Padding(
      padding: EdgeInsetsDirectional.only(
        start: 16.w,
        top: 23.sp,
        end: 16.w,
      ),
      child: Text.rich(
        TextSpan(
          text: '${onDemandTitle.translate()}',
          style: TextStyle(
            color: ThemeColors.black,
            fontWeight: FontWeight.w700,
            fontSize: 28.sp,
          ),
          // children: [
          //   TextSpan(
          //     text: '${privateTouringTitle.translate()} ',
          //     style: TextStyle(
          //         color: ThemeColors.accentColor,
          //         fontWeight: FontWeight.w700,
          //         fontSize: 28.sp),
          //     children: [
          //       TextSpan(
          //         text: supportTitle.translate(),
          //         style: TextStyle(
          //           color: ThemeColors.black,
          //           fontWeight: FontWeight.w700,
          //           fontSize: 28.sp,
          //         ),
          //       )
          //     ],
          //   )
          // ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _description() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CustomTextWidget(
          title: studyAnyTimeTitle.translate(),
          color: ThemeColors.gray48484A,
          textAlign: TextAlign.center,
          size: 15,
          fontWeight: FontWeight.w400,
        ),
      ],
    );
  }

  // void _getLookUps() {
  //   _servicesProvider = DefaultServicesProvider();
  //   _servicesProvider.content.fetchLookups();
  // }

  Future<void> _initDeepLink() async {
    deepLinkStream = DeepLinking.deepLinking.listenDynamicLinks();
  }

  void _handleNotificationFirebaseWhenAppClosed() {
    FireBaseMessagingImpl.fireBaseMessagingImpl
        .handleNotificationsWhenAppClosed();
  }
}
