import 'package:flutter/material.dart';
import 'package:modarby/Providers/CollectDataProvider.dart';
import 'package:modarby/Widgets/notification_widget_bottom_sheet.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/notifications/firebase_messaging_impl.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/navigation_service.dart';
import 'package:modarby/core/utilities/storage.dart';
import 'package:modarby/features/messages/domain/enum/conversation_place.dart';
import 'package:modarby/features/messages/presentation/arguments/conversation_argument.dart';
import 'package:modarby/features/messages/presentation/views/conversation_screen.dart';
import 'package:modarby/features/messages/presentation/widgets/build_conversation_instructor_widget.dart';
import 'package:modarby/features/messages/presentation/widgets/build_conversation_student_widget.dart';
import 'package:provider/provider.dart';

import '../../../../Enums/FontWeights.dart';
import '../../../../Enums/Roles.dart';
import '../../../../Models/Messages/ConversationModel.dart';
import '../../../../Models/Messages/Messages.dart';
import '../../../../Providers/AuthenticationProvider.dart';
import '../../../../Providers/DependencyManager/DependencyManager.dart';
import '../../../../Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import '../../../../Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import '../../../../Providers/DependencyManager/TextProviders/TextProvider.dart';
import '../../../../Providers/MessagesProvider.dart';
import '../../../../core/utilities/ProgressIndicators.dart';
import '../../../../core/utilities/language/LanguagesKeys.dart';

class MessagesScreen extends StatefulWidget {
  static const routeName = '/Messages';

  @override
  _MessagesScreenState createState() => _MessagesScreenState();
}

class _MessagesScreenState extends State<MessagesScreen> {
  late TextProvider textProvider;
  late IconsProvider iconsProvider;

  late LocalizationProvider localizationProvider;

  late AuthenticationProvider authenticationProvider;
  late MessagesProvider messagesProvider;

  Messages? messages;

  String? noMessagesTitle;
  String? alertMessageContent;

  int pageIndex = 1;
  bool _isLoadingMore = false;

  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();

  final screenMargin = 15.0;
  final bodyPadding = 20.0;
  final noMessagesMargin = 20.0;
  final noMessagesIconWidth = 87.0;
  final avatarIconSize = 14.0;
  final avatarWidth = 60.0;
  final avatarHeight = 85.0;
  final avatarRadius = 30.0;
  final flagWidth = 20.0;
  final flagHeight = 16.0;
  final verticalPadding = 7.0;
  final horizontalPadding = 5.0;
  final conversationBoxRadius = 10.0;
  final iconBorderWidth = 2.0;
  final sectionWidthPadding = 8.0;
  final unreadMessagesDiameter = 25.0;

  final _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    textProvider = context.read<DependencyManager>().text;
    iconsProvider = context.read<DependencyManager>().icons;
    localizationProvider = context.read<DependencyManager>().localization;

    authenticationProvider = context.read<AuthenticationProvider>();
    _loadData();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      checkNotification();
    });
    super.initState();
  }

  Future<void> checkNotification() async {
    final notificationEnabled = await StaticVar.context
        .read<CollectDataProvider>()
        .getNotificationsAllowed();
    if (notificationEnabled.inverted) {
      await checkNotificationsPermeation(
        context: StaticVar.context,
        accept: () async {
          getIt<Storage>().shownNotification = false;
          final collectProvider = StaticVar.context.read<CollectDataProvider>();
          collectProvider.addUser();
          Navigator.of(StaticVar.context).pop();
          await FireBaseMessagingImpl.fireBaseMessagingImpl
              .requestPermissions();
        },
        skip: () async {
          getIt<Storage>().shownNotification = false;
          final collectProvider = StaticVar.context.read<CollectDataProvider>();
          collectProvider.addUser();
          Navigator.pop(StaticVar.context);
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    _initializeData();
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: ThemeColors.white,
      body: Container(
        width: MediaQuery.of(context).size.width,
        child: Column(
          children: [
            const Divider(height: 1),
            buildBody(),
            SizedBox(height: bodyPadding),
            _isLoadingMore ? ProgressIndicators.loadingIndicator() : SizedBox(),
            SizedBox(height: _isLoadingMore ? bodyPadding : 0),
          ],
        ),
      ),
    );
  }

  Widget buildBody() {
    if (messages == null) return buildLoading();
    if ((messages?.conversations ?? []).isEmpty) return buildNoMessages();
    return buildConversations();
  }

  Widget buildLoading() {
    return Expanded(
      child: ProgressIndicators.loadingIndicator(),
    );
  }

  Widget buildNoMessages() {
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          iconsProvider.parametrizedIcon(iconsProvider.send,
              width: noMessagesIconWidth, fit: BoxFit.contain),
          SizedBox(height: noMessagesMargin),
          textProvider.buildTitle1(noMessagesTitle,
              weight: FontWeights.semiBold),
        ],
      ),
    );
  }

  Widget buildConversations() {
    return Expanded(
      child: NotificationListener<ScrollNotification>(
        onNotification: _scrollNotify,
        child: RefreshIndicator(
          key: _refreshIndicatorKey,
          onRefresh: _loadData,
          child: ListView.builder(
            itemCount: (messages?.conversations ?? []).length,
            itemBuilder: (BuildContext context, int index) {
              return buildConversationTile(messages?.conversations?[index]);
            },
          ),
        ),
      ),
    );
  }

  Widget buildConversationTile(Conversation? conversation) {
    if (conversation == null) {
      return const SizedBox.shrink();
    }
    return Column(
      children: [
        InkWell(
          onTap: () => _openConversation(conversation),
          child: Ink(
            child: Container(
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsetsDirectional.only(
                  start: 16,
                  end: 8,
                  top: 20,
                  bottom: 13,
                ),
                color: (conversation.unread ?? 0) == 0
                    ? ThemeColors.white
                    : ThemeColors.colorFAFAFA,
                child: Visibility(
                  visible: getIt<Storage>().role == Roles.STUDENT ||
                      getIt<Storage>().role == Roles.PARENT,
                  replacement: BuildConversationInstructorWidget(
                    conversation: conversation,
                    iconsProvider: iconsProvider,
                    localizationProvider: localizationProvider,
                    textProvider: textProvider,
                  ),
                  child: BuildConversationStudentWidget(
                    conversation: conversation,
                    iconsProvider: iconsProvider,
                    localizationProvider: localizationProvider,
                    textProvider: textProvider,
                  ),
                )),
          ),
        ),
        const Divider(
          thickness: 1,
          height: 0,
          color: ThemeColors.colorF2F2F7,
        ),
      ],
    );
  }

  Alignment buildFittedBoxAlignment() {
    if (localizationProvider.locals.language == LanguagesKeys.ARABIC) {
      return Alignment.centerRight;
    }
    return Alignment.centerLeft;
  }

  Future<void> _openConversation(Conversation conversation) async {
    /// start loading and process
    messagesProvider.conversation = conversation;
    if (conversation.userId != null) {
      final argument = ConversationArgument(
        conversationType: ConversationType.messages,
        callConversationMessages: false,
        userId: conversation.userId ?? '',
        userName: conversation.userName ?? '',
        userNameAr: conversation.userNameAr ?? '',
      );
      NavigationService.instance
          .navigateToIfNotCurrent(ConversationScreen.routeName, args: argument)
          .then((value) {
        messagesProvider.getUserChat(
          authenticationProvider.accessToken,
          pageIndex,
        );
      });
    }
  }

  bool _scrollNotify(ScrollNotification scrollInfo) {
    if (!_isLoadingMore &&
        scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent &&
        (messages?.conversations ?? []).length < messages!.totalCount!) {
      setState(() {
        _isLoadingMore = true;
      });
      _loadMore();
    }
    return true;
  }

  Future<void> _loadMore() async {
    if ((messages?.conversations ?? []).length < messages!.totalCount!) {
      pageIndex++;
      await messagesProvider.getUserChat(
          authenticationProvider.accessToken, pageIndex);
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  Future<void> _loadData() async {
    pageIndex = 1;
    final accessToken = context.read<AuthenticationProvider>().accessToken;
    await context.read<MessagesProvider>().getUserChat(accessToken, pageIndex);
  }

  void _initializeData() {
    messagesProvider = context.watch<MessagesProvider>();
    messages = context.watch<MessagesProvider>().messages;

    noMessagesTitle =
        localizationProvider.resources.getWithKey(MESSAGING_NOMESSAGES);

    if (StaticVar.isStudent) {
      alertMessageContent = localizationProvider.resources
          .getWithKey(MESSAGING_HEADERALERTSTUDENT);
    } else if (getIt<Storage>().role == Roles.INSTRUCTOR ||
        getIt<Storage>().role == Roles.REFERRAL) {
      alertMessageContent = localizationProvider.resources
          .getWithKey(MESSAGING_HEADERALERTINSTRUCTOR);
    }
  }
}
