import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modarby/Models/InstructorProfile/Instructor.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/SearchProvider.dart';
import 'package:modarby/Providers/StudentProfileProvider.dart';
import 'package:modarby/Widgets/custom_bottom_sheet.dart';
import 'package:modarby/Widgets/custom_button_widget.dart';
import 'package:modarby/Widgets/custom_text_widget.dart';
import 'package:modarby/Widgets/sticky_bottom_appbar.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/extentions/iterables/iterable_first.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';
import 'package:modarby/core/utilities/shard.dart';
import 'package:modarby/features/booking_details/widgets/multi_lessons_content.dart';
import 'package:modarby/features/selected_fee/widgets/custom_app_bar_widget.dart';
import 'package:modarby/features/selected_fee/widgets/total_tax_with_vat_widget.dart';
import 'package:provider/provider.dart';

import '../../../UIDTOs/Booking/InstructorBooking.dart';
import '../../../UIDTOs/Booking/LessonData.dart';

class BookDetailsScreen extends StatefulWidget {
  static const routeName = '/StudentBookDetails';

  final bool isReschedule;
  final String? bookingGuid;
  final LessonData? lesson;
  final bool isBookingFromPackage;
  final bool isInstructorBooking;
  final InstructorBookingArgs? instructorBookingArgs;
  final double? packageRemainingTime;
  final String? packageGuid;
  final bool showAppBar;
  const BookDetailsScreen({
    super.key,
    this.bookingGuid,
    this.packageRemainingTime,
    this.packageGuid,
    this.lesson,
    this.instructorBookingArgs,
    this.isReschedule = false,
    this.isBookingFromPackage = false,
    this.isInstructorBooking = false,
    this.showAppBar = true,
  });

  @override
  BookDetailsScreenState createState() => BookDetailsScreenState();
}

class BookDetailsScreenState extends State<BookDetailsScreen> {
  late LocalizationProvider _localizationProvider;
  late StudentProfileProvider _studentProfileProvider;
  Instructor? _instructor;
  bool isTotalCostExpand = false;
  final GlobalKey<MultiLessonsContentState> secondWidgetKey = GlobalKey();
  late IconsProvider _iconsProvider;

  late String? _currencyName;
  List<LessonData?> lessons = [];
  @override
  void initState() {
    _initializeData();
    _loadProfileStudent();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Scaffold(
            backgroundColor: ThemeColors.white,
            body: _body(),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.startFloat,
            floatingActionButton: _floatActionButton(),
            bottomNavigationBar: _bottomNavigationWidget(),
          ),
        ),
      ],
    );
  }

  Widget _body() {
    return Container(
      height: double.infinity,
      width: double.infinity,
      color: ThemeColors.white,
      child: SafeArea(
        child: Column(
          children: [
            if (widget.showAppBar) _appBar(),
            Expanded(
              child: ListView(
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                children: [
                  _buildContent(),
                  SizedBox(height: 30.sp),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  _appBar() {
    return CustomAppBarWidget(
      title: widget.isBookingFromPackage
          ? PACKAGES_BOOKDETAILS.translate()
          : widget.isReschedule
              ? BOOKING_RESCHEDULE_TITLE.translate()
              : bookingDetailsSingleLessons.translate(),
      valueProgress: 0.4,
    );
  }

  Widget _buildContent() {
    if (widget.isReschedule ||
        widget.isBookingFromPackage ||
        !(_instructor?.hasPackages ?? false)) {
      return MultiLessonsContent(
        lessons: lessons,
        key: secondWidgetKey,
        isReschedule: widget.isReschedule,
        bookingGuid: widget.bookingGuid,
        lesson: widget.lesson,
        isBookingFromPackage: widget.isBookingFromPackage,
        isInstructorBooking: widget.isInstructorBooking,
        instructorBookingArgs: widget.instructorBookingArgs,
        packageRemainingTime: widget.packageRemainingTime,
        packageGuid: widget.packageGuid,
        onChangedDuration: () {
          setState(() {});
          secondWidgetKey.currentState?.setState(() {});
        },
      );
    }
    return _buildViews();
  }

  Widget _buildViews() {
    return MultiLessonsContent(
      lessons: lessons,
      key: secondWidgetKey,
      onChangedDuration: () {
        setState(() {});
        secondWidgetKey.currentState?.setState(() {});
      },
    );
  }

  void _initializeData() {
    _localizationProvider = context.read<DependencyManager>().localization;
    _studentProfileProvider = context.read<StudentProfileProvider>();
    _iconsProvider = context.read<DependencyManager>().icons;
    _instructor = context.read<SearchProvider>().instructor;
    _currencyName = _localizationProvider.locals.currency?.name;
  }

  _bottomNavigationWidget() {
    if (widget.isReschedule) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          StickyBottomAppBar(
            child: CustomButtonWidget(
              colorButton: ThemeColors.color26467A,
              padding: EdgeInsetsDirectional.only(
                top: 14.sp,
                bottom: 14.sp,
              ),
              title: '${BOOKING_BUTTONS_NEXT.translate()}',
              titleColor: ThemeColors.white,
              onPressed: () {
                secondWidgetKey.currentState?.submit();
              },
            ),
          ),
        ],
      );
    } else if (widget.isBookingFromPackage &&
        widget.packageRemainingTime != null) {
      return bookingFromPackage();
    } else {
      return bookingButtonsLessonsBottomBar();
    }
  }

  void _onClickConfirm() {
    secondWidgetKey.currentState?.submit();
  }

  double calculatePriceLessons() {
    double total = 0;
    final cost = Shard().getPriceStudentWithSearchInstructor(_instructor);
    for (LessonData? item
        in secondWidgetKey.currentState?.widget.lessons ?? []) {
      String numericString = (item?.durationController?.text ?? 0)
          .toString()
          .replaceAll(RegExp(r'[^0-9]'), '');
      final duration = double.tryParse(numericString) ?? 0;
      total += (duration / 60) * cost;
    }
    return total;
  }

  double calculateDurationsLessons() {
    double durations = 0;
    for (LessonData? item
        in secondWidgetKey.currentState?.widget.lessons ?? []) {
      String numericString = (item?.durationController?.text ?? 0)
          .toString()
          .replaceAll(RegExp(r'[^0-9]'), '');
      final duration = double.tryParse((numericString));
      durations += duration ?? 60;
    }
    return durations;
  }

  double _calculateTaxPrice(double price) {
    final tax = (double.parse(
            _studentProfileProvider.student?.otherTax.toString() ?? "0")) /
        100;
    return price * tax;
  }

  Widget bookingButtonsLessonsBottomBar() {
    final price = calculatePriceLessons();
    final tax = _calculateTaxPrice(price);
    final priceWithVat = price + tax;
    return TotalTaxWithVatWidget(
      modeViewOnly:
          (lessons.firstOrNull?.durationController?.text.isEmpty ?? false),
      totalCostExpand: isTotalCostExpand,
      onClickConfirm: _onClickConfirm,
      currency: _currencyName,
      price: price.toStringAsFixed(2),
      timeOrHours:
          '${calculateDurationsLessons().toStringAsFixed(0)} ${minutes.translate()}',
      vat: tax.toStringAsFixed(2),
      priceWithVat: priceWithVat.toStringAsFixed(2),
      hideTotal: false,
    );
  }

  Widget bookingFromPackage() {
    var total = 0;
    for (var item in secondWidgetKey.currentState?.widget.lessons ?? []) {
      total += int.tryParse(
              item.durationController.text.split(' ').first.toString()) ??
          0;
    }
    final parent = StickyBottomAppBar(
      child: CustomButtonWidget(
        colorButton: ThemeColors.color26467A,
        margin: EdgeInsetsDirectional.only(
          start: 16.w,
          end: 16.w,
        ),
        padding: EdgeInsetsDirectional.only(
          top: 14.sp,
          bottom: 14.sp,
        ),
        title: widget.isReschedule
            ? '${BOOKING_BUTTONS_NEXT.translate()}'
            : '${PACKAGES_BUTTONS_PURCHASE.translate()}',
        titleColor: ThemeColors.white,
        onPressed: () {
          secondWidgetKey.currentState?.submit();
        },
      ),
    );
    final child = Container(
      padding: EdgeInsetsDirectional.only(
        start: 16.w,
        end: 16.w,
        bottom: 12.sp,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CustomTextWidget(
            title: totalDetectPackageTitle.translate(),
            fontWeight: FontWeight.w600,
            size: 13,
            color: ThemeColors.color1C1C1E,
          ),
          CustomTextWidget(
            title: '$total ${minutes.translate()}',
            fontWeight: FontWeight.w600,
            size: 13,
            color: ThemeColors.color1C1C1E,
          ),
        ],
      ),
    );
    return Visibility(
        visible:
            (lessons.firstOrNull?.durationController?.text.isEmpty ?? false),
        replacement:
            CustomBottomSheet(parentWidget: child, childWidget: parent),
        child: _modeViewOnlyWidget());
  }

  void _loadProfileStudent() {
    final authProvider = context.read<AuthenticationProvider>();
    final languageId = _localizationProvider.locals.languageId;
    context
        .read<StudentProfileProvider>()
        .getProfile(authProvider.accessToken, languageId);
  }

  Widget _modeViewOnlyWidget() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        StickyBottomAppBar(
            child: CustomButtonWidget(
          onPressed: () {},
          title: PACKAGES_BUTTONS_PURCHASE.translate(),
          padding: EdgeInsetsDirectional.only(
            top: 14.sp,
            bottom: 14.sp,
          ),
          titleColor: ThemeColors.white,
          // colorButton: ThemeColors.accentColor.withOpacity(0.50),
          colorButton: ThemeColors.color26467A.withOpacity(0.50),
        )),
      ],
    );
  }

  Widget _floatActionButton() {
    final checkAllLessonsNotCompleted = lessons
        .any((element) => (element?.durationController?.text ?? '').isEmpty);
    if (widget.isReschedule ||
        (context.read<StudentProfileProvider>().student?.isNew ?? false) ||
        checkAllLessonsNotCompleted) {
      return SizedBox(
        width: 46.w,
      );
    }
    return Container(
      decoration: BoxDecoration(
        color: ThemeColors.grayE5E5EA,
        borderRadius: BorderRadius.circular(40.sp),
      ),
      child: Material(
        color: ThemeColors.transparent,
        child: InkWell(
          onTap: _addLesson,
          borderRadius: BorderRadius.circular(40.sp),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(width: 17.5.sp),
              _iconsProvider.parametrizedIcon(
                _iconsProvider.plus,
                color: ThemeColors.color1C1C1E,
                width: 18.w,
                height: 18.w,
              ),
              SizedBox(width: 10.sp),
              CustomTextWidget(
                title: BOOKING_BUTTONS_ADDANOTHERLESSON.translate(),
                color: ThemeColors.color1C1C1E,
                size: 13,
                paddingTop: 9.5.sp,
                paddingBottom: 9.5.sp,
                paddingEnd: 17.5.sp,
                fontWeight: FontWeight.w600,
              )
            ],
          ),
        ),
      ),
    );
  }

  void _addLesson() {
    final checkAllLessonsNotCompleted = lessons
        .any((element) => (element?.durationController?.text ?? '').isEmpty);
    if (checkAllLessonsNotCompleted.inverted) {
      for (var lesson in lessons) {
        lesson?.isExpand = false;
      }
      lessons.add(LessonData());
      lessons.last?.isExpand = true;
      setState(() {});
    }
  }
}
