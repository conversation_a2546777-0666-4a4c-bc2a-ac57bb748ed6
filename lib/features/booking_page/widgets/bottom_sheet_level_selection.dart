import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modarby/Models/Content/TraineeTypes.dart';
import 'package:modarby/Widgets/custom_button_widget.dart';
import 'package:modarby/Widgets/custom_text_widget.dart';
import 'package:modarby/Widgets/image_loading.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/utilities/Snackbars.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';

class BottomSheetLevelSelection extends StatefulWidget {
  const BottomSheetLevelSelection({
    Key? key,
    this.types,
    this.onClickConfirm,
  }) : super(key: key);
  final List<TraineeTypes>? types;
  final Function(TraineeTypes? selection)? onClickConfirm;
  @override
  State<BottomSheetLevelSelection> createState() =>
      _BottomSheetLevelSelectionState();
}

class _BottomSheetLevelSelectionState extends State<BottomSheetLevelSelection> {
  TraineeTypes? selectionLevel;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10),
          topRight: Radius.circular(10),
        ),
        color: ThemeColors.white,
      ),
      child: SafeArea(
        child: Container(
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10),
              topRight: Radius.circular(10),
            ),
            color: ThemeColors.white,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 30.sp,
              ),
              CustomTextWidget(
                title: titleSelectLevelBottomSheet.translate(),
                color: ThemeColors.black,
                textAlign: TextAlign.center,
                paddingEnd: 38.w,
                paddingStart: 38.w,
                fontWeight: FontWeight.w700,
                size: 20,
              ),
              SizedBox(height: 13.5.sp),
              CustomTextWidget(
                title: descriptionSelectLevelBottomSheet.translate(),
                color: ThemeColors.color636366,
                textAlign: TextAlign.center,
                paddingEnd: 38.w,
                paddingStart: 38.w,
                fontWeight: FontWeight.w400,
                size: 15,
              ),
              SizedBox(height: 13.5.sp),
              Expanded(
                child: GridView.builder(
                  physics: NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  padding: EdgeInsetsDirectional.only(
                    start: 16.w,
                    end: 16.w,
                  ),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    mainAxisSpacing: 8.w,
                    crossAxisSpacing: 11.sp,
                    childAspectRatio: 0.921,
                  ),
                  itemCount: (widget.types ?? []).length,
                  itemBuilder: _itemLevelSelectionBuilder,
                ),
              ),
              SizedBox(height: 16.sp),
              _confirmButton(),
              SizedBox(
                height: 16.sp,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget? _itemLevelSelectionBuilder(BuildContext context, int index) {
    final item = widget.types?[index];
    return GestureDetector(
      onTap: () {
        selectionLevel = item;
        setState(() {});
      },
      child: Container(
        decoration: BoxDecoration(
            color: selectionLevel == item
                ? ThemeColors.color26467A.withOpacity(0.06)
                : ThemeColors.white,
            borderRadius: BorderRadius.circular(10),
            boxShadow: const [
              BoxShadow(
                color: Color.fromRGBO(0, 0, 0, 0.06),
                blurRadius: 10.0,
                spreadRadius: 0.0,
                offset: Offset(0, 0),
              ),
            ],
            border: selectionLevel == item
                ? Border.all(color: ThemeColors.color26467A)
                : null),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            ImageLoading(
              imageUrl: item?.image ?? '',
              width: 59.w,
              height: 67.sp,
              colorWidget: ThemeColors.color26467A,
            ),
            SizedBox(height: 11.sp),
            CustomTextWidget(
              title: item?.name ?? '',
              size: 12,
              fontWeight: FontWeight.w600,
              color: ThemeColors.color1C1C1E,
            )
          ],
        ),
      ),
    );
  }

  _confirmButton() {
    return Padding(
      padding: EdgeInsetsDirectional.only(
        start: 16.w,
        end: 16.w,
      ),
      child: CustomButtonWidget(
        padding: EdgeInsetsDirectional.only(
          top: 10.sp,
          bottom: 10.sp,
        ),
        onPressed: _onClickConfirm,
        title: confirmTitle.translate(),
        sizeTitle: 17,
        fontWeightTitle: FontWeight.w600,
        enabled: selectionLevel != null,
        titleColor: ThemeColors.white,
        colorButton: ThemeColors.color26467A,
      ),
    );
  }

  void _onClickConfirm() {
    if (selectionLevel != null) {
      widget.onClickConfirm?.call(selectionLevel);
    } else {
      Snackbars.dangerBottom(context, ERROR_SUBJECTS_SELECTLEVEL.translate());
    }
  }
}
