import 'dart:async';
import 'dart:developer';

import 'package:firebase_database/firebase_database.dart';
import 'package:modarby/Providers/CommonProvider.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:provider/provider.dart';

class CallCleanupService {
  // Check and delete old calls for a specific receiver

  // Get server time once to calculate offset
  Future<int> getServerTimeOffset() async {
    try {
      DatabaseReference offsetRef =
          FirebaseDatabase.instance.ref('.info/serverTimeOffset');
      DatabaseEvent event = await offsetRef.once();
      final value = event.snapshot.value;
      if (value != null) {
        if (value is int?) {
          return (value as int?) ?? 0;
        } else if (value is double?) {
          return ((value as double?) ?? 0).toInt();
        } else if (value is double) {
          return (value).toInt();
        } else {
          return 0;
        }
      } else {
        return 0;
      }
    } catch (e) {
      print('Error getting server offset: $e');
      return 0;
    }
  }

  // Get current server time
  Future<int> getCurrentServerTime() async {
    int offset = await getServerTimeOffset();
    return DateTime.now().millisecondsSinceEpoch + offset;
  }

  Future<void> checkAndDeleteOldCallsByReceiver(String receiverId) async {
    if (receiverId.isEmpty) return;

    final DatabaseReference callsRef =
        FirebaseDatabase.instance.ref().child('calls');
    try {
      log('once:start:checkAndDeleteOldCallsByReceiver:$receiverId');
      // Query calls for specific receiver
      final Query receiverCalls =
          callsRef.orderByChild('receiverId').equalTo(receiverId);

      try {
        final snapshot = await receiverCalls.once();
        await _checkDataAndClean(snapshot.snapshot, callsRef);
      } catch (e) {
        log('Error with once() query: $e');
        // Fallback to get() if once() fails
        try {
          final snapshot = await receiverCalls.get();
          await _checkDataAndClean(snapshot, callsRef);
        } catch (e) {
          log('Error with get() query: $e');
        }
      }
    } catch (e) {
      log('Error in checkAndDeleteOldCallsByReceiver: $e');
    }
  }

  // Start periodic cleanup for a specific receiver
  StreamSubscription? _cleanupSubscription;
  void startPeriodicCleanupForReceiver(String? receiverId,
      {Duration checkInterval = const Duration(seconds: 45)}) {
    try {
      // Cancel existing subscription if any
      _cleanupSubscription?.cancel();

      if (receiverId != null && receiverId.isNotEmpty) {
        _cleanupSubscription = Stream.periodic(checkInterval).listen((_) {
          checkAndDeleteOldCallsByReceiver(receiverId);
        });
      }
    } catch (e) {
      log('Error starting periodic cleanup: $e');
    }
  }

  void dispose() {
    _cleanupSubscription?.cancel();
    _cleanupSubscription = null;
  }

  Future<void> _checkDataAndClean(
      DataSnapshot snapshot, DatabaseReference callsRef) async {
    try {
      if (snapshot.value == null) return;

      final timeCall =
          (StaticVar.context.read<CommonProvider>().voiceCallsDuration ?? 0);
      final timeRingCall =
          (StaticVar.context.read<CommonProvider>().voiceCallsRingDuration ??
              0);

      final Map<dynamic, dynamic> calls = snapshot.value as Map;
      // Check each call for the receiver
      for (var entry in calls.entries) {
        try {
          final call = entry.value;
          final key = entry.key;
          if (call['timestamp'] != null) {
            int serverTime = await getCurrentServerTime();
            int initiatedAt = call['timestamp'] as int;
            final isCallActive = call['status'] == 'active';
            int secondsElapsed = ((serverTime - initiatedAt) ~/ 1000);

            if (isCallActive && (secondsElapsed > timeCall)) {
              log('Cleaning active call: $key');
              await callsRef.child(key).remove();
            } else if (secondsElapsed > timeRingCall && !isCallActive) {
              log('Cleaning inactive call: $key');
              await callsRef.child(key).remove();
            }
          }
        } catch (e) {
          log('Error cleaning individual call: $e');
          continue; // Continue with next call even if one fails
        }
      }
    } catch (e) {
      log('Error in _checkDataAndClean: $e');
    }
  }
}
