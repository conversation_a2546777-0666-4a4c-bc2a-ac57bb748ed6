import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_callkit_incoming/entities/entities.dart';
import 'package:flutter_callkit_incoming/flutter_callkit_incoming.dart';
import 'package:modarby/Enums/AttachmentType.dart';
import 'package:modarby/Enums/Roles.dart';
import 'package:modarby/Enums/call_status_enum.dart';
import 'package:modarby/Models/Common/CallResponse.dart';
import 'package:modarby/Models/Messages/ConversationModel.dart';
import 'package:modarby/Models/Messages/MessageUserInfoModel.dart';
import 'package:modarby/Models/calls/call_model.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/CommonProvider.dart';
import 'package:modarby/Providers/MessagesProvider.dart';
import 'package:modarby/Providers/internet_checker_provider.dart';
import 'package:modarby/Services/voice_call/VoiceCallRepository.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/forground_service/forground_service_impl.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/page_loading_dialog/page_loading_dialog.dart';
import 'package:modarby/core/utilities/Snackbars.dart';
import 'package:modarby/core/utilities/cool_down_handler.dart';
import 'package:modarby/core/utilities/error_parser.dart';
import 'package:modarby/core/utilities/language/LanguagesKeys.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';
import 'package:modarby/core/utilities/navigation_service.dart';
import 'package:modarby/core/utilities/permition_handler.dart';
import 'package:modarby/core/utilities/shard.dart';
import 'package:modarby/core/utilities/storage.dart';
import 'package:modarby/env/env.dart';
import 'package:modarby/features/messages/domain/enum/conversation_place.dart';
import 'package:modarby/features/messages/presentation/arguments/conversation_argument.dart';
import 'package:modarby/features/messages/presentation/views/conversation_screen.dart';
import 'package:modarby/features/voice_call/data/call-cleanup.dart';
import 'package:modarby/features/voice_call/views/CallPageArguments.dart';
import 'package:modarby/features/voice_call/views/animation_fap.dart';
import 'package:modarby/features/voice_call/views/call_page.dart';
import 'package:modarby/features/voice_call/views/confirmation_call_dialog.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

class CallProvider with ChangeNotifier {
  late VoiceCallRepository _repository;
  CallProvider() {
    _repository = VoiceCallRepository();
  }
  String currentUserId = '';
  late final RtcEngine _engine;
  CallStatus statusCall = CallStatus.nothing;
  Timer? _timer;
  Timer? _callTimerNoAnswer;
  final GlobalKey<MovableFABState> fabKey = GlobalKey<MovableFABState>();

  MessageUserInfoModel? messageUserInfoModel;
  String callKitId = '';
  CallPageArguments? callArgument;
  bool isJoined = false;
  bool openMicrophone = true;
  bool enableSpeakerphone = false;
  double _inEarMonitoringVolume = 100;
  final ChannelProfileType _channelProfileType =
      ChannelProfileType.channelProfileCommunication;
  late final RtcEngineEventHandler _rtcEngineEventHandler;

  int _secondsRemaining = 0;

  bool isAlertCallWillEndPlaying = false;
  bool isAlertInAnotherCallPlaying = false;

  bool isInitialized = false;
  StudentAdditionalInfo? studentAdditionalInfo;

  bool callAccepted = false; // Step 1: Add this boolean variable
  bool get isSender => callArgument?.isSender ?? false;
  int isAllowedReceiverToCallMe = 2;
  int isAllowedMeToCall = 2;
  int langIdReceiver = 1;

  Future<void> initEngine() async {
    if (!isInitialized) {
      isInitialized = true;
      _engine = createAgoraRtcEngine();
      await _engine.initialize(const RtcEngineContext(appId: Env.agoraAppId));
      _rtcEngineEventHandler = RtcEngineEventHandler(
        onError: (err, msg) {
          log('[onError] err: $err, msg: $msg');
        },
        onJoinChannelSuccess: (connection, elapsed) {
          log('[onJoinChannelSuccess] connection: ${connection.toJson()} elapsed: $elapsed');
          isJoined = true;
          notifyListeners();
        },
        onUserJoined: (RtcConnection connection, int uid, int elapsed) async {
          debugPrint("remote user $uid joined");
          saveDataCallInShardPreferance();
          callAccepted = true;
          _engine.enableAudio();
          _engine.enableLocalAudio(openMicrophone);
          startTimer();
          _callTimerNoAnswer?.cancel();
          updateStatus(CallStatus.active);
          setCallConnected();
          stopSound();
          notifyListeners();
          if (isSender) {
            _startRecord();
          }
          _updateStatusFirebase(callArgument?.channelName);
        },
        onUserOffline: (RtcConnection connection, int uid,
            UserOfflineReasonType reason) async {
          debugPrint("remote user left channel");
          stopSound();
          checkIfCallActive(callArgument?.channelName);
        },
        onLeaveChannel: (connection, stats) {
          log('[onLeaveChannel] connection: ${connection.toJson()} stats: ${stats.toJson()}');
          isJoined = false;
          callAccepted = false;
          stopSound();
          notifyListeners();
        },
      );

      _engine.registerEventHandler(_rtcEngineEventHandler);
      await _engine.enableAudio();
      await _engine.setClientRole(role: ClientRoleType.clientRoleBroadcaster);
    }
  }

  StreamSubscription<DatabaseEvent>? listener;
  void _cancelListenerFirebase() {
    try {
      if (listener != null) {
        listener?.cancel();
        listener = null;
      }
    } catch (e) {
      log('Error canceling Firebase listener: $e');
      // Reset listener even if there's an error
      listener = null;
    }
  }

  void listenerCallIfCancel(String channelName) {
    try {
      _cancelListenerFirebase();
      if (channelName.isEmpty) return;

      final DatabaseReference callsRef = FirebaseDatabase.instance.ref();
      listener = callsRef.child('calls/$channelName').onValue.listen(
        (DatabaseEvent event) {
          try {
            final data = event.snapshot.value as Map<dynamic, dynamic>?;
            if (data != null &&
                data['status'] == 'canceled' &&
                statusCall != CallStatus.noAnswer) {
              _cancelListenerFirebase();
              endCall(
                withEnd: true,
                leaveWithDelete: false,
              );
              if (NavigationService.instance.isCurrent(CallPage.routeName)) {
                NavigationService.instance.goBack();
              }
            } else if (data != null &&
                data['status'] == CallStatus.noAnswer.name) {
              _cancelListenerFirebase();
              if (statusCall == CallStatus.incoming) {
                removeIncomingCall();
              } else {
                endCall(
                  withEnd: true,
                  leaveWithDelete: false,
                  status: CallStatus.noAnswer,
                );
              }
            }
          } catch (e) {
            log('Error processing Firebase event: $e');
          }
        },
        onError: (error) {
          log('Firebase listener error: $error');
          _cancelListenerFirebase();
        },
      );
    } catch (e) {
      log('Error setting up Firebase listener: $e');
    }
  }

  Future<bool> checkIfCallExists(String? channelName) async {
    if (channelName == null || channelName.isEmpty) {
      return false;
    }
    try {
      log('once : start:checkIfCallExists:$channelName');
      final snapShot = await FirebaseDatabase.instance
          .ref()
          .child('calls')
          .child(channelName)
          .once();
      return _checkDataCallIfExists(snapShot.snapshot);
    } catch (e) {
      log('Error in checkAndDeleteOldCallsByReceiver: $e');
      try {
        log('get : start:checkIfCallExists:$channelName');
        final snapShot = await FirebaseDatabase.instance
            .ref()
            .child('calls')
            .child(channelName)
            .get();
        return _checkDataCallIfExists(snapShot);
      } catch (e) {
        log('Error in checkAndDeleteOldCallsByReceiver: $e');
        return false;
      }
    }
  }

  // Get server time once to calculate offset
  Future<int> getServerTimeOffset() async {
    try {
      DatabaseReference offsetRef =
          FirebaseDatabase.instance.ref('.info/serverTimeOffset');
      DatabaseEvent event = await offsetRef.once();
      final value = event.snapshot.value;
      if (value != null) {
        if (value is int?) {
          return (value as int?) ?? 0;
        } else if (value is double?) {
          return ((value as double?) ?? 0).toInt();
        } else if (value is double) {
          return (value).toInt();
        } else {
          return 0;
        }
      } else {
        return 0;
      }
    } catch (e) {
      print('Error getting server offset: $e');
      return 0;
    }
  }

  // Get current server time
  Future<int> getCurrentServerTime() async {
    int offset = await getServerTimeOffset();
    return DateTime.now().millisecondsSinceEpoch + offset;
  }

  Future<bool> checkIfCallActive(String? channelName) async {
    if (channelName == null || channelName.isEmpty) {
      return false;
    }
    await Future.delayed(const Duration(seconds: 1));
    try {
      DatabaseEvent event = await FirebaseDatabase.instance
          .ref()
          .child('calls')
          .child(channelName)
          .once();

      if (event.snapshot.exists &&
          event.snapshot.value != null &&
          event.snapshot.value is Map<dynamic, dynamic>) {
        final map = Map<String, dynamic>.from(
            event.snapshot.value as Map<dynamic, dynamic>);
        if (map['status'] == 'active') {
          endCall(
            withEnd: true,
            sendChatStatus: callAccepted,
          );
          if (NavigationService.instance.isCurrent(CallPage.routeName)) {
            NavigationService.instance.goBack();
          }
        }
      }
      // If the data exists and is not null, return true
      return event.snapshot.exists && event.snapshot.value != null;
    } catch (e) {
      print('Error checking call existence: $e');
      return false;
    }
  }

  final cool = CoolDownHandler();
  Future<void> acceptCall(CallPageArguments? args) async {
    cool.executeWithCooldown(() => acceptCallOnce(args), Duration(seconds: 6));
  }

  Future<void> acceptCallOnce(CallPageArguments? args) async {
    saveCallKitId();
    if ((args?.channelName ?? '').isEmpty) return;
    final isCallStillExists = await checkIfCallExists(args?.channelName ?? '');
    if (isCallStillExists) {
      log('call exists');
      final statusMic = await PermissionWrapper.checkPermission(
        submitColor: ThemeColors.color26467A,
        context: StaticVar.context,
        permission: Permission.microphone,
        title: titleMicCallPermission.translate().orDefault,
        body: bodyMicCallPermission.translate().orDefault,
        openSettingsText: openSetting.translate().orDefault,
      );
      log('call exists mic $statusMic');

      if (statusMic.inverted) {
        return;
      }

      if (callAccepted) {
        endCall(withEnd: true);
        return;
      }
      callArgument = args;
      setCallConnected();
      try {
        await _engine.joinChannel(
          token: args?.agoraToken ?? '',
          channelId: args?.channelName ?? '',
          uid: ((args?.userID ?? 0)).toInt(),
          options: ChannelMediaOptions(
            // Automatically subscribe to all audio streams
            autoSubscribeAudio: true,
            // Publish microphone audio
            publishMicrophoneTrack: true,
            channelProfile: _channelProfileType,
            clientRoleType: ClientRoleType.clientRoleBroadcaster,
          ),
        );
        NavigationService.instance
            .navigateToIfNotCurrent(CallPage.routeName, args: args);
        saveCallKitId();
      } catch (e, _) {
        endCall(
          withEnd: true,
        );
        Snackbars.dangerBottom(
            StaticVar.context, callNotAvailableRightNow.translate().orDefault);
        log('call cant join ${e.toString()}');
        log('call cant join ${_.toString()}');
      }
    } else {
      if (callKitId.isNotEmpty) {
        await FlutterCallkitIncoming.endCall(callKitId);
      } else {
        final call = await getCurrentCall();
        if (call != null && call['id'] != null) {
          await FlutterCallkitIncoming.endCall(call['id']);
        }
      }
      updateStatus(CallStatus.nothing);
      _reset();
      if (NavigationService.instance.isCurrent(CallPage.routeName)) {
        NavigationService.instance.goBack();
      }
      Snackbars.dangerBottom(
          StaticVar.context, callNotAvailableRightNow.translate().orDefault);
    }
  }

  Future<void> joinChannel({
    required CallPageArguments? callPageArguments,
    String? userId,
  }) async {
    if (callPageArguments?.channelName == null) return;
    log('joinChannel user ${callPageArguments?.channelName}');
    try {
      await _engine.joinChannel(
        token: callPageArguments?.agoraToken ?? '',
        channelId: callPageArguments?.channelName ?? '',
        uid: callPageArguments?.userID ?? 0,
        options: ChannelMediaOptions(
          channelProfile: _channelProfileType,
          clientRoleType: ClientRoleType.clientRoleBroadcaster,
        ),
      );
    } catch (e, _) {
      endCall(
        withEnd: true,
      );
      Snackbars.dangerBottom(
          StaticVar.context, callNotAvailableRightNow.translate().orDefault);
      log(e.toString());
      log(_.toString());
    }
  }

  Future<void> endCall({
    bool withEnd = true,
    CallStatus status = CallStatus.ended,
    bool leaveWithDelete = true,
    bool sendChatStatus = false,
  }) async {
    if (statusCall == CallStatus.ended ||
        statusCall == CallStatus.noAnswer ||
        statusCall == CallStatus.nothing) return;
    log('leaveChannel user ${callArgument?.channelName ?? ''}');
    _cancelListenerFirebase();
    updateStatus(status);
    final timeCall = _secondsElapsed > 0 ? _formatTime(_secondsElapsed) : '';
    await _engine.leaveChannel();
    isJoined = false;
    callAccepted = false;
    stopSound();
    if (leaveWithDelete) {
      deleteCallFirebase(
        callArgument?.channelName ?? '',
        status: status == CallStatus.noAnswer
            ? CallStatus.noAnswer.name
            : 'canceled',
      );
    }
    _stopRecord();
    ForegroundServiceImpl().stopBackgroundService();
    stopTimer();
    _stopAlertWillEndSound();
    if (withEnd) {
      if (callKitId.isNotEmpty) {
        await FlutterCallkitIncoming.endCall(callKitId);
      } else {
        final call = await getCurrentCall();
        if (call != null && call['id'] != null) {
          await FlutterCallkitIncoming.endCall(call['id']);
        }
      }
    }
    if (statusCall != CallStatus.noAnswer) {
      _reset();
    }
    if (statusCall == CallStatus.noAnswer && isSender.inverted) {
      _reset();
    }
    if (sendChatStatus) {
      if (timeCall.isNotEmpty) {
        _sendMessageStatusCallChat(AttachmentType.callFinished,
            '${titleMessageFinishedCall.translateLang(callArgument?.receiverLanguageId == 1 ? 'ar' : 'en').orDefault} $timeCall');
      } else if (isSender.inverted) {
        _sendMessageStatusCallChat(
            AttachmentType.callBusy,
            titleMessageNoAnswer
                .translateLang(
                    callArgument?.receiverLanguageId == 1 ? 'ar' : 'en')
                .orDefault);
      } else {
        _sendMessageStatusCallChat(
            AttachmentType.callNoAnswer,
            titleMessageMissedCall
                .translateLang(
                    callArgument?.receiverLanguageId == 1 ? 'ar' : 'en')
                .orDefault);
      }
    }
  }

  void startCallTimerNoAnswer({bool withEnd = false}) {
    final timeNoAnswer =
        StaticVar.context.read<CommonProvider>().voiceCallsRingDuration ?? 0;
    _callTimerNoAnswer = Timer(Duration(seconds: timeNoAnswer), () {
      if (withEnd) {
        _callTimerNoAnswer?.cancel();
        _callTimerNoAnswer = null;
        endCall(
            withEnd: true, status: CallStatus.noAnswer, sendChatStatus: true);
        updateStatus(CallStatus.noAnswer);
      } else {
        if (callAccepted.inverted) {
          if (statusCall == CallStatus.incoming) {
            _callTimerNoAnswer?.cancel();
            _callTimerNoAnswer = null;
            _reset();
          }
        }
        notifyListeners();
      }
    });
  }

  Future<void> switchMicrophone() async {
    await _engine.enableLocalAudio(!openMicrophone);
    openMicrophone = !openMicrophone;
    final call = await getCurrentCall();
    if (call != null && call['id'] != null) {
      await FlutterCallkitIncoming.muteCall(call['id'].toString(),
          isMuted: !openMicrophone);
    }
    notifyListeners();
  }

  Future<void> switchSpeakerphone() async {
    await _engine.setEnableSpeakerphone(!enableSpeakerphone);
    enableSpeakerphone = !enableSpeakerphone;
    notifyListeners();
  }

  Future<void> setInEarMonitoringVolume(double value) async {
    _inEarMonitoringVolume = value;
    await _engine.setInEarMonitoringVolume(_inEarMonitoringVolume.toInt());
    notifyListeners();
  }

  @override
  void dispose() {
    cool.dispose();
    disposeProvider();
    super.dispose();
  }

  Future<bool> isReceiverInAnyCall(String receiverId) async {
    try {
      Map<dynamic, dynamic> calls = {};
      DatabaseEvent eventReceiver = await FirebaseDatabase.instance
          .ref()
          .child('calls')
          .orderByChild('receiverId')
          .equalTo(receiverId)
          .once();

      DatabaseEvent eventSender = await FirebaseDatabase.instance
          .ref()
          .child('calls')
          .orderByChild('senderId')
          .equalTo(receiverId)
          .once();

      if (eventReceiver.snapshot.value != null &&
          eventReceiver.snapshot.value is Map) {
        calls.addAll(eventReceiver.snapshot.value as Map<dynamic, dynamic>);
        // If there are any calls with this receiverId, return true
      }
      if (eventSender.snapshot.value != null &&
          eventSender.snapshot.value is Map) {
        calls.addAll(eventSender.snapshot.value as Map<dynamic, dynamic>);
        // If there are any calls with this receiverId, return true
      }

      return calls.isNotEmpty;
    } catch (e) {
      log("Error checking calls: $e");
      return false;
    }
  }

  Future<void> createCallDocument(String id, Call model) async {
    try {
      final pusher = FirebaseDatabase.instance.ref().child('calls').child(id);
      pusher.set(model.toMap());
      pusher.push();
      log("Document saved successfully.");
    } catch (e) {
      log("Failed to save document: $e");
    }
  }

  int _secondsElapsed = 0;

  void startTimer() {
    final timeCall =
        StaticVar.context.read<CommonProvider>().voiceCallsDuration ?? 300;
    final timeRemainingAlert =
        StaticVar.context.read<CommonProvider>().voiceCallsRemainingDuration ??
            30;
    _secondsElapsed = 0;
    _secondsRemaining = timeCall;
    const duration = Duration(seconds: 1);
    isAlertCallWillEndPlaying = false;
    _timer = Timer.periodic(duration, (Timer timer) {
      if (_secondsRemaining > 0) {
        if (_secondsRemaining == timeCall - 1) {
          enableSpeakerphone = true;
          notifyListeners();
          _engine.setEnableSpeakerphone(true);
        }
        _secondsRemaining--;
        _secondsElapsed++;
        if (_secondsRemaining <= timeRemainingAlert) {
          _playAlertWillEndSound();
        }
        if (_secondsRemaining == 1) {
          _secondsElapsed++;
          stopSound();
          endCall(
            withEnd: true,
            leaveWithDelete: false,
            sendChatStatus: isSender,
          );
          if (NavigationService.instance.isCurrent(CallPage.routeName)) {
            NavigationService.instance.goBack();
          }
        }
        notifyListeners();
      }
    });
  }

  void _playAlertWillEndSound() async {
    if (isAlertCallWillEndPlaying.inverted) {
      isAlertCallWillEndPlaying = true;
      _playSound(soundFile: 'assets/audio/warning_call_ended.wav', loop: false);
    }
  }

  void _stopAlertWillEndSound() {
    stopSound();
    isAlertCallWillEndPlaying = false;
  }

  Future<void> _playSound(
      {required String soundFile, bool loop = false}) async {
    final path = await _engine.getAssetAbsolutePath(soundFile);
    await stopSound();
    _engine.playEffect(
      soundId: 1,
      filePath: path ?? '',
      loopCount: loop ? -1 : 0,
      pitch: 1,
      pan: 1,
      gain: 100,
      publish: false,
    );
  }

  Future<void> stopSound() async {
    try {
      isAlertInAnotherCallPlaying = false;
      await _engine.stopEffect(1);
      await _engine.stopAllEffects();
    } catch (e) {
      log('error stop sound ${e.toString()}');
    }
  }

  void stopTimer() {
    _callTimerNoAnswer?.cancel();
    _callTimerNoAnswer = null;
    _timer?.cancel();
    _timer = null;
  }

  String get timeLeft {
    int minutes = _secondsRemaining ~/ 60;
    int seconds = _secondsRemaining % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  Future<void> deleteCallFirebase(String? id,
      {String status = 'canceled'}) async {
    if (id != null) {
      await FirebaseDatabase.instance
          .ref()
          .child('calls/$id')
          .update({'status': status}).then((_) async {
        // After updating status, remove the call data
        await FirebaseDatabase.instance
            .ref()
            .child('calls/$id')
            .remove()
            .then((_) {
          print('Call entry removed from the database');
        });
      });
    }
  }

  Future<dynamic> getCurrentCall() async {
    var calls = await FlutterCallkitIncoming.activeCalls();
    if (calls is List && calls.isNotEmpty) {
      return calls[0];
    } else {
      return calls;
    }
  }

  void _reset() {
    if (fabKey.currentState != null) {
      fabKey.currentState?.resetPosition();
    }
    ForegroundServiceImpl().stopBackgroundService();
    deleteDataCallInShardPreferance();
    stopSound();
    _secondsElapsed = 0;
    statusCall = CallStatus.nothing;
    callAccepted = false;
    _secondsRemaining = 0;
    isAlertCallWillEndPlaying = false;
    isJoined = false;
    openMicrophone = true;
    enableSpeakerphone = false;
    _inEarMonitoringVolume = 100;
    stopTimer();
    _cancelListenerFirebase();
    _stopAlertWillEndSound();
    callKitId = '';
    notifyListeners();
  }

  void setMute(bool value) {
    openMicrophone = value.inverted;
    notifyListeners();
  }

  Future<void> setCallConnected() async {
    if (callKitId.isNotEmpty) {
      await FlutterCallkitIncoming.setCallConnected(callKitId);
    } else {
      final call = await getCurrentCall();
      if (call != null && call['id'] != null) {
        await FlutterCallkitIncoming.setCallConnected(call['id']);
      }
    }
  }

  Future<void> startCallFlutterKit(
      {required String nameCaller, Map<String, dynamic>? extraData}) async {
    if (callKitId.isEmpty) {
      final id = const Uuid().v4();
      callKitId = id;
      final params = CallKitParams(
        id: id,
        nameCaller: nameCaller,
        appName: 'Modarby',
        handle: '**********',
        type: 0,
        duration:
            (StaticVar.context.read<CommonProvider>().voiceCallsRingDuration ??
                    30) *
                1000,
        textAccept: 'Accept',
        textDecline: 'Decline',
        missedCallNotification: const NotificationParams(
          showNotification: false,
          isShowCallback: false,
          subtitle: 'Missed call',
          callbackText: 'Call back',
        ),
        extra: extraData ?? {},
        headers: <String, dynamic>{'apiKey': 'Abc@123!', 'platform': 'flutter'},
        android: const AndroidParams(
          isCustomNotification: false,
          isShowLogo: false,
          ringtonePath: 'system_ringtone_default',
          backgroundColor: '#0955fa',
          backgroundUrl: 'assets/invisible.png',
          actionColor: '#4CAF50',
          textColor: '#000000',
          isShowFullLockedScreen: true,
          incomingCallNotificationChannelName: 'high_importance_channel',
        ),
        ios: const IOSParams(
          iconName: 'CallKitLogo',
          handleType: '',
          supportsVideo: true,
          maximumCallGroups: 2,
          maximumCallsPerCallGroup: 1,
          audioSessionMode: 'default',
          audioSessionActive: true,
          audioSessionPreferredSampleRate: 44100.0,
          audioSessionPreferredIOBufferDuration: 0.005,
          supportsDTMF: true,
          supportsHolding: true,
          supportsGrouping: false,
          supportsUngrouping: false,
          ringtonePath: 'system_ringtone_default',
        ),
      );
      await FlutterCallkitIncoming.startCall(params);
    }
  }

  Future<void> callUser({String? userId, String? nameUser}) async {
    if (userId == null) return;
    final statusMic = await PermissionWrapper.checkPermission(
      submitColor: ThemeColors.color26467A,
      context: StaticVar.context,
      title: titleMicCallPermission.translate().orDefault,
      body: bodyMicCallPermission.translate().orDefault,
      openSettingsText: openSetting.translate().orDefault,
      permission: Permission.microphone,
    );
    if (statusMic.inverted) {
      return;
    }
    log('microphone Permission granted ');
    final loader = getIt<IPageLoadingDialog>().showLoadingDialog();
    final isConnected = await getIt<InternetChecker>().checkInternet();
    if (isConnected.inverted) {
      loader.hide();
      Snackbars.danger(StaticVar.context,
          titleMessageNoInternetConnectionCall.translate().orDefault);
      return;
    }

    log('check  isReceiverInAnyCall ');

    final isReceiverInCall = await isReceiverInAnyCall(userId);
    if (isReceiverInCall) {
      _playSoundBusy();
      stopTimer();
      updateStatus(CallStatus.receiverInCall);

      final arguments = CallPageArguments(
        isSender: true,
        senderUserId: userId,
        agoraToken: '',
        channelName: '',
        name: nameUser,
        receiverLanguageId: 0,
        userID: 0,
      );
      callArgument = arguments;
      final data = await StaticVar.context
          .read<MessagesProvider>()
          .getUserInfo(callArgument?.senderUserId ?? '');
      loader.hide();
      NavigationService.instance
          .navigateToIfNotCurrent(CallPage.routeName, args: arguments);
      _sendMessageStatusCallChat(
          AttachmentType.callReceiverInAnotherCall,
          titleMessageMissedCall
              .translateLang(data?.lang == 1 ? 'ar' : 'en')
              .orDefault);
      CallCleanupService().checkAndDeleteOldCallsByReceiver(userId);
      return;
    }
    log('check  success receiver not In Any Call ');
    CallResponse? data;
    try {
      data = await generateAgoraToken(userId);
    } catch (error) {
      loader.hide();
      final errorException = ErrorParser().parseError(error);
      Snackbars.danger(StaticVar.context, errorException.message);
    }

    if (data?.token == null || data?.channelName == null) {
      loader.hide();
      return;
    }

    final arguments = CallPageArguments(
      isSender: true,
      senderUserId: userId,
      agoraToken: data?.token ?? "",
      channelName: data?.channelName ?? "",
      name: nameUser,
      receiverLanguageId: data?.langId ?? 0,
      userID: data?.uid,
    );
    _reset();
    callArgument = arguments;
    updateStatus(CallStatus.ringing);
    startCallTimerNoAnswer(withEnd: true);
    await joinChannel(
      callPageArguments: callArgument,
      userId: userId,
    );
    _playSound(
      soundFile: 'assets/audio/call_sound_sender.wav',
      loop: true,
    );
    loader.hide();
    NavigationService.instance
        .navigateToIfNotCurrent(CallPage.routeName, args: callArgument);

    listenerCallIfCancel(callArgument?.channelName ?? '');
    startCallFlutterKit(
        nameCaller: nameUser ?? '', extraData: callArgument?.toJson());
  }

  Future<void> confirmationCallDialog(
      {String? userId, String? nameUser}) async {
    if (userId == null) return;
    if (getIt<Storage>().showConfirmCall) {
      final isConfirmed = await showDialog(
        context: StaticVar.context,
        builder: (_) => FractionallySizedBox(
          widthFactor: 1.1,
          heightFactor: 0.5,
          child: CallConfirmationDialog(),
        ),
      );

      if (isConfirmed) {
        getIt<Storage>().showConfirmCall = false;
        callUser(
          userId: userId,
          nameUser: nameUser,
        );
      }
    } else {
      callUser(
        userId: userId,
        nameUser: nameUser,
      );
    }
  }

  Future<void> getInfoStudentData() async {
    if (callArgument?.senderUserId != null &&
        (getIt<Storage>().role != Roles.STUDENT ||
            getIt<Storage>().role != Roles.PARENT) &&
        isSender.inverted) {
      final data = await StaticVar.context
          .read<MessagesProvider>()
          .getStudentInfoMessage(callArgument?.senderUserId);
      if (data != null) {
        studentAdditionalInfo = data;
        notifyListeners();
      }
    }
  }

  Future<void> getUserInfo() async {
    final data = await StaticVar.context
        .read<MessagesProvider>()
        .getUserInfo(callArgument?.senderUserId ?? '');
    messageUserInfoModel = data;
    notifyListeners();
  }

  Future<void> sendMessage() async {
    final conversation = _buildConversation(messageUserInfoModel);
    StaticVar.context.read<MessagesProvider>().conversation = conversation;
    StaticVar.context.read<MessagesProvider>().haveMessageInstructorReply =
        false;
    if (conversation.userId != null) {
      final argument = ConversationArgument(
        conversationType: ConversationType.anyPlace,
        userId: conversation.userId ?? '',
        userName: conversation.userName ?? '',
        userNameAr: conversation.userNameAr ?? '',
      );
      notifyListeners();
      if (callAccepted.inverted) {
        _reset();
      }
      NavigationService.instance
          .navigateToAndRemove(ConversationScreen.routeName, args: argument);
    }
  }

  bool get isStudent =>
      getIt<Storage>().role == Roles.STUDENT ||
      getIt<Storage>().role == Roles.PARENT;
  Conversation _buildConversation(MessageUserInfoModel? data) {
    return Conversation(
      usersid: callArgument?.senderUserId ?? '',
      userslist: Shard().isStudent.inverted
          ? data?.name
          : Shard().language == LanguagesKeys.ARABIC
              ? (data?.arabicName ?? '')
              : (data?.name ?? ''),
      userspic: '${data?.picture}',
      usersflag: data?.flag ?? 'SA',
      counts: null,
      usersemail: null,
      usersprofile: null,
      lastMessage: null,
      lastMessageDate: null,
    );
  }

  void cancelCall() {
    updateStatus(CallStatus.nothing);
    _reset();
    if (NavigationService.instance.isCurrent(CallPage.routeName)) {
      NavigationService.instance.goBack();
    }
    notifyListeners();
  }

  void disposeProvider() {
    _cancelListenerFirebase();
    stopTimer();
    stopSound();
    _stopAlertWillEndSound();
    _engine.unregisterEventHandler(_rtcEngineEventHandler);
    _engine.release();
  }

  void updateStatus(CallStatus status) {
    statusCall = status;
    notifyListeners();
  }

  Future<void> removeIncomingCall() async {
    await _engine.leaveChannel();
    statusCall = CallStatus.nothing;
    _cancelListenerFirebase();
    stopSound();
    stopTimer();
    _stopAlertWillEndSound();
    if (NavigationService.instance.isCurrent(CallPage.routeName)) {
      NavigationService.instance.goBack();
    }
    if (callKitId.isNotEmpty) {
      await FlutterCallkitIncoming.endCall(callKitId);
    } else {
      final call = await getCurrentCall();
      if (call != null && call['id'] != null) {
        await FlutterCallkitIncoming.endCall(call['id']);
      }
    }
    _reset();
    notifyListeners();
  }

  void update() {
    notifyListeners();
  }

  Future<void> reCall() async {
    if (callArgument?.name != null &&
        (callArgument?.senderUserId ?? '').isNotEmpty) {
      await FlutterCallkitIncoming.silenceEvents();
      await FlutterCallkitIncoming.endAllCalls();
      await FlutterCallkitIncoming.unsilenceEvents();
      _reset();
      callUser(
          userId: callArgument?.senderUserId,
          nameUser: callArgument?.name ?? '');
    }
  }

  Future<void> _startRecord() async {
    if ((callArgument?.channelName ?? '').isEmpty) return;
    await startRecord(channelName: callArgument?.channelName ?? '');
  }

  Future<void> _stopRecord() async {
    if ((callArgument?.channelName ?? '').isEmpty) return;
    await stopRecord(
      channelName: callArgument?.channelName ?? '',
    );
  }

  Future<CallResponse?> generateAgoraToken(String receiverId) async {
    final accessToken = Shard().accessToken;
    if (accessToken == null) {
      return null;
    }
    try {
      final response =
          await _repository.generateAgoraToken(receiverId, accessToken);
      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> canCall(String receiverId) async {
    final voiceCallEnabled =
        StaticVar.context.read<CommonProvider>().voiceCallsEnabled ?? false;
    if (voiceCallEnabled.inverted) return false;
    final accessToken = Shard().accessToken;
    if (accessToken == null) {
      return false;
    }
    try {
      final response = await _repository.canCall(
          receiverUserId: receiverId, token: accessToken);
      langIdReceiver = response.lang ?? 1;
      updateIsAllowedMeToCall(response.canCall ?? false);
      updateIsAllowedReceiverCallMe(response.isAllowedOtherSetting ?? false);
      return response.canCall ?? false;
    } catch (error) {
      final errorException = ErrorParser().parseError(error);
      Snackbars.danger(StaticVar.context, errorException.message);
      return false;
    }
  }

  Future<void> updateCanCall(String receiverId, bool isAllowed) async {
    final accessToken = Shard().accessToken;
    if (accessToken == null) {
      return;
    }
    final loader = getIt<IPageLoadingDialog>().showLoadingDialog();
    try {
      updateIsAllowedReceiverCallMe(isAllowed);
      await _repository.updateCanCall(
        receiverUserId: receiverId,
        token: accessToken,
        isAllowed: isAllowed,
      );
      loader.hide();
    } catch (error) {
      loader.hide();
      final errorException = ErrorParser().parseError(error);
      Snackbars.danger(StaticVar.context, errorException.message);
    }
  }

  Future<void> stopRecord({required String channelName}) async {
    final accessToken = Shard().accessToken;
    if (accessToken == null) return;
    try {
      await _repository.stopRecordAudio(
          token: accessToken, channelName: channelName);
    } catch (error) {
      final errorException = ErrorParser().parseError(error);
      log('{error stop record :${errorException.message}');
    }
  }

  Future<void> startRecord({required String channelName}) async {
    final accessToken = Shard().accessToken;
    if (accessToken == null) return;
    try {
      await _repository.startRecordAudio(
          token: accessToken, channelName: channelName);
    } catch (error) {
      final errorException = ErrorParser().parseError(error);
      log('{error start record :${errorException.message}');
    }
  }

  updateIsAllowedMeToCall(bool value) {
    isAllowedMeToCall = value ? 1 : 2;
    notifyListeners();
  }

  updateIsAllowedReceiverCallMe(bool value) {
    // 1 is allowed
    // 2 is not allowed
    isAllowedReceiverToCallMe = value ? 1 : 2;
    notifyListeners();
  }

  String _formatTime(int totalSeconds) {
    int minutes = totalSeconds ~/ 60;
    int seconds = totalSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  void _sendMessageStatusCallChat(int typeCall, String timeFinished) {
    if (callArgument?.senderUserId == null) return;
    final authProvider = StaticVar.context.read<AuthenticationProvider>();
    StaticVar.context.read<MessagesProvider>().sendMessageLocal(
          callArgument?.senderUserId,
          authProvider,
          timeFinished,
          false,
          '',
          typeCall,
        );
  }

  Future<void> _updateStatusFirebase(String? id) async {
    if (id != null) {
      try {
        final callRef =
            FirebaseDatabase.instance.ref().child('calls').child(id);

        // Method 1: Update only the status field
        await callRef.update({
          'status': 'active'
              .toString(), // or however you store the enum in your database
        });
        print("Call status updated successfully");
      } catch (e) {
        print("Failed to update call status: $e");
      }
    }
  }

  Future<void> saveDataCallInShardPreferance() async {
    final shard = await SharedPreferences.getInstance();
    await shard.setString('_callerName', callArgument?.name ?? '');
    await shard.setBool('_callIsActive', true);
    await ForegroundServiceImpl().startBackgroundService();
  }

  Future<void> deleteDataCallInShardPreferance() async {
    final shard = await SharedPreferences.getInstance();
    shard.setString('_callerName', '');
    shard.setBool('_callIsActive', false);
  }

  Future<void> _haideCallIfAndroid() async {
    if (Platform.isAndroid && callKitId.isNotEmpty) {
      CallKitParams callKitParams = CallKitParams(id: callKitId);
      await FlutterCallkitIncoming.hideCallkitIncoming(callKitParams);
    }
  }

  Future<void> saveCallKitId() async {
    if (kReleaseMode) {
      if (callKitId.isEmpty) {
        final call = await getCurrentCall();
        log('call from current call:$call');
        if (call != null && call['id'] != null) {
          callKitId = call['id'];
          _haideCallIfAndroid();
        }
      } else {
        _haideCallIfAndroid();
      }
    }
  }

  Future<bool> _checkDataCallIfExists(DataSnapshot snapshot) async {
    log('finished:checkIfCallExists:${snapshot.value}');
    final timeCall =
        (StaticVar.context.read<CommonProvider>().voiceCallsDuration ?? 0);
    final timeRingCall =
        (StaticVar.context.read<CommonProvider>().voiceCallsRingDuration ?? 0);

    if (snapshot.value == null) return false;

    final Map<dynamic, dynamic> call = snapshot.value as Map;

    // Get current time in milliseconds since epoch
    int serverTime = await getCurrentServerTime();
    int initiatedAt = call['timestamp'] as int;
    final status = call['status'];
    int secondsElapsed = ((serverTime - initiatedAt) ~/ 1000);
    if (status == 'active') {
      return secondsElapsed <= timeCall;
    } else {
      return secondsElapsed <= timeRingCall;
    }
  }

  Future<void> _playSoundBusy() async {
    final path = await _engine.getAssetAbsolutePath('assets/audio/busy.wav');
    await stopSound();
    _engine.playEffect(
      soundId: 1,
      filePath: path ?? '',
      loopCount: 0,
      pitch: 1,
      pan: 1,
      gain: 100,
      publish: false,
    );
    isAlertInAnotherCallPlaying = true;
  }
}
