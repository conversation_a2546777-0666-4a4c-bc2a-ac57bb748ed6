import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:modarby/Widgets/custom_text_widget.dart';
import 'package:modarby/Widgets/flip_if_rtl.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/config/themes/images.dart';

class CustomAppBarAuthWidget extends StatelessWidget {
  const CustomAppBarAuthWidget({
    Key? key,
    this.title,
    this.valueProgress = 0.0,
    this.onClickBack,
    this.onClickClose,
    required this.showBackTitle,
    required this.showBack,
    this.showClose = false,
    this.showTitle = true,
    this.showProgress = true,
    this.titleSize,
    this.heightProgress,
    this.colorProgress,
  }) : super(key: key);
  final String? title;
  final VoidCallback? onClickBack;
  final VoidCallback? onClickClose;
  final double? heightProgress;
  final double? valueProgress;
  final Color? colorProgress;
  final bool showBack;
  final bool showClose;
  final bool showBackTitle;
  final bool showTitle;
  final bool showProgress;
  final double? titleSize;

  @override
  Widget build(BuildContext context) {
    return _appBar(context);
  }

  _appBar(BuildContext context) {
    return Column(
      children: [
        if (!showClose) SizedBox(height: 10.sp),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _backAppBar(context),
            _titleAppBar(),
            _closeAppBar(context),
          ],
        ),
        // if (!showClose) SizedBox(height: 16.sp),

        Visibility(
          visible: showProgress,
          replacement: SizedBox(height: 4.sp),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: LinearProgressIndicator(
              minHeight: heightProgress ?? 4.sp,
              value: valueProgress ?? 0.0,
              backgroundColor: colorProgress ?? ThemeColors.colorF2F2F7,
              color: colorProgress ?? ThemeColors.accentColor,
            ),
          ),
        ),
      ],
    );
  }

  _backAppBar(BuildContext context) {
    return Visibility(
      visible: showBack,
      replacement: SizedBox(
        width: 40.w,
        height: 30.sp,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(width: 6.w),
          FlipIfRTL(builder: (_) {
            return InkWell(
              onTap: () => _clickBack(context),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: SvgPicture.asset(
                  Images.bookingBackIcon,
                  width: 24.w,
                  height: 24.w,
                ),
              ),
            );
          }),
          // if (showBackTitle)
          //   CustomTextWidget(
          //     title: BTN_BACK.translate(),
          //     color: ThemeColors.black,
          //     size: 13,
          //     fontWeight: FontWeight.w400,
          //   )
        ],
      ),
    );
  }

  _closeAppBar(BuildContext context) {
    return Visibility(
      visible: showClose,
      replacement: SizedBox(
        width: 40.w,
        height: 40.sp,
      ),
      child: IconButton(
        onPressed: onClickClose ??
            () {
              Navigator.pop(context);
            },
        icon: Icon(
          Icons.close,
          color: ThemeColors.black,
          size: 24.sp,
        ),
      ),
    );
  }

  _titleAppBar() {
    return Visibility(
      visible: showTitle,
      replacement: const SizedBox(
        width: 2,
      ),
      child: CustomTextWidget(
        title: title ?? '',
        fontWeight: FontWeight.w600,
        color: ThemeColors.color1C1C1E,
        textAlign: TextAlign.center,
        size: titleSize ?? 15,
      ),
    );
  }

  void _clickBack(BuildContext context) {
    if (onClickBack != null) {
      onClickBack?.call();
    } else {
      Navigator.pop(context);
    }
  }
}
