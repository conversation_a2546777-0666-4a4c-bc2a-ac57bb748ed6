import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:modarby/Enums/Roles.dart';
import 'package:modarby/Models/Content/CountryCode.dart';
import 'package:modarby/Models/Content/Nationality.dart';
import 'package:modarby/Models/Content/StudentExtrnalSource.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/CollectDataProvider.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/DependencyManager/TextProviders/TextProvider.dart';
import 'package:modarby/Providers/MessagesProvider.dart';
import 'package:modarby/Providers/SearchProvider.dart';
import 'package:modarby/Providers/StudentProfileProvider.dart';
import 'package:modarby/SharedScreens/NotificationsPermission.dart';
import 'package:modarby/Student/Screens/Home.dart';
import 'package:modarby/Student/Screens/Lessons.dart';
import 'package:modarby/Widgets/ProfileSelectBox.dart';
import 'package:modarby/Widgets/custom_button_widget.dart';
import 'package:modarby/Widgets/custom_selection_country.dart';
import 'package:modarby/Widgets/custom_text_widget.dart';
import 'package:modarby/Widgets/generic_listing_sheet/generic_listing_item.dart';
import 'package:modarby/Widgets/generic_listing_sheet/generic_listing_sheet.dart';
import 'package:modarby/Widgets/sticky_bottom_appbar.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/config/themes/images.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/helper/show_bottom_sheet/i_show_bottom_sheet.dart';
import 'package:modarby/core/helper/show_bottom_sheet/show_bottom_sheet_input.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/utilities/ProgressIndicators.dart';
import 'package:modarby/core/utilities/Snackbars.dart';
import 'package:modarby/core/utilities/error_parser.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';
import 'package:modarby/core/utilities/navigation_service.dart';
import 'package:modarby/core/utilities/shard.dart';
import 'package:modarby/core/utilities/storage.dart';
import 'package:modarby/features/auth/arguments/auth_argument.dart';
import 'package:modarby/features/auth/promise/student_promise_page.dart';
import 'package:modarby/features/auth/widgets/custom_app_bar_auth_widget.dart';
import 'package:modarby/features/messages/presentation/views/conversation_screen.dart';
import 'package:provider/provider.dart';

import '../../../Models/Authentication/StudentAdditionalInfoDTO.dart';
import '../../../Models/Authentication/StudentRegisterDTO.dart';
import '../../../core/Utilities/Validator.dart';

enum AdditionalInfoType { personal, study }

class StudentAdditionalInfoPage extends StatefulWidget {
  static const routeName = '/StudentAdditionalInfoPage';
  final AuthArgument? arguments;
  const StudentAdditionalInfoPage({Key? key, this.arguments}) : super(key: key);

  @override
  State createState() => _StudentAdditionalInfoPageState();
}

class _StudentAdditionalInfoPageState extends State<StudentAdditionalInfoPage> {
  late TextProvider _textProvider;

  late LocalizationProvider _localizationProvider;

  String? _language;

  List? _genders;
  List<Nationality>? nationalities;

  List<dynamic>? _levels;
  List<String> _years = List.empty(growable: true);
  List<String> _collegeYears = List.empty(growable: true);
  List<StudentExtrnalSource> studentExternalSourceList =
      List.empty(growable: true);

  // int? _selectedType;
  int? _selectedGenderIndex;
  int? _selectedLevel;
  int? _selectedCurriculum;
  int? _selectedYear;
  int? _selectedSource;
  StudentExtrnalSource? _itemSelectedSource;

  String? _typeLabel;
  String? _genderLabel;
  String? _levelLabel;
  String? _curriculumLabel;
  String? _yearLabel;
  String? _fullNameLabel;
  String? _emailLabel;
  String? sourceLabel;
  String? nationalityLabel;

  late Map<String?, bool> _errorsStudy;
  late Map<String?, bool> _errorsPersonal;
  AdditionalInfoType _additionalInfoTypeSelected = AdditionalInfoType.personal;
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _nationalityController = TextEditingController(
    text: pleaseSelectSource.translate().orDefault,
  );

  CountryCode? defaultCountryCode;
  bool validatePersonalInfo = false;
  bool validateStudyInfo = false;
  bool nameExists = false;
  bool emailExists = false;
  bool nationalityExists = false;
  bool genderExists = false;
  // bool typeExists = false;
  bool countryExists = false;
  bool howHearAboutUsExists = false;
  late AuthenticationProvider _authenticationProvider;
  @override
  void initState() {
    _initializeData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (_additionalInfoTypeSelected != AdditionalInfoType.personal) {
          _additionalInfoTypeSelected = AdditionalInfoType.personal;
          _validatePerson();
          setState(() {});
          return true;
        } else if (widget.arguments?.showBackInAdditionalInfo ?? false) {
          return true;
        } else {
          return false;
        }
      },
      child: Scaffold(
        body: _buildBody(),
        bottomNavigationBar: _bottom(),
      ),
    );
  }

  Widget _buildBody() {
    return SafeArea(
      child: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: _buildContent(),
      ),
    );
  }

  Widget _appBar() {
    return CustomAppBarAuthWidget(
      title: _additionalInfoTypeSelected == AdditionalInfoType.personal
          ? weAlmostDoneTitle.translate()
          : lastStepTitle.translate(),
      showBackTitle: true,
      showBack: (_additionalInfoTypeSelected == AdditionalInfoType.study &&
              isRegisteredUser.inverted) ||
          (widget.arguments?.showBackInAdditionalInfo ?? false),
      showClose: false,
      showTitle: true,
      showProgress: isRegisteredUser.inverted,
      valueProgress: _additionalInfoTypeSelected == AdditionalInfoType.personal
          ? 1 / 2
          : 4 / 5,
      onClickBack: _onClickBackAppBar,
      onClickClose: _onClickCloseAppBar,
    );
  }

  Widget _buildContent() {
    final isPersonal =
        _additionalInfoTypeSelected == AdditionalInfoType.personal;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        _appBar(),
        Expanded(
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsetsDirectional.only(
                start: isPersonal ? 16.w : 0,
                end: isPersonal ? 16.w : 0,
                top: 16.sp,
                bottom: 24.sp,
              ),
              child: Visibility(
                visible:
                    _additionalInfoTypeSelected == AdditionalInfoType.personal,
                replacement: _studyInfoContent(),
                child: _personalInfoContent(),
              ),
            ),
          ),
        )
      ],
    );
  }

  // Widget _buildType() {
  //   return Visibility(
  //       visible: _additionalInfoTypeSelected == AdditionalInfoType.personal,
  //       replacement: const SizedBox.shrink(),
  //       child: Container(
  //         margin: EdgeInsetsDirectional.only(
  //           top: 10.sp,
  //         ),
  //         padding: EdgeInsetsDirectional.only(
  //           start: 15.w,
  //           top: 16.sp,
  //           end: 15.w,
  //           bottom: 16.sp,
  //         ),
  //         decoration: BoxDecoration(
  //           borderRadius: BorderRadius.circular(10),
  //           color: ThemeColors.white,
  //           border: Border.all(
  //             color: ThemeColors.colorF2F2F7,
  //           ),
  //         ),
  //         child: Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             _buildTitle(_typeLabel),
  //             SizedBox(height: 5.sp),
  //             _buildTypeValues(),
  //           ],
  //         ),
  //       ));
  // }
  //
  // Widget _buildTypeValues() {
  //   return IntrinsicHeight(
  //     child: Container(
  //       padding: EdgeInsetsDirectional.only(
  //         start: 2.w,
  //         end: 2.w,
  //       ),
  //       decoration: BoxDecoration(
  //         color: ThemeColors.colorF2F2F7,
  //         borderRadius: BorderRadiusDirectional.circular(8),
  //       ),
  //       child: Row(
  //           mainAxisSize: MainAxisSize.min,
  //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //           children: [
  //             Expanded(
  //               child: TextButton(
  //                 style: TextButton.styleFrom(
  //                   alignment: Alignment.center,
  //                   foregroundColor: ThemeColors.colorF2F2F7,
  //                   disabledBackgroundColor: ThemeColors.colorF2F2F7,
  //                   elevation: _selectedType == 1 ? 1 : 0,
  //                   shadowColor: ThemeColors.black,
  //                   shape: RoundedRectangleBorder(
  //                     borderRadius: BorderRadius.circular(8),
  //                   ),
  //                   backgroundColor: _selectedType == 1
  //                       ? ThemeColors.white
  //                       : ThemeColors.colorF2F2F7,
  //                 ),
  //                 onPressed: () => _onTypeChange(
  //                   1,
  //                 ),
  //                 child: _buildValue(
  //                     STUDENT_ADDITIONAL_INFO_TYPE_STUDENT.translate()),
  //               ),
  //             ),
  //             Visibility(
  //               visible: _selectedType == null,
  //               replacement: const SizedBox.shrink(),
  //               child: Container(
  //                 padding: EdgeInsetsDirectional.only(
  //                   top: 3.sp,
  //                   bottom: 3.sp,
  //                 ),
  //                 child: const VerticalDivider(
  //                   width: 2,
  //                   thickness: 2,
  //                   color: ThemeColors.grayE5E5EA,
  //                 ),
  //               ),
  //             ),
  //             Expanded(
  //               child: TextButton(
  //                 style: TextButton.styleFrom(
  //                   alignment: Alignment.center,
  //                   foregroundColor: ThemeColors.colorF2F2F7,
  //                   disabledBackgroundColor: ThemeColors.colorF2F2F7,
  //                   elevation: _selectedType == 2 ? 1 : 0,
  //                   shadowColor: ThemeColors.black,
  //                   shape: RoundedRectangleBorder(
  //                     borderRadius: BorderRadius.circular(8),
  //                   ),
  //                   backgroundColor: _selectedType == 2
  //                       ? ThemeColors.white
  //                       : ThemeColors.colorF2F2F7,
  //                 ),
  //                 onPressed: () => _onTypeChange(
  //                   2,
  //                 ),
  //                 child: _buildValue(
  //                     STUDENT_ADDITIONAL_INFO_TYPE_PARENT.translate()),
  //               ),
  //             ),
  //           ]),
  //     ),
  //   );
  // }

  Widget _buildGender() {
    return Visibility(
        visible: _additionalInfoTypeSelected == AdditionalInfoType.personal,
        replacement: const SizedBox.shrink(),
        child: Container(
          margin: EdgeInsetsDirectional.only(
            top: 10.sp,
          ),
          padding: EdgeInsetsDirectional.only(
            start: 15.w,
            top: 16.sp,
            end: 15.w,
            bottom: 16.sp,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: ThemeColors.white,
            border: Border.all(
              color: ThemeColors.colorF2F2F7,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTitle(_genderLabel),
              SizedBox(height: 5.sp),
              _buildGenderValues(),
              // _buildError(_genderLabel),
            ],
          ),
        ));
  }

  Widget _buildGenderValues() {
    return IntrinsicHeight(
      child: Container(
        padding: EdgeInsetsDirectional.only(
          start: 2.w,
          end: 2.w,
        ),
        decoration: BoxDecoration(
          color: ThemeColors.colorF2F2F7,
          borderRadius: BorderRadiusDirectional.circular(8),
        ),
        child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: TextButton(
                  style: TextButton.styleFrom(
                    alignment: Alignment.center,
                    foregroundColor: ThemeColors.colorF2F2F7,
                    disabledBackgroundColor: ThemeColors.colorF2F2F7,
                    elevation: _selectedGenderIndex == 0 ? 1 : 0,
                    shadowColor: ThemeColors.black,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    backgroundColor: _selectedGenderIndex == 0
                        ? ThemeColors.white
                        : ThemeColors.colorF2F2F7,
                  ),
                  onPressed: () => _onGenderChange(
                    0,
                  ),
                  child: _buildValue(_genders?[0].name.toString()),
                ),
              ),
              Visibility(
                visible: _selectedGenderIndex == null,
                replacement: const SizedBox.shrink(),
                child: Container(
                  padding: EdgeInsetsDirectional.only(
                    top: 3.sp,
                    bottom: 3.sp,
                  ),
                  child: const VerticalDivider(
                    width: 2,
                    thickness: 2,
                    color: ThemeColors.grayE5E5EA,
                  ),
                ),
              ),
              Expanded(
                child: TextButton(
                  style: TextButton.styleFrom(
                    alignment: Alignment.center,
                    foregroundColor: ThemeColors.colorF2F2F7,
                    disabledBackgroundColor: ThemeColors.colorF2F2F7,
                    elevation: _selectedGenderIndex == 1 ? 1 : 0,
                    shadowColor: ThemeColors.black,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    backgroundColor: _selectedGenderIndex == 1
                        ? ThemeColors.white
                        : ThemeColors.colorF2F2F7,
                  ),
                  onPressed: () => _onGenderChange(
                    1,
                  ),
                  child: _buildValue(
                    _genders?[1].name.toString(),
                  ),
                ),
              ),
            ]),
      ),
    );
  }

  Widget _buildTitle(String? title) {
    return CustomTextWidget(
      title: title,
      color: ThemeColors.black,
      size: 13,
      fontWeight: FontWeight.w400,
    );
  }

  Widget _buildValue(String? value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CustomTextWidget(
          title: value,
          fontWeight: FontWeight.w500,
          size: 13,
          textAlign: TextAlign.center,
          color: ThemeColors.black,
        ),
      ],
    );
  }

  Widget _buildError(
    String? field, {
    String error = '',
  }) {
    return Visibility(
      visible: _additionalInfoTypeSelected == AdditionalInfoType.study
          ? (_errorsStudy[field] ?? false)
          : (_errorsPersonal[field] ?? false),
      child: Padding(
        padding: EdgeInsetsDirectional.only(
          top: 10.sp,
        ),
        child: _textProvider.buildNormalText5(
          error.isNotEmpty ? error : Validator.isRequired(_language!),
          color: ThemeColors.darkRed,
        ),
      ),
    );
  }

  void _initializeData() {
    _textProvider = context.read<DependencyManager>().text;
    _localizationProvider = context.read<DependencyManager>().localization;
    _authenticationProvider = context.read<AuthenticationProvider>();
    _language = _localizationProvider.locals.language;
    nationalities = _localizationProvider.lookups
        .getWithKey(NATIONALITY)
        .cast<Nationality>()
        .toList();
    _genders = _localizationProvider.lookups.getWithKey(GENDER);
    _levels = _localizationProvider.lookups.getWithKey(TRAINEETYPE);
    _years = [
      _localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_FIRST),
      _localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_SECOND),
      _localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_THIRD),
      _localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_FOURTH),
      _localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_FIFTH),
      _localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_SIXTH),
    ];
    studentExternalSourceList = _localizationProvider.lookups
        .getWithKey(studentExternalSource)
        .cast<StudentExtrnalSource>();
    studentExternalSourceList.removeWhere((element) => element.id == 2237);
    _collegeYears = [
      _localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_FIRST),
      _localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_SECOND),
      _localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_THIRD),
      _localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_FOURTH),
      _localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_MORETHANFOURTH),
    ];

    _typeLabel = _localizationProvider.resources
        .getWithKey(STUDENT_ADDITIONAL_INFO_TYPE);
    _genderLabel = _localizationProvider.resources
        .getWithKey(STUDENT_ADDITIONAL_INFO_GENDER);
    _levelLabel = _localizationProvider.resources
        .getWithKey(STUDENT_ADDITIONAL_INFO_LEVEL);
    _curriculumLabel = _localizationProvider.resources
        .getWithKey(STUDENT_ADDITIONAL_INFO_CURRICULUM);
    _yearLabel = _localizationProvider.resources
        .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR);
    _fullNameLabel =
        _localizationProvider.resources.getWithKey(STUDENT_FORMS_FULLNAME);
    _emailLabel =
        _localizationProvider.resources.getWithKey(STUDENT_FORMS_EMAIL);
    sourceLabel =
        _localizationProvider.resources.getWithKey(errorHowYouHearAboutUs);
    nationalityLabel =
        _localizationProvider.resources.getWithKey(FORMS_NATIONALITY);

    _errorsStudy = {
      _levelLabel: false,
      _curriculumLabel: false,
      _yearLabel: false,
    };
    _errorsPersonal = {
      _typeLabel: false,
      _genderLabel: false,
      _fullNameLabel: false,
      _emailLabel: false,
      sourceLabel: false,
    };

    _checkStudentDataAndSave();
    _checkDataAndSelection();
  }
  //
  // void _onTypeChange(int? value) {
  //   setState(() => _selectedType = value);
  //   _validatePerson();
  // }

  void _onGenderChange(int? value) {
    setState(() => _selectedGenderIndex = value);
    _validatePerson();
  }

  List<String> _getYearsList() {
    if (_selectedLevel == null) return [];
    if (_selectedLevel == 4) return _collegeYears;
    return _years;
  }

  void _onYearChange(int? value) {
    setState(() => _selectedYear = value);
    _validateStudy();
  }

  Future<void> _submit() async {
    if (isRegisteredUser) {
      if (getIt<Storage>().isComeRegistered) {
        final notificationEnabled =
            await context.read<CollectDataProvider>().getNotificationsAllowed();
        if (notificationEnabled) {
          _updateAdditionalInfo();
          return;
        }
        widget.arguments?.fromUpdate = true;
        await NavigationService.instance.navigateTo(
          NotificationsPermissionScreen.routeName,
          args: widget.arguments,
        );
        await _updateAdditionalInfo();
      } else {
        await _updateAdditionalInfo();
      }
    } else {
      if ((!_validatePerson()) && (!_validateStudy())) return;
      if (getIt<Storage>().isComeRegistered) {
        final notificationEnabled =
            await context.read<CollectDataProvider>().getNotificationsAllowed();
        if (notificationEnabled) {
          await _registerUserFirstStep();
          return;
        }
        widget.arguments?.fromUpdate = true;
        await NavigationService.instance.navigateTo(
          NotificationsPermissionScreen.routeName,
          args: widget.arguments,
        );
        await _registerUserFirstStep();
      } else {
        await _registerUserFirstStep();
      }
    }
  }

  bool _validatePerson() {
    _errorsPersonal[_typeLabel] = false;
    _errorsPersonal[_genderLabel] = false;
    _errorsPersonal[_fullNameLabel] = false;
    _errorsPersonal[_emailLabel] = false;
    _errorsPersonal[sourceLabel] = false;
    _errorsPersonal[nationalityLabel] = false;
    if (Validator.isValidName(
            _fullNameController.text, _localizationProvider.locals.language) !=
        null) {
      _errorsPersonal[_fullNameLabel] = true;
    }
    if (Validator.isEmail(
            _emailController.text, _localizationProvider.locals.language) !=
        null) {
      _errorsPersonal[_emailLabel] = true;
    }
    // if (_selectedType == null) _errorsPersonal[_typeLabel] = true;
    if (_selectedGenderIndex == null) _errorsPersonal[_genderLabel] = true;
    if (_nationalityController.text.isEmpty ||
        _nationalityController.text == pleaseSelectSource.translate()) {
      _errorsPersonal[nationalityLabel] = true;
    }
    if (_selectedSource == null) _errorsPersonal[sourceLabel] = true;
    if (_errorsPersonal.containsValue(true)) {
      validatePersonalInfo = false;

      setState(() {});
      return false;
    }

    validatePersonalInfo = true;

    setState(() {});
    return true;
  }

  bool _validateStudy() {
    _errorsStudy[_yearLabel] = false;
    if (_selectedYear == null && _showYears()) _errorsStudy[_yearLabel] = true;
    if (_errorsStudy.containsValue(true)) {
      validateStudyInfo = false;
      setState(() {});
      return false;
    }
    validateStudyInfo = true;
    setState(() {});
    return true;
  }

  bool _showYears() {
    if (_selectedLevel == null) return false;
    final level = _levels?.firstWhereOrNull((l) => l.id == _selectedLevel);
    if ((level.numOfYears ?? 0) > 0) return true;
    return false;
  }

  Future<StudentRegisterDTO> _generateDTO() async {
    final token = await Shard().getToken();
    final phoneCountryCode = StaticVar.countryCodes?.firstWhereOrNull(
        (element) => element.id == getIt<Storage>().countryCodeLogin);
    final timeZone = StaticVar.timeZones?.firstWhereOrNull(
        (element) => element.countryCode == defaultCountryCode?.isoCode);
    final language = _localizationProvider.locals.languageId;
    final nationality = (_nationalityController.text).trim().isNotEmpty
        ? nationalities?.firstWhereOrNull(
            (nationality) => nationality.name == _nationalityController.text)
        : null;
    return StudentRegisterDTO(
      languageId: language,
      countryCodeId: phoneCountryCode?.id,
      timeZoneId: timeZone?.id ?? 1780,
      placeOrResidenceCountryId: defaultCountryCode?.id,
      currencyId: Shard().currencyId,
      fullName: _fullNameController.text,
      email: _emailController.text,
      phoneNumber: getIt<Storage>().whatsappNumber,
      deviceToken: token,
      whatsappCode: getIt<Storage>().confirmationCode,
      pinCode: getIt<Storage>().confirmationCode,
      additionalInfoDTO: _generateAdditionalInfo(),
      studentSourceId: _selectedSource,
      nationality: nationality != null ? nationality.id : -1,
    );
  }

  StudentAdditionalInfoDTO _generateAdditionalInfo() {
    final nationality = (_nationalityController.text ?? '').trim().isNotEmpty
        ? nationalities?.firstWhereOrNull(
            (nationality) => nationality.name == _nationalityController.text)
        : null;
    return StudentAdditionalInfoDTO(
      userTypeId: getIt<Storage>().role == Roles.STUDENT ? 1 : 3,
      genderId: _genders?[_selectedGenderIndex ?? 0].id,
      levelId: _selectedLevel,
      curriculumId: _selectedCurriculum,
      year: _selectedYear != null ? ((_selectedYear ?? 0) + 1) : null,
      placeOrResidenceCountryId: defaultCountryCode?.id,
      studentSourceId: _selectedSource,
      email: _emailController.text,
      nationality: nationality != null ? nationality.id : -1,
    );
  }

  Widget _bottom() {
    return StickyBottomAppBar(
      child: _confirmButton(),
    );
  }

  Widget _confirmButton() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CustomButtonWidget(
          colorButton: ThemeColors.color26467A,
          margin: EdgeInsetsDirectional.only(
            start: 16.w,
            end: 16.w,
          ),
          padding: EdgeInsetsDirectional.only(
            top: 14.sp,
            bottom: 14.sp,
          ),
          onPressed: _onClickNext,
          title: BTN_NEXT.translate(),
          titleColor: ThemeColors.white,
          fontWeightTitle: FontWeight.w600,
          sizeTitle: 17,
          enabled: _additionalInfoTypeSelected == AdditionalInfoType.personal
              ? validatePersonalInfo
              : validateStudyInfo,
        ),
      ],
    );
  }

  void _onClickNext() {
    if (_additionalInfoTypeSelected == AdditionalInfoType.personal) {
      final personValidation = _validatePerson();
      final studyValidation = _validateStudy();
      if (personValidation && studyValidation) {
        _submit.call();
      } else {
        _additionalInfoTypeSelected = AdditionalInfoType.study;
      }
      setState(() {});
    } else {
      final studyValidation = _validateStudy();
      if (studyValidation) {
        _submit.call();
      }
      setState(() {});
    }
  }

  Widget _title() {
    return CustomTextWidget(
      title: _additionalInfoTypeSelected == AdditionalInfoType.personal
          ? personalInfoTitle.translate()
          : studyInfoTitle.translate(),
      size: 15,
      fontWeight: FontWeight.w500,
      color: ThemeColors.black,
    );
  }

  _fullName() {
    return Visibility(
      visible: _additionalInfoTypeSelected == AdditionalInfoType.personal,
      replacement: const SizedBox.shrink(),
      child: Container(
        margin: EdgeInsetsDirectional.only(
          top: 10.sp,
        ),
        padding: EdgeInsetsDirectional.only(
          start: 15.w,
          top: 8.sp,
          end: 15.w,
          bottom: 8.sp,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: ThemeColors.white,
          border: Border.all(
            color: ThemeColors.colorF2F2F7,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomTextWidget(
              isRequired: true,
              title: STUDENT_FORMS_FULLNAME.translate(),
              color: ThemeColors.black,
              size: 12,
              fontWeight: FontWeight.w400,
            ),
            TextField(
              controller: _fullNameController,
              inputFormatters: [
                FilteringTextInputFormatter.deny(RegExp('[0-9|\u0660-\u0669]'))
              ],
              onChanged: (_) {
                _validatePerson();
              },
              decoration: InputDecoration(
                isCollapsed: true,
                contentPadding: EdgeInsetsDirectional.zero,
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                hintText: STUDENT_FORMS_FULLNAME.translate(),
                labelStyle: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 13.sp,
                  color: ThemeColors.black,
                ),
                hintStyle: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 13.sp,
                  color: ThemeColors.black.withOpacity(0.3),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  _email() {
    return Visibility(
      visible: _additionalInfoTypeSelected == AdditionalInfoType.personal,
      replacement: const SizedBox.shrink(),
      child: Container(
        margin: EdgeInsetsDirectional.only(
          top: 10.sp,
        ),
        padding: EdgeInsetsDirectional.only(
          start: 15.w,
          top: 8.sp,
          end: 15.w,
          bottom: 8.sp,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: ThemeColors.white,
          border: Border.all(
            color: ThemeColors.colorF2F2F7,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomTextWidget(
              isRequired: true,
              title: STUDENT_FORMS_EMAIL.translate(),
              color: ThemeColors.black,
              size: 12,
              fontWeight: FontWeight.w400,
            ),
            TextField(
              controller: _emailController,
              onChanged: (_) {
                _validatePerson();
              },
              decoration: InputDecoration(
                isCollapsed: true,
                contentPadding: EdgeInsetsDirectional.zero,
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                hintText: STUDENT_FORMS_EMAIL.translate(),
                labelStyle: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 13.sp,
                  color: ThemeColors.black,
                ),
                hintStyle: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 13.sp,
                  color: ThemeColors.black.withOpacity(0.3),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCountries() {
    return Visibility(
      visible: _additionalInfoTypeSelected == AdditionalInfoType.personal,
      replacement: const SizedBox.shrink(),
      child: CustomSelectionCountry(
        defaultCountry: defaultCountryCode,
        selectionCountryCode: _selectionCountryCode,
      ),
    );
  }

  Widget _personalInfoContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _title(),
        if (nameExists.inverted) _fullName(),
        if (nameExists.inverted) _buildError(_fullNameLabel),
        if (nationalityExists.inverted) SizedBox(height: 10.sp),
        if (nationalityExists.inverted) _buildNationality(),
        if (nationalityExists.inverted) _buildError(nationalityLabel),
        if (emailExists.inverted) _email(),
        if (emailExists.inverted) _buildError(_emailLabel),
        // if (typeExists.inverted) _buildType(),
        // if (typeExists.inverted) _buildError(_typeLabel),
        if (genderExists.inverted) SizedBox(height: 10.sp),
        if (genderExists.inverted) _buildGender(),
        if (genderExists.inverted) _buildError(_genderLabel),
        if (countryExists.inverted) SizedBox(height: 10.sp),
        if (countryExists.inverted) _buildCountries(),
        if (howHearAboutUsExists.inverted) SizedBox(height: 20.sp),
        if (howHearAboutUsExists.inverted) _titleHowYouHearAboutUs(),
        if (howHearAboutUsExists.inverted) SizedBox(height: 10.sp),
        if (howHearAboutUsExists.inverted) _buildHowYouHearAboutUs(),
        if (howHearAboutUsExists.inverted)
          _buildError(sourceLabel,
              error: errorHowYouHearAboutUs.translate() ?? ''),
      ],
    );
  }

  Widget _studyInfoContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsetsDirectional.only(
            start: 16.w,
            end: 16.w,
          ),
          child: _title(),
        ),
        SizedBox(height: 10.sp),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _getNumOfYears() ?? 0,
          itemBuilder: _itemBuilder,
          separatorBuilder: _separatorBuilder,
        ),
        SizedBox(height: 10.sp),
        Padding(
          padding: EdgeInsetsDirectional.only(
            start: 16.w,
            end: 16.w,
          ),
          child: _buildError(_yearLabel),
        ),
      ],
    );
  }

  int? _getNumOfYears() {
    return (_levels ?? []).firstWhere((l) => l.id == _selectedLevel).numOfYears;
  }

  Widget? _itemBuilder(BuildContext context, int index) {
    String? item = _getYearsList()[index];
    final id = _getYearsList().indexWhere((element) => element == item);
    bool selected = id == _selectedYear;
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => _onClickItem(item),
      child: _itemSelection(
        item,
        selected,
      ),
    );
  }

  Widget _separatorBuilder(BuildContext context, int index) {
    return SizedBox(height: 12.sp);
  }

  void _onClickItem(String? item) {
    final id = _getYearsList().indexWhere((element) => element == item);
    _onYearChange.call(id);
    setState(() {});
  }

  void _onClickBackAppBar() {
    if (_additionalInfoTypeSelected != AdditionalInfoType.personal) {
      _additionalInfoTypeSelected = AdditionalInfoType.personal;
      _validatePerson();
      setState(() {});
    } else {
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      }
    }
  }

  Widget _itemSelection(String name, bool selected,
      {bool keepSelection = true}) {
    return Column(
      children: [
        if (keepSelection) _dividerSelection(selected),
        Container(
          color: keepSelection
              ? selected
                  ? ThemeColors.color26467A.withOpacity(0.05)
                  : ThemeColors.white
              : ThemeColors.white,
          child: Padding(
            padding: EdgeInsetsDirectional.only(
              start: keepSelection ? 16.w : 0,
              end: 16.w,
              top: 7.w,
              bottom: 7.w,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SvgPicture.asset(
                  selected
                      ? Images.radioSelectedIcon
                      : Images.radioNotSelectedIcon,
                  width: 24.sp,
                  height: 24.sp,
                  color: selected ? ThemeColors.color26467A : null,
                ),
                SizedBox(width: 10.w),
                CustomTextWidget(
                  title: name,
                  fontWeight: FontWeight.w400,
                  size: 14,
                  color: ThemeColors.color1C1C1E,
                )
              ],
            ),
          ),
        ),
        if (keepSelection) _dividerSelection(selected),
      ],
    );
  }

  _dividerSelection(bool selected) {
    return Divider(
      thickness: 1,
      height: 1,
      color: selected ? ThemeColors.color26467A : ThemeColors.grayE5E5EA,
    );
  }

  Widget _titleHowYouHearAboutUs() {
    return CustomTextWidget(
      title: howYouHearAboutUs.translate(),
      size: 15,
      fontWeight: FontWeight.w500,
      color: ThemeColors.black,
    );
  }

  Widget _buildHowYouHearAboutUs() {
    return GestureDetector(
      onTap: onOptionPressed,
      child: Container(
        padding: EdgeInsetsDirectional.all(13.sp),
        decoration: BoxDecoration(
          color: ThemeColors.white,
          border: Border.all(color: ThemeColors.grayE5E5EA, width: 1),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextWidget(
                  title: _itemSelectedSource?.name ??
                      pleaseSelectSource.translate(),
                  size: 13,
                  fontWeight: FontWeight.w400,
                  color: ThemeColors.black,
                ),
              ],
            ),
            const Icon(
              Icons.expand_more,
              color: ThemeColors.black,
            )
          ],
        ),
      ),
    );
  }

  Future<void> onOptionPressed() async {
    final sheet = GenericListingSheet<StudentExtrnalSource>(
      selectedColor: ThemeColors.color26467A.withOpacity(0.10),
      title: pleaseSelectSource.translate() ?? '',
      display: GenericListingItemViewBuilder.text((e) => e.name ?? ''),
      equalityCheck: (a, b) => a?.id == b?.id,
      allowMultipleSelection: false,
      items: studentExternalSourceList,
      totalCount: studentExternalSourceList.length,
      selectedItems: _itemSelectedSource != null ? [_itemSelectedSource] : [],
      heightFactor: 1,
      onClickClose: _onClickClose,
      onSubmit: _onSubmit,
      colorConfirm: ThemeColors.color26467A,
    );
    await getIt<IShowBottomSheetHelper>().showBottomSheet<StudentExtrnalSource>(
      ShowBottomSheetInput(
        isScrollControlled: false,
        sheet,
      ),
    );
  }

  void _onSubmit(List<StudentExtrnalSource?> items) {
    Navigator.pop(context);
    log(items.firstOrNull?.name ?? '');
    _selectedSource = items.firstOrNull?.id;
    _itemSelectedSource = items.firstOrNull;
    setState(() {});
    _validatePerson();
  }

  void _onClickCloseAppBar() {
    context.read<AuthenticationProvider>().clearController();
    NavigationService.instance.navigateToAndRemove(StudentHomeScreen.routeName);
  }

  Future<void> _checkDataAndSelection() async {
    _selectedLevel = widget.arguments?.firstInstallationArguments?.levelId;
    _selectedCurriculum =
        widget.arguments?.firstInstallationArguments?.curriculmsId;
    if (isRegisteredUser) {
      final student = context.read<StudentProfileProvider>().student;
      _fullNameController.text = student?.name ?? '';
      final checkValue = RegExp(
              r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
          .hasMatch(student?.email ?? '');
      _emailController.text = checkValue ? student?.email ?? '' : '';

      if ((student?.nationalityId != null &&
              student?.nationalityId != -1 &&
              student?.nationalityId != 0) ||
          (student?.additionalInfo?.nationalityId != null &&
              student?.additionalInfo?.nationalityId != -1 &&
              student?.additionalInfo?.nationalityId != 0)) {
        final nationality = nationalities?.firstWhereOrNull(
          (nationality) => ((nationality.id == student?.nationalityId) ||
              (nationality.id == student?.additionalInfo?.nationalityId)),
        );
        if (nationality != null) {
          _nationalityController.text = nationality.name ?? '';
        } else {
          final countryCode = StaticVar.countryCodes?.firstWhereOrNull(
              (element) =>
                  element.isoCode ==
                  (student?.countryCode ?? getIt<Storage>().countryCode));
          final defaultNationality = (nationalities)
              ?.firstWhereOrNull((e) => e.countryCode == countryCode?.isoCode);
          _nationalityController.text = defaultNationality?.name ??
              pleaseSelectSource.translate().orDefault;
        }
      } else {
        final countryCode = StaticVar.countryCodes?.firstWhereOrNull(
            (element) => element.isoCode == getIt<Storage>().countryCode);
        final defaultNationality = (nationalities)
            ?.firstWhereOrNull((e) => e.countryCode == countryCode?.isoCode);
        _nationalityController.text = defaultNationality?.name ??
            pleaseSelectSource.translate().orDefault;
      }
      if (student?.placeOrResidenceCountryId != null &&
          student?.placeOrResidenceCountryId != 0) {
        final countryCode = StaticVar.countryCodes?.firstWhereOrNull(
            (element) => element.id == student?.placeOrResidenceCountryId);

        defaultCountryCode = countryCode ?? StaticVar.countryCodes?.firstOrNull;
      } else {
        final countryCode = StaticVar.countryCodes?.firstWhereOrNull(
            (element) => element.isoCode == getIt<Storage>().countryCode);
        defaultCountryCode = countryCode ?? StaticVar.countryCodes?.firstOrNull;
      }
      _selectedGenderIndex =
          parseGender(_genders ?? [], student?.additionalInfo?.gender);
      // _selectedType = student?.additionalInfo?.type;
      _selectedYear = student?.additionalInfo?.year != 0
          ? student?.additionalInfo?.year
          : null;
      if ((student?.isPersonalInfoExists ?? false) && (_selectedLevel == 6)) {
        _submit();
      } else if ((student?.isPersonalInfoExists ?? false) &&
          (_selectedLevel != 6)) {
        _additionalInfoTypeSelected = AdditionalInfoType.study;
        _validateStudy();
      } else {
        _additionalInfoTypeSelected = AdditionalInfoType.personal;
        _validatePerson();
      }

      if (student?.source != null && student?.source != 0) {
        _selectedSource = student?.source;
        _itemSelectedSource = studentExternalSourceList
            .firstWhereOrNull((element) => element.id == student?.source);
      }
      // check field exists
      nameExists =
          (student?.name ?? '').isNotEmpty && (student?.name ?? '').length > 3;

      emailExists = checkValue ? (student?.email ?? '').isNotEmpty : false;
      // typeExists = student?.additionalInfo?.type != null &&
      //     student?.additionalInfo?.type != 0;
      genderExists = student?.additionalInfo?.gender != null;
      nationalityExists = (student?.nationalityId != null &&
              student?.nationalityId != -1 &&
              student?.nationalityId != 0) ||
          (student?.additionalInfo?.nationalityId != null &&
              student?.additionalInfo?.nationalityId != -1 &&
              student?.additionalInfo?.nationalityId != 0);

      countryExists = student?.placeOrResidenceCountryId != null &&
          student?.placeOrResidenceCountryId != 0;
      howHearAboutUsExists = student?.source != null && student?.source != 0;

      _validatePerson();
    } else {
      final countryCode = StaticVar.countryCodes?.firstWhereOrNull(
          (element) => element.isoCode == getIt<Storage>().countryCode);
      defaultCountryCode = countryCode ?? StaticVar.countryCodes?.firstOrNull;
      final defaultNationality = (nationalities)
          ?.firstWhereOrNull((e) => e.countryCode == countryCode?.isoCode);
      _nationalityController.text =
          defaultNationality?.name ?? pleaseSelectSource.translate().orDefault;
      final source = _authenticationProvider.studentDataWizard?.studentSourceId;
      if (source != null && source != 0) {
        _selectedSource = source;
        _itemSelectedSource = studentExternalSourceList
            .firstWhereOrNull((element) => element.id == source);
      }
      howHearAboutUsExists = source != null && source != 0;
      _validatePerson();
    }

    setState(() {});
  }

  bool get isRegisteredUser => getIt<Storage>().isLogin;

  Future<void> _updateAdditionalInfo() async {
    ProgressIndicators.loadingDialog(context);

    final timeZone = StaticVar.timeZones?.firstWhereOrNull(
      (element) => element.countryCode == defaultCountryCode?.isoCode,
    );

    final dto = _generateAdditionalInfo();
    try {
      final accessToken = context.read<AuthenticationProvider>().accessToken;
      final languageId = _localizationProvider.locals.languageId;
      final studentProvider = context.read<StudentProfileProvider>();
      await studentProvider.updateAdditionalInfo(accessToken, dto, languageId);
      final student = studentProvider.student;
      final nationality = (_nationalityController.text ?? '').trim().isNotEmpty
          ? nationalities?.firstWhereOrNull(
              (nationality) => nationality.name == _nationalityController.text)
          : null;
      if (student != null) {
        student.name = _fullNameController.text;
        student.placeOrResidenceCountryId = defaultCountryCode?.id;
        student.timezone = timeZone?.id ?? 1780;
        student.email = _emailController.text;
        student.source = _selectedSource;
        student.nationalityId = nationality != null ? nationality.id : -1;
        await studentProvider.updateProfile(
          accessToken,
          student,
          Shard().languageId,
        );
      }
      getIt<Storage>().registrationDataWizard = true;
      Navigator.pop(context);

      _goToRouteSpecific(context);
    } catch (error) {
      Navigator.pop(context);
      final errorException = ErrorParser().parseError(error);
      Snackbars.danger(context, errorException.message);
    }
  }

  Future<void> _goToRouteSpecific(BuildContext context) async {
    Shard().doSearch(widget.arguments?.firstInstallationArguments);
    if (widget.arguments?.comeFromOtherPlace ?? false) {
      if (widget.arguments?.routeScreen == ConversationScreen.routeName) {
        NavigationService.instance.navigateToAndRemove(
          StudentHomeScreen.routeName,
          args: {
            'PAGE_INDEX': 3,
          },
        );
      } else if (widget.arguments?.routeScreen ==
          StudentLessonsScreen.routeName) {
        NavigationService.instance.navigateToAndRemove(
          StudentHomeScreen.routeName,
          args: {
            'PAGE_INDEX': 1,
          },
        );
      } else {
        NavigationService.instance.navigateToAndRemove(
          widget.arguments?.routeScreen ?? StudentHomeScreen.routeName,
        );
      }
    } else {
      goToMessageOrLessons();
    }
  }

  Future<void> goToMessageOrLessons() async {
    if (isRegisteredUser) {
      final messageProvider = context.read<MessagesProvider>();
      final authProvider = context.read<AuthenticationProvider>();
      await messageProvider.getUserChat(authProvider.accessToken, 1);
      if ((messageProvider.conversationMessages ?? []).isNotEmpty) {
        NavigationService.instance
            .navigateToAndRemove(StudentHomeScreen.routeName, args: {
          'PAGE_INDEX': 3,
        });
      } else {
        NavigationService.instance
            .navigateToAndRemove(StudentHomeScreen.routeName, args: {
          'PAGE_INDEX': 1,
        });
      }
    } else {
      NavigationService.instance
          .navigateToAndRemove(StudentHomeScreen.routeName);
    }
  }

  int? parseGender(List? genders, int? gender) {
    int? genderIndex;
    for (int i = 0; i < (_genders ?? []).length; i++) {
      if (gender == _genders?[i].id) {
        genderIndex = i;
      }
    }
    return genderIndex;
  }

  void _checkStudentDataAndSave() {
    final authProvider = context.read<AuthenticationProvider>();
    if (authProvider.isLoggedIn.inverted &&
        (getIt<Storage>().whatsappNumber ?? '').isNotEmpty) {
      final data =
          widget.arguments?.firstInstallationArguments?.toSearchListDTO();
      if (data != null) {
        context.read<SearchProvider>().saveData(data);
      }
    }
  }

  Future<void> _registerUserFirstStep() async {
    ProgressIndicators.loadingDialog(context);
    final dto = await _generateDTO();
    widget.arguments?.studentRegisterDTO = dto;
    Navigator.pop(context);
    Navigator.pushNamed(
      context,
      StudentPromisePage.routeName,
      arguments: widget.arguments,
    );
  }

  void _selectionCountryCode(CountryCode? countryCode) {
    setState(() {
      defaultCountryCode = countryCode;
      _validatePerson();
    });
  }

  void _onClickClose() {
    Navigator.pop(context);
  }

  Widget _buildNationality() {
    final options =
        (nationalities ?? []).map((Nationality e) => e.name ?? '').toList();

    return ProfileSelectBox(
      label: nationalityLabel,
      colorBorder: ThemeColors.colorF2F2F7,
      options: options,
      onSelect: () {
        _validatePerson();
      },
      controller: _nationalityController,
    );
  }
}

class ComboBoxSelection extends StatefulWidget {
  const ComboBoxSelection({
    Key? key,
    this.title,
    this.selectionTitle,
    this.enabled,
    this.padding,
    required this.onClick,
    this.selectionId,
  }) : super(key: key);
  final String? title;
  final String? selectionTitle;
  final int? selectionId;
  final bool? enabled;
  final EdgeInsets? padding;
  final void Function() onClick;
  @override
  State<ComboBoxSelection> createState() => _ComboBoxSelectionState();
}

class _ComboBoxSelectionState extends State<ComboBoxSelection> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onClick,
      child: Container(
        padding: widget.padding ?? EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: ThemeColors.grayE5E5EA,
          ),
          color: (widget.enabled ?? false)
              ? ThemeColors.white
              : ThemeColors.colorF2F2F7,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                CustomTextWidget(
                  title: widget.title ?? '',
                  fontWeight: FontWeight.w400,
                  color: ThemeColors.black,
                  isRequired: true,
                  size: 12,
                ),
                CustomTextWidget(
                  title: widget.selectionTitle ?? '',
                  fontWeight: FontWeight.w400,
                  color: ThemeColors.color8E8E93,
                  size: 13,
                )
              ],
            ),
            const Icon(Icons.expand_more_rounded),
          ],
        ),
      ),
    );
  }
}
