import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:modarby/Enums/Roles.dart';
import 'package:modarby/Enums/URLS.dart';
import 'package:modarby/Instructor/Screens/Instructor_home.dart';
import 'package:modarby/Instructor/Screens/Registration/SignupAbout.dart';
import 'package:modarby/Instructor/Screens/Registration/SignupMissing.dart';
import 'package:modarby/Instructor/Screens/Registration/signup_additional_page/signup_instructor_additional_argument.dart';
import 'package:modarby/Instructor/Screens/Registration/signup_additional_page/start_instructor_need_more_info.dart';
import 'package:modarby/Models/InstructorProfile/GetProfileDTO.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/CollectDataProvider.dart';
import 'package:modarby/Providers/InstructorProfileProvider.dart';
import 'package:modarby/Providers/MessagesProvider.dart';
import 'package:modarby/Providers/StudentProfileProvider.dart';
import 'package:modarby/SharedScreens/NotificationsPermission.dart';
import 'package:modarby/Student/Screens/Home.dart';
import 'package:modarby/Widgets/custom_button_widget.dart';
import 'package:modarby/Widgets/custom_text_widget.dart';
import 'package:modarby/Widgets/image_loading.dart';
import 'package:modarby/Widgets/sticky_bottom_appbar.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/config/themes/images.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';
import 'package:modarby/core/utilities/loading_indicator.dart';
import 'package:modarby/core/utilities/navigation_service.dart';
import 'package:modarby/core/utilities/routing_user.dart';
import 'package:modarby/core/utilities/shard.dart';
import 'package:modarby/core/utilities/storage.dart';
import 'package:modarby/features/auth/arguments/auth_argument.dart';
import 'package:modarby/features/live_tutors/tutors/presentation/screens/live_notifications_tutor_screen.dart';
import 'package:provider/provider.dart';

import '../../../Instructor/Screens/BillingOptions.dart';
import '../../../Models/live_tutors/PendingRequests.dart';
import '../../../Providers/emergency_settings_provider.dart';
import '../../../Referral/Screens/Registration/SignupAbout.dart';

class WelcomeBackPage extends StatefulWidget {
  static const routeName = '/WelcomeBackPage';
  final AuthArgument? argument;
  const WelcomeBackPage({Key? key, this.argument}) : super(key: key);

  @override
  State<WelcomeBackPage> createState() => _WelcomeBackPageState();
}

class _WelcomeBackPageState extends State<WelcomeBackPage> {
  @override
  void initState() {
    if ((studentDataWizard &&
            (getIt<Storage>().isComeFromSkip ||
                getIt<Storage>().isComeFromGetStarted ||
                registrationDataWizard) &&
            isStudent) ||
        isStudent.inverted) {
      _goToHome();
    }
    super.initState();
  }

  late EmergencySettingsProvider _emergencyProvider;
  List<PendingRequests>? _pendingRequests;

  @override
  Widget build(BuildContext context) {
    final studentProfileProvider = context.watch<StudentProfileProvider>();
    _watchers();
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        body: _body(studentProfileProvider),
        bottomNavigationBar: _bottom(studentProfileProvider),
      ),
    );
  }

  Widget _body(StudentProfileProvider studentProfileProvider) {
    return SafeArea(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  _image(),
                  SizedBox(height: 3.sp),
                  _welcomeBackTitle(),
                  SizedBox(height: 40.sp),
                  _nameUser(),
                  SizedBox(height: 40.sp),
                  _collectSomeDataMessage(),
                ],
              ),
            ),
            _loader(),
            SizedBox(height: 40.sp),
          ],
        ),
      ),
    );
  }

  Widget _image() {
    return Stack(
      children: [
        SizedBox(
          height: 76.sp,
          width: 72.w,
          child: Container(
            width: 72.sp,
            height: 72.sp,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.sp),
                border: Border.all(
                  color: ThemeColors.colorF2F2F7,
                )),
            child: ImageLoading(
              fit: BoxFit.cover,
              borderRadius: 10,
              imageUrl: _getImageUrl(context),
              width: 72.sp,
              height: 72.sp,
              placeHolderWidget: SvgPicture.asset(
                Images.personAuthIcon,
                width: 72.w,
                height: 72.sp,
                fit: BoxFit.cover,
              ),
              errorWidget: SvgPicture.asset(
                Images.personAuthIcon,
                width: 72.w,
                height: 72.sp,
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
        PositionedDirectional(
          bottom: 0,
          end: 0,
          child: SvgPicture.asset(
            Images.personGreenAuthIcon,
            width: 18.w,
            height: 18.w,
          ),
        ),
      ],
    );
  }

  Widget _welcomeBackTitle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const CustomTextWidget(
          title: '👋 ',
          fontWeight: FontWeight.w700,
          size: 28,
          color: ThemeColors.black,
        ),
        CustomTextWidget(
          title: welcomeBackTitle.translate(),
          fontWeight: FontWeight.w700,
          textAlign: TextAlign.center,
          size: 28,
          color: ThemeColors.black,
        ),
      ],
    );
  }

  Widget _nameUser() {
    final name = getIt<Storage>().displayNameWelcomePage;
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CustomTextWidget(
          title: name ?? '',
          fontWeight: FontWeight.w400,
          size: 16,
          color: ThemeColors.color1C1C1E,
        ),
      ],
    );
  }

  Widget _loader() {
    return Visibility(
      visible: isStudent.inverted ||
          (studentDataWizard &&
              (getIt<Storage>().isComeFromSkip ||
                  getIt<Storage>().isComeFromGetStarted ||
                  registrationDataWizard) &&
              isStudent),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 36.w,
            height: 36.w,
            alignment: Alignment.center,
            child: const CircularProgressIndicator(
              color: ThemeColors.accentColor,
              backgroundColor: ThemeColors.colorF2F2F7,
            ),
          ),
        ],
      ),
    );
  }

  void _goToHome() async {
    await _loadProfile();
    await _loadMessages();
    await _profileNavigation();
  }

  // Future<void> _loadData() async {
  //   final autProvider = context.read<AuthenticationProvider>();
  //   _emergencyProvider = context.read<EmergencySettingsProvider>();
  //   await _emergencyProvider.getPendingRequests(context.read<AuthenticationProvider>().accessToken);
  // }
  void _watchers() {
    _pendingRequests = context.watch<EmergencySettingsProvider>().listRequests;
  }

  Future<void> _loadProfile() async {
    if (isStudent) {
      await context.read<StudentProfileProvider>().getProfile(
            context.read<AuthenticationProvider>().accessToken,
            Shard().languageId,
          );
    } else if (isStudent.inverted) {
      _emergencyProvider = context.read<EmergencySettingsProvider>();
      await _emergencyProvider.getPendingRequest();
      _pendingRequests = context.read<EmergencySettingsProvider>().listRequests;
      final dto = _generateInstructorProfileDTO();
      await context.read<InstructorProfileProvider>().getProfile(dto);
    }
  }

  GetProfileDTO _generateInstructorProfileDTO() {
    return GetProfileDTO(
      accessToken: context.read<AuthenticationProvider>().accessToken,
      languageId: Shard().languageId,
      currencyId: Shard().currencyId,
    );
  }

  Future<void> _loadMessages() async {
    await context
        .read<MessagesProvider>()
        .getUserChat(context.read<AuthenticationProvider>().accessToken, 1);
  }

  bool get studentDataWizard => getIt<Storage>().studentDataWizard;

  bool get registrationDataWizard => getIt<Storage>().registrationDataWizard;
  bool get isStudent =>
      getIt<Storage>().role == Roles.STUDENT ||
      getIt<Storage>().role == Roles.PARENT;
  Future<void> _profileNavigation() async {
    final notificationEnabled =
        await context.read<CollectDataProvider>().getNotificationsAllowed();

    if (notificationEnabled) {
      checkRoutingNavigation();
    } else {
      if (studentDataWizard && registrationDataWizard) {
        widget.argument?.fromUpdate = true;
        await NavigationService.instance.navigateTo(
          NotificationsPermissionScreen.routeName,
          args: widget.argument,
        );
        checkRoutingNavigation();
      } else {
        widget.argument?.fromUpdate = true;
        await NavigationService.instance.navigateTo(
          NotificationsPermissionScreen.routeName,
          args: widget.argument,
        );
        checkRoutingNavigation();
      }
    }
  }

  void goToMessageOrLessons() {
    final messageProvider = context.read<MessagesProvider>();
    if ((messageProvider.conversationMessages ?? []).isNotEmpty) {
      NavigationService.instance
          .navigateToAndRemove(StudentHomeScreen.routeName, args: {
        'PAGE_INDEX': 3,
      });
    } else {
      NavigationService.instance
          .navigateToAndRemove(StudentHomeScreen.routeName, args: {
        'PAGE_INDEX': 1,
      });
    }
  }

  _collectSomeDataMessage() {
    return Visibility(
      visible: isStudent &&
          (studentDataWizard.inverted ||
              (getIt<Storage>().isComeFromSkip ||
                      getIt<Storage>().isComeFromGetStarted ||
                      registrationDataWizard)
                  .inverted),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
            child: CustomTextWidget(
              title: weNeedToCollectSomeData.translate(),
              fontWeight: FontWeight.w700,
              textAlign: TextAlign.center,
              paddingStart: 16.w,
              paddingEnd: 16.w,
              maxLine: 3,
              size: 20,
              color: ThemeColors.black,
            ),
          ),
        ],
      ),
    );
  }

  String _getImageUrl(BuildContext context) {
    if (StaticVar.isStudent) {
      final student = context.read<StudentProfileProvider>().student;
      final studentPhoto =
          '${URLS().STUDENTS_IMAGES_URL}/${student?.photoName ?? ''}';
      return studentPhoto;
    } else {
      final instructor = context.read<InstructorProfileProvider>().instructor;
      final studentPhoto =
          '${URLS().INSTRUCTORS_IMAGES_URL}/${instructor?.picture}';
      return studentPhoto;
    }
  }

  _bottom(StudentProfileProvider studentProfileProvider) {
    return Visibility(
      visible: isStudent &&
          (studentDataWizard.inverted ||
              (getIt<Storage>().isComeFromSkip ||
                      getIt<Storage>().isComeFromGetStarted ||
                      registrationDataWizard)
                  .inverted),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          StickyBottomAppBar(
              child: CustomButtonWidget(
            colorButton: ThemeColors.color26467A,
            padding: EdgeInsetsDirectional.only(
              top: 14.sp,
              bottom: 14.sp,
            ),
            title: BTN_NEXT.translate(),
            titleColor: ThemeColors.white,
            onPressed: () {
              LoadingIndicator.show(context);
              _goToHome();
            },
          )),
        ],
      ),
    );
  }

  Future<void> checkRoutingNavigation() async {
    if (isStudent) {
      await RoutingUser().firstLogin(
        widget.argument,
        StaticVar.context,
        goToWelcome: false,
        showLoader: false,
      );
    } else {
      Navigator.pushNamedAndRemoveUntil(
        StaticVar.context,
        InstructorHomeScreen.routeName,
        (_) => false,
      );
      if (StaticVar.context.read<InstructorProfileProvider>().instructor!.isVerified! &&
          !StaticVar.context
              .read<InstructorProfileProvider>()
              .instructor!
              .isPreferencesUpdated! &&
          StaticVar.context.read<InstructorProfileProvider>().instructor!.isMarketing ==
              false) {
        final signupInstructorAdditionalArgument =
            SignupInstructorAdditionalArgument(fromUpdate: true);
        Navigator.pushNamedAndRemoveUntil(
          StaticVar.context,
          StartInstructorNeedMoreInfo.routeName,
          (_) => false,
          arguments: signupInstructorAdditionalArgument,
        );
      } else if (!StaticVar.context
              .read<InstructorProfileProvider>()
              .instructor!
              .isVerified! &&
          StaticVar.context.read<InstructorProfileProvider>().instructor!.name ==
              null &&
          StaticVar.context.read<InstructorProfileProvider>().instructor!.isMarketing ==
              true) {
        Navigator.pushNamedAndRemoveUntil(
          StaticVar.context,
          ReferralSignupAboutScreen.routeName,
          (_) => false,
          arguments: false,
        );
      } else if (!StaticVar.context
              .read<InstructorProfileProvider>()
              .instructor!
              .isVerified! &&
          StaticVar.context.read<InstructorProfileProvider>().instructor!.name ==
              null &&
          StaticVar.context.read<InstructorProfileProvider>().instructor!.isMarketing ==
              false) {
        Navigator.pushNamedAndRemoveUntil(
          StaticVar.context,
          InstructorSignupAboutScreen.routeName,
          (_) => false,
          arguments: true,
        );
      } else if (!StaticVar.context
              .read<InstructorProfileProvider>()
              .instructor!
              .isVerified! &&
          StaticVar.context
                  .read<InstructorProfileProvider>()
                  .instructor!
                  .isMarketing ==
              true &&
          StaticVar.context
                  .read<InstructorProfileProvider>()
                  .missing!
                  .isBillingInfoSaved ==
              false) {
        Navigator.pushNamedAndRemoveUntil(
          StaticVar.context,
          InstructorBillingOptionsScreen.routeName,
          arguments: {
            'backable': false,
            'isMarketing': true,
            'isProfile': false,
            'isRegister': false
          },
          (_) => false,
        );
      } else if (!StaticVar.context
              .read<InstructorProfileProvider>()
              .instructor!
              .isVerified! &&
          StaticVar.context
                  .read<InstructorProfileProvider>()
                  .instructor!
                  .isMarketing ==
              false) {
        Navigator.pushNamedAndRemoveUntil(
          StaticVar.context,
          InstructorSignupMissingsScreen.routeName,
          (_) => false,
        );
      } else if (StaticVar.context
              .read<InstructorProfileProvider>()
              .instructor!
              .isVerified! &&
          _pendingRequests != null &&
          _pendingRequests!.isNotEmpty) {
        context
            .read<EmergencySettingsProvider>()
            .getPendingRequestToStartTimer();
        Navigator.pushNamedAndRemoveUntil(
          StaticVar.context,
          LiveNotificationsTutorScreen.routeName,
          (_) => false,
        );
      }
    }
  }
}
