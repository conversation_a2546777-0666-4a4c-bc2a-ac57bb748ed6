import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/DependencyManager/TextProviders/TextProvider.dart';
import 'package:modarby/Widgets/custom_button_widget.dart';
import 'package:modarby/Widgets/custom_text_widget.dart';
import 'package:modarby/Widgets/do_you_need_help.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/config/themes/images.dart';
import 'package:modarby/core/extentions/iterables/iterable_first.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';
import 'package:modarby/features/auth/arguments/auth_argument.dart';
import 'package:provider/provider.dart';

import '../../../Widgets/PinCode.dart';
import '../widgets/custom_app_bar_auth_widget.dart';

class ConfirmCodePage extends StatefulWidget {
  static const routeName = '/ConfirmCodePage';
  final AuthArgument? arguments;
  const ConfirmCodePage({super.key, this.arguments});
  @override
  State<ConfirmCodePage> createState() => _ConfirmCodePageState();
}

class _ConfirmCodePageState extends State<ConfirmCodePage> {
  late TextProvider _textProvider;

  late LocalizationProvider _localizationProvider;
  late AuthenticationProvider _authProvider;
  List? _countries;

  bool _isCounting = true;

  String? _headerTitle;
  String? _headerSubtitle;

  String? _submitLabel;

  final _bodyMarginVertical = 20.0;
  final _numOfDigits = 4;
  Duration _duration = const Duration(seconds: 30);
  Timer? timer;

  final _isCountingController = TextEditingController();

  bool valueChanged = false;

  @override
  void initState() {
    _initializeTimer();
    _initializeData();
    _isCountingController.addListener(_handleCounting);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    _initializeWatcher();
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        body: _body(),
      ),
    );
  }

  Widget _buildImage() {
    return SvgPicture.asset(
      haveEmail ? Images.whatsAppAuthIcon : Images.whatsIcon,
      width: 119.sp,
      height: 81.sp,
    );
  }

  Widget _titleHeader() {
    return CustomTextWidget(
      title: _headerTitle ?? '',
      size: 28,
      fontWeight: FontWeight.w700,
      color: ThemeColors.black,
      textAlign: TextAlign.center,
      maxLine: 2,
      height: 3.4,
    );
  }

  Widget _buildCode() {
    return PinCode(
      numOfDigits: _numOfDigits,
      controller: _authProvider.codeController,
      autoFocus: true,
      onChanged: _onChangedPin,
      showError: _authProvider.showErrorCode,
    );
  }

  Widget buildTimer() {
    return GestureDetector(
      onTap: (_isCountingController.text == 'false')
          ? _onCLickSendAgain
          : () => {},
      child: Text.rich(TextSpan(
        text: formatDuration(_duration),
        style: TextStyle(
          fontSize: 13.sp,
          fontWeight: FontWeight.w400,
          color: ThemeColors.color1C1C1E,
        ),
        children: [
          TextSpan(text: ' ', children: [
            TextSpan(
              text: '${STUDENTREGISTER_CONFIRMATION_SENDAGAIN.translate()}',
              style: TextStyle(
                decoration: TextDecoration.underline,
                fontSize: 13.sp,
                fontWeight: FontWeight.w400,
                color: (_isCountingController.text == 'false')
                    ? ThemeColors.color007AFF
                    : ThemeColors.color8E8E93,
              ),
            )
          ])
        ],
      )),
    );
  }

  String formatDuration(Duration duration) {
    return [duration.inMinutes, duration.inSeconds]
        .map((seg) => seg.remainder(60).toString().padLeft(2, '0'))
        .join(':');
  }

  void _initializeTimer() {
    _isCountingController.text = 'true';
    _isCountingController.addListener(_resetTimer);
    startTimer();
  }

  void startTimer() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          if ((_duration.inSeconds) < 1) {
            timer.cancel();
            _isCountingController.text = 'false';
          } else {
            _duration = _duration - const Duration(seconds: 1);
          }
        });
      }
    });
  }

  void _resetTimer() {
    if (_isCountingController.text == 'true') {
      setState(() {
        startTimer();
      });
    }
  }

  bool get haveEmail =>
      (widget.arguments?.validateUserEntity?.isEmail ?? false) &&
      (widget.arguments?.validateUserEntity?.email ?? '').isNotEmpty;
  Future<void> _verify() async {
    _authProvider.confirmCode(widget.arguments, context);
  }

  void _handleCounting() {
    if (_isCountingController.text == 'false') {
      setState(() {
        _isCounting = false;
      });
    } else if (_isCountingController.text == 'true' && !_isCounting) {
      setState(() {
        _isCounting = true;
      });
    }
  }

  void _dismissKeyboard() {
    FocusScope.of(context).unfocus();
  }

  void _initializeWatcher() {
    _authProvider = context.watch<AuthenticationProvider>();
  }

  void _initializeData() {
    _textProvider = context.read<DependencyManager>().text;
    _localizationProvider = context.read<DependencyManager>().localization;

    _countries = _localizationProvider.countryCodes.getAll();

    _headerTitle = _localizationProvider.resources
        .getWithKey(STUDENTREGISTER_CONFIRMATION_TOPHEADER);
    _headerSubtitle = _localizationProvider.resources
        .getWithKey(STUDENTREGISTER_CONFIRMATION_SUBHEADER);
    _submitLabel = _localizationProvider.resources
        .getWithKey(STUDENTREGISTER_CONFIRMATION_CONFIRMCODE);
  }

  Widget _message() {
    return CustomTextWidget(
      title: haveEmail ? _headerSubtitle : notHaveEmailMessage.translate(),
      size: 15,
      textAlign: TextAlign.center,
      fontWeight: FontWeight.w400,
      height: 2,
      color: ThemeColors.color636366,
    );
  }

  Widget _bottom() {
    return _confirmButton();
  }

  Widget _confirmButton() {
    return Container(
      padding: EdgeInsets.only(top: 15),
      decoration: BoxDecoration(
        color: ThemeColors.white,
        boxShadow: [
          BoxShadow(
            color: ThemeColors.grayE5E5EA,
            offset: Offset(0, -1),
            blurRadius: 3.0,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomButtonWidget(
            colorButton: ThemeColors.color26467A,
            margin: EdgeInsetsDirectional.only(
              start: 16.w,
              end: 16.w,
            ),
            padding: EdgeInsetsDirectional.only(
              top: 14.sp,
              bottom: 14.sp,
            ),
            onPressed: _verify,
            title: _submitLabel,
            titleColor: ThemeColors.white,
            fontWeightTitle: FontWeight.w600,
            sizeTitle: 17,
            enabled: valueChanged,
          ),
          SizedBox(height: 16.sp),
          const DoYouNeedAhelp(),
        ],
      ),
    );
  }

  Widget _body() {
    return SafeArea(
      child: SizedBox(
        height: double.infinity,
        width: double.infinity,
        child: GestureDetector(
          onTap: _dismissKeyboard,
          child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                _appBar(),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsetsDirectional.only(
                        start: 16.w,
                        end: 16.w,
                        top: 33.sp,
                        bottom: 24.sp,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          _buildImage(),
                          SizedBox(height: 10.sp),
                          _titleHeader(),
                          _message(),
                          SizedBox(height: 10.sp),
                          _number(),
                          SizedBox(height: 14.sp),
                          _buildCode(),
                          SizedBox(height: 14.sp),
                          buildTimer(),
                          SizedBox(height: _bodyMarginVertical),
                        ],
                      ),
                    ),
                  ),
                ),
                _bottom(),
              ]),
        ),
      ),
    );
  }

  Widget _appBar() {
    return CustomAppBarAuthWidget(
      title: confirmWhatsAppNumberLabel.translate(),
      showBackTitle: true,
      showBack: true,
      showClose: false,
      valueProgress: 2 / 5,
    );
  }

  void _onChangedPin(String value) {
    _authProvider.setCodeErrorConfirmCode(false);
    valueChanged = value.isNotEmpty && value.length == 4;
    setState(() {});
  }

  Future<void> _onCLickSendAgain() async {
    if (!_isCounting) {
      await _authProvider.sendCodeAgain(context, widget.arguments);
      _isCountingController.text = 'true';
    }
  }

  Widget _number() {
    final country = _countries?.firstWhereOrNull(
        (country) => country.id == widget.arguments?.countryCodeId);
    final email = widget.arguments?.validateUserEntity?.email ?? '';

    return Padding(
      padding: EdgeInsetsDirectional.symmetric(horizontal: 16.sp),
      child: Text.rich(
        textAlign: TextAlign.center,
        TextSpan(
          text: '${country?.countryCode}${widget.arguments?.whatsNumber} ',
          style: const TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.w400,
            color: ThemeColors.color007AFF,
          ),
          children: [
            if (haveEmail)
              TextSpan(
                text: ' | $email',
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w400,
                  color: ThemeColors.color007AFF,
                ),
              ),
          ],
        ),
      ),
    );
  }

  bool get isAr => _localizationProvider.locals.language == 'ar';
}
