import 'dart:convert';
import 'dart:math';

import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// import 'package:google_maps_webservice/places.dart';
import 'package:google_maps_webservice_ex/places.dart';
import 'package:modarby/Models/Content/Language.dart';
import 'package:modarby/Models/Content/LanguageLevel.dart';
import 'package:modarby/Models/Content/Nationality.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/DependencyManager/TextProviders/TextProvider.dart';
import 'package:modarby/Providers/StudentProfileProvider.dart';
import 'package:modarby/Providers/ThirdPartiesProvider.dart';
import 'package:modarby/SharedScreens/UpdateLogin.dart';
import 'package:modarby/Widgets/CustomDatePicker.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/utilities/ProgressIndicators.dart';
import 'package:modarby/core/utilities/error_parser.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';
import 'package:modarby/core/utilities/shard.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';

import '../../Enums/FontWeights.dart';
import '../../Enums/Settings.dart';
import '../../Enums/UserType.dart';
import '../../Models/Authentication/StudentAdditionalInfoDTO.dart';
import '../../Models/Content/CountryCode.dart';
import '../../Models/Shared/SpokenLanguage.dart';
import '../../Models/StudentProfile/Student.dart';
import '../../Models/ThirdParties.dart/Place.dart';
import '../../Widgets/PhoneFormField.dart';
import '../../Widgets/ProfileCard.dart';
import '../../Widgets/ProfileCountryField.dart';
import '../../Widgets/ProfileFormField.dart';
import '../../Widgets/ProfileSelectBox.dart';
import '../../Widgets/SimpleAppBar.dart';
import '../../Widgets/SpokenLanguages.dart';
import '../../core/Utilities/Snackbars.dart';
import '../../core/Utilities/Validator.dart';
import '../../core/utilities/language/LanguagesKeys.dart';

class StudentEditProfileScreen extends StatefulWidget {
  static const routeName = '/StudentEditProfile';

  @override
  _StudentEditProfileScreenState createState() =>
      _StudentEditProfileScreenState();
}

class _StudentEditProfileScreenState extends State<StudentEditProfileScreen> {
  late TextProvider textProvider;
  late IconsProvider iconsProvider;

  late LocalizationProvider localizationProvider;

  Student? student;

  String? appBarTitle;
  String? actionButtonLabel;
  String? nameLabel;
  String? genderFieldLabel;
  String? birthDateFieldLabel;
  String? nationalityLabel;
  String? emailFieldLabel;
  String? phoneNumberLabel;
  String? phoneNumberHint;
  String? whatsappBoxLabel;
  String? changeWhatsappLabel;
  String? cityFieldLabel;
  String? timeZoneFieldLabel;
  String? privacyLabel;
  String? receiveSMSLabel;
  String? missingFieldsLabel;

  String? _typeLabel;
  String? _levelLabel;
  String? _curriculumLabel;
  String? _yearLabel;

  List<dynamic>? genders;
  List<Nationality>? nationalities;
  List<CountryCode>? countryCodes;
  List<dynamic>? timezones;
  List<Language>? languages;
  List<LanguageLevel>? levels;

  String? _language;

  late CountryCode? placeOfResidence;
  late List _types;
  List? _levels;
  List? _curriculums;
  List? _years;
  List? _collegeYears;

  int? _selectedType;
  int? _selectedLevel;
  int? _selectedCurriculum;
  int? _selectedYear;

  bool showPlacesSearch = false;
  String? studentCity;
  double? studentLongitude, studentLatitude;
  String sessionToken = Uuid().v4();
  List<Place> suggestedPlaces = [];

  late Map<String?, bool> _errors;

  final bodyMarginHorizontal = 15.0;
  final bodyMarginVertical = 30.0;
  final mainBodyPadding = 70.0;
  final formFieldPadding = 30.0;
  final buttonWidth = 76.0;
  final buttonBorderRadius = 30.0;
  final internalPadding = 10.0;
  final suggestedPlacesMaxHeight = 200.0;

  final _contentMargin = 30.0;
  final _internalMargin = 5.0;

  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();

  TextEditingController? nameController;
  TextEditingController? genderController;
  TextEditingController? birthDayController;
  TextEditingController? nationalityController =
      TextEditingController(text: pleaseSelectSource.translate().orDefault);
  TextEditingController? emailController;
  TextEditingController? phoneCodeController;
  TextEditingController? phoneNumberController;
  TextEditingController? whatsappCodeController;
  TextEditingController? whatsappNumberController;
  TextEditingController? cityController;
  TextEditingController? timeZoneController;
  TextEditingController? placeOfResidenceController;
  TextEditingController? languagesController;

  @override
  void initState() {
    _initializeData();
    getStudent();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: ThemeColors.primaryColor,
        appBar: SimpleAppBar(title: appBarTitle, trailing: buildSaveButton()),
        body: GestureDetector(
          onTap: _dismissKeyboard,
          child: Container(
            width: MediaQuery.of(context).size.width,
            margin: EdgeInsets.symmetric(
                horizontal: bodyMarginHorizontal, vertical: bodyMarginVertical),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  ProfileCard(),
                  SizedBox(height: mainBodyPadding / 2),
                  buildBody(),
                ],
              ),
            ),
          ),
        ));
  }

  Widget buildSaveButton() {
    return Container(
      width: buttonWidth,
      padding: EdgeInsets.symmetric(vertical: internalPadding),
      child: TextButton(
        child: textProvider.buildNormalText3(
          actionButtonLabel,
          weight: FontWeights.medium,
          color: ThemeColors.primaryColor,
        ),
        style: TextButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonBorderRadius),
          ),
          backgroundColor: ThemeColors.color26467A,
          padding: EdgeInsets.zero,
        ),
        onPressed: () => _submitMethod(),
      ),
    );
  }

  Widget buildBody() {
    final language = localizationProvider.locals.language;
    final options =
        (nationalities ?? []).map((Nationality e) => e.name ?? '').toList();

    return Form(
      key: _formKey,
      child: Column(
        children: [
          ProfileFormField(
            label: nameLabel,
            hint: nameLabel,
            controller: nameController,
            validator: (value) => Validator.isValidName(value, language),
            formatters: [
              FilteringTextInputFormatter.deny(RegExp('[0-9|\u0660-\u0669]'))
            ],
          ),
          SizedBox(height: formFieldPadding),
          ProfileSelectBox(
            colorBorder: ThemeColors.defaultBorderColor,
            label: genderFieldLabel,
            options: genders?.map((gender) => gender.name).toList(),
            controller: genderController,
          ),
          SizedBox(height: formFieldPadding),
          CustomDatePicker(
            label: birthDateFieldLabel,
            controller: birthDayController,
          ),
          SizedBox(height: formFieldPadding),
          ProfileSelectBox(
            colorBorder: ThemeColors.defaultBorderColor,
            label: nationalityLabel,
            options: options,
            controller: nationalityController,
          ),
          SizedBox(height: formFieldPadding),
          ProfileFormField(
            label: emailFieldLabel,
            hint: emailFieldLabel,
            controller: emailController,
          ),
          SizedBox(height: formFieldPadding),
          PhoneFormField(
            label: phoneNumberLabel,
            hint: phoneNumberHint,
            codeController: phoneCodeController,
            numberController: phoneNumberController,
          ),
          SizedBox(height: formFieldPadding),
          PhoneFormField(
            label: whatsappBoxLabel,
            hint: phoneNumberHint,
            codeController: whatsappCodeController,
            numberController: whatsappNumberController,
            isEnabled: false,
          ),
          SizedBox(height: formFieldPadding / 2),
          buildChangeWhatsapp(),
          SizedBox(height: formFieldPadding),
          buildCityField(),
          SizedBox(height: formFieldPadding),
          ProfileCountryField(
            countryController: placeOfResidenceController,
            countryCode: placeOfResidence?.isoCode,
          ),
          SizedBox(height: formFieldPadding),
          ProfileSelectBox(
              label: timeZoneFieldLabel,
              options: timezones?.map((timezone) => timezone.name).toList(),
              controller: timeZoneController),
          SizedBox(height: formFieldPadding),
          _buildAdditionalInfo(),
          SizedBox(height: formFieldPadding),
          SpokenLanguages(
              languages: languages ?? [],
              levels: levels ?? [],
              spokenLanguages: student?.spokenLanguages ?? [],
              referral: false,
              controller: languagesController),
          SizedBox(height: formFieldPadding),
        ],
      ),
    );
  }

  Widget buildChangeWhatsapp() {
    return GestureDetector(
      onTap: () => Navigator.pushNamed(context, UpdateLoginScreen.routeName,
          arguments: true),
      child: Row(
        children: [
          iconsProvider.mobileChat,
          SizedBox(width: 10.0),
          Expanded(
            child: textProvider.buildNormalText3(
              changeWhatsappLabel,
              color: ThemeColors.activeChoiceColor,
            ),
          ),
          _buildArrow(),
        ],
      ),
    );
  }

  Widget _buildArrow() {
    final icon = iconsProvider.parametrizedIcon(
      iconsProvider.rightArrow,
      color: ThemeColors.activeChoiceColor,
    );
    if (localizationProvider.locals.language == LanguagesKeys.ARABIC) {
      return Transform(
        alignment: Alignment.center,
        transform: Matrix4.rotationY(pi),
        child: icon,
      );
    }
    return icon;
  }

  Widget buildCityField() {
    return Column(
      children: [
        FocusScope(
          child: Focus(
            onFocusChange: (value) => setState(() {
              showPlacesSearch = value;
              if (value) sessionToken = Uuid().v4();
              if (!value) suggestedPlaces.clear();
            }),
            child: ProfileFormField(
                label: cityFieldLabel,
                hint: cityFieldLabel,
                controller: cityController),
          ),
        ),
        Visibility(
          child: buildSuggestedPlaces(),
          visible: showPlacesSearch,
        )
      ],
    );
  }

  Widget buildSuggestedPlaces() {
    if (suggestedPlaces.isEmpty) return SizedBox();
    return Container(
      constraints: BoxConstraints(maxHeight: suggestedPlacesMaxHeight),
      decoration: BoxDecoration(
          border: Border.all(color: ThemeColors.defaultBorderColor)),
      child: Scrollbar(
        thumbVisibility: true,
        controller: _scrollController,
        child: ListView.builder(
          controller: _scrollController,
          itemCount: suggestedPlaces.length,
          itemBuilder: (BuildContext context, int index) {
            return ListTile(
              title: textProvider
                  .buildNormalText4(suggestedPlaces[index].description),
              onTap: () async => await _parseCity(suggestedPlaces[index]),
            );
          },
        ),
      ),
    );
  }

  Widget _buildAdditionalInfo() {
    return Column(
      children: [
        _buildType(),
        SizedBox(height: _contentMargin),
        _buildLevel(),
        if (_showYears()) SizedBox(height: _contentMargin),
        if (_showYears()) _buildYear(),
        if (_showCurriculums()) SizedBox(height: _contentMargin),
        if (_showCurriculums()) _buildCurriculum(),
      ],
    );
  }

  Widget _buildType() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTitle(_typeLabel),
        SizedBox(height: _internalMargin),
        _buildTypeValues(),
        _buildError(_typeLabel),
      ],
    );
  }

  Widget _buildTypeValues() {
    return Row(
      children: _types.map((value) {
        return Expanded(
          child: RadioListTile<int>(
            activeColor: ThemeColors.accentColor,
            dense: true,
            title: _buildValue(value),
            value: value == _types.first ? UserType.student : UserType.parent,
            groupValue: _selectedType,
            onChanged: _onTypeChange,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildLevel() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTitle(_levelLabel),
        SizedBox(height: _internalMargin),
        _buildLevelValues(),
        _buildError(_levelLabel),
      ],
    );
  }

  Widget _buildLevelValues() {
    return Column(
      children: (_levels ?? []).map((value) {
        return RadioListTile<int?>(
          activeColor: ThemeColors.accentColor,
          dense: true,
          title: _buildValue(value.name),
          value: value.id,
          groupValue: _selectedLevel,
          onChanged: _onLevelChange,
        );
      }).toList(),
    );
  }

  Widget _buildYear() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTitle(_yearLabel),
        SizedBox(height: _internalMargin),
        _buildYearValues(),
        _buildError(_yearLabel),
      ],
    );
  }

  Widget _buildYearValues() {
    return ListView.builder(
      itemCount: _getNumOfYears(),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (BuildContext context, int index) {
        return RadioListTile<int>(
          activeColor: ThemeColors.accentColor,
          dense: true,
          title: _buildValue(_getYearsList()![index]),
          value: (_getYearsList()?.indexOf(_getYearsList()![index]) ?? 0) + 1,
          groupValue: _selectedYear,
          onChanged: _onYearChange,
        );
      },
    );
  }

  Widget _buildCurriculum() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTitle(_curriculumLabel),
        SizedBox(height: _internalMargin),
        _buildCurriculumValues(),
        _buildError(_curriculumLabel),
      ],
    );
  }

  Widget _buildCurriculumValues() {
    return Column(
      children: (_curriculums ?? []).map((value) {
        return RadioListTile<int?>(
          activeColor: ThemeColors.accentColor,
          dense: true,
          title: _buildValue(value.name),
          value: value.id,
          groupValue: _selectedCurriculum,
          onChanged: _onCurriculumChange,
        );
      }).toList(),
    );
  }

  Widget _buildTitle(String? title) {
    return textProvider.buildNormalText3(title);
  }

  Widget _buildValue(String? value) {
    return textProvider.buildNormalText4(value);
  }

  Widget _buildError(String? field) {
    return Visibility(
      visible: _errors[field]!,
      child: textProvider.buildNormalText5(
        Validator.isRequired(_language!),
        color: ThemeColors.darkRed,
      ),
    );
  }

  void _onTypeChange(int? value) {
    setState(() => _selectedType = value);
  }

  void _onLevelChange(int? value) {
    _selectedYear = null;
    setState(() => _selectedLevel = value);
    if (!_showCurriculums()) _selectedCurriculum = null;
  }

  bool _showYears() {
    if (_selectedLevel == null) return false;
    final level = _levels?.firstWhereOrNull((l) => l.id == _selectedLevel);
    if (level.numOfYears > 0) return true;
    return false;
  }

  int? _getNumOfYears() {
    return _levels?.firstWhereOrNull((l) => l.id == _selectedLevel).numOfYears;
  }

  List? _getYearsList() {
    if (_selectedLevel == null) return [];
    if (_selectedLevel == 4) return _collegeYears;
    return _years;
  }

  void _onYearChange(int? value) {
    setState(() => _selectedYear = value);
  }

  bool _showCurriculums() {
    if (_selectedLevel == null) return false;
    final level = _levels?.firstWhereOrNull((l) => l.id == _selectedLevel);
    if (level.id == 4 || level.id == 6) return false;
    return true;
  }

  void _onCurriculumChange(int? value) {
    setState(() => _selectedCurriculum = value);
  }

  Future<void> _getSuggestedPlaces() async {
    if ((cityController?.text ?? '').isEmpty) {
      return setState(() => suggestedPlaces.clear());
    }
    try {
      final input = cityController?.text;
      final language = localizationProvider.locals.language;
      final newPlaces = await context
          .read<ThirdPartiesProvider>()
          .getSuggestedPlaces(input ?? '', language,
              Settings.GOOGLE_PLACES_API_KEY, sessionToken);
      setState(() {
        suggestedPlaces = newPlaces;
      });
    } catch (error) {
      final errorException = ErrorParser().parseError(error);
      Snackbars.danger(context, errorException.message);
    }
  }

  Future<void> _parseCity(Place place) async {
    final _places = GoogleMapsPlaces(apiKey: Settings.GOOGLE_PLACES_API_KEY);
    final detail = await _places.getDetailsByPlaceId(place.placeId!);
    final latitude = detail.result?.geometry?.location.lat;
    final longitude = detail.result?.geometry?.location.lng;
    final address = place.description;

    setState(() {
      student?.latitude = latitude;
      student?.longitude = longitude;
      cityController?.text = address ?? '';
      studentCity = address;
      _dismissKeyboard();
    });
  }

  void _submitMethod() async {
    _dismissKeyboard();
    if (!_formKey.currentState!.validate() || !_validate()) {
      Snackbars.danger(context, missingFieldsLabel ?? '');
      return;
    }
    ProgressIndicators.loadingDialog(context);
    final student = _generateStudent();
    final authProvider = context.read<AuthenticationProvider>();
    final studentProfileProvider = context.read<StudentProfileProvider>();
    final languageId = localizationProvider.locals.languageId;
    final additionalInfoDTO = _generateAdditionalInfoDTO();
    try {
      await studentProfileProvider.updateProfile(
          authProvider.accessToken, student, languageId);
      await studentProfileProvider.updateAdditionalInfo(
          authProvider.accessToken, additionalInfoDTO, languageId);
      await studentProfileProvider.getProfile(
          authProvider.accessToken, languageId);
      final firstInstallationArgument =
          await Shard().getFirstInstallationArgumentFromServer();
      firstInstallationArgument?.levelId = _selectedLevel;
      firstInstallationArgument?.curriculmsId = _selectedCurriculum;
      Shard().doSearch(
        firstInstallationArgument,
      );
      Navigator.pop(context);
      Navigator.pop(context);
    } catch (error) {
      Navigator.pop(context);
      final errorException = ErrorParser().parseError(error);
      Snackbars.danger(context, errorException.message);
    }
  }

  Student _generateStudent() {
    final firstname = nameController?.text ?? '';
    final birthday = birthDayController?.text ?? '';
    final email = emailController?.text ?? '';
    final phone = phoneNumberController?.text ?? '';
    final whatsapp = whatsappNumberController?.text ?? '';
    final currencyId = localizationProvider.locals.currency?.id;

    String? city = student?.city;
    if (studentLongitude != student?.longitude ||
        studentLatitude != student?.latitude) city = studentCity;

    final genderId = genders
        ?.firstWhereOrNull((gender) => gender.name == genderController?.text)
        ?.id;
    final nationality = (nationalityController?.text ?? '').trim().isNotEmpty
        ? nationalities?.firstWhereOrNull(
            (nationality) => nationality.name == nationalityController?.text)
        : null;
    final timezone = timezones?.firstWhereOrNull(
        (timezone) => timezone.name == timeZoneController?.text);
    final placeOfResidence = (countryCodes ?? []).firstWhereOrNull(
        (country) => country.name == placeOfResidenceController?.text);
    final phoneCountryCodeId = (countryCodes ?? [])
        .firstWhereOrNull((countryCode) =>
            countryCode.countryCode == phoneCodeController?.text)
        ?.id;
    final whatsappCountryCodeId = (countryCodes ?? [])
        .firstWhereOrNull((countryCode) =>
            countryCode.countryCode == whatsappCodeController?.text)
        ?.id;

    List<SpokenLanguage>? languages;
    if ((languagesController?.text ?? '').isNotEmpty) {
      final jsonLanguages = json.decode(languagesController?.text ?? '');
      languages = List<SpokenLanguage>.from(
          jsonLanguages.map((language) => SpokenLanguage.fromJson(language)));
    }

    return Student(
      userId: student?.userId,
      name: firstname,
      genderId: genderId,
      dateOfBirth: birthday,
      nationalityId: nationality != null ? nationality.id : -1,
      email: email,
      phoneCountryCodeId: phoneCountryCodeId,
      phoneNumber: phone,
      whatsAppCountryCodeId: whatsappCountryCodeId,
      whatsappNumber: whatsapp,
      city: city,
      longitude: student?.longitude,
      latitude: student?.latitude,
      timezone: timezone?.id,
      placeOrResidenceCountryId: placeOfResidence?.id,
      spokenLanguages: languages ?? (student?.spokenLanguages ?? []),
      defaultCurrencyId: currencyId,
      emailConfirmed: student?.emailConfirmed,
      countryCode: timezone?.countryCode,
      photoName: student?.photoName,
      source: student?.source,
    );
  }

  StudentAdditionalInfoDTO _generateAdditionalInfoDTO() {
    final placeOfResidence = (countryCodes ?? []).firstWhereOrNull(
        (country) => country.name == placeOfResidenceController?.text);
    return StudentAdditionalInfoDTO(
      userTypeId: _selectedType,
      levelId: _selectedLevel,
      curriculumId: _selectedCurriculum,
      year: _selectedYear,
      placeOrResidenceCountryId: placeOfResidence?.id,
      studentSourceId: student?.source,
    );
  }

  bool _validate() {
    _errors[_typeLabel] = false;
    _errors[_levelLabel] = false;
    _errors[_yearLabel] = false;
    _errors[_curriculumLabel] = false;

    if (_selectedType == null) _errors[_typeLabel] = true;
    if (_selectedLevel == null) _errors[_levelLabel] = true;
    if ((_selectedYear == null || _selectedYear == 0) && _showYears()) {
      _errors[_yearLabel] = true;
    }
    if (_selectedCurriculum == null && _showCurriculums()) {
      _errors[_curriculumLabel] = true;
    }

    if (_errors.containsValue(true)) {
      setState(() {});
      return false;
    }

    return true;
  }

  void _onResidenceChange() {
    const SAUDIA_TIMEZONE_ID = 1780;
    final country = countryCodes?.firstWhere((country) {
      return country.name == placeOfResidenceController?.text;
    });
    final timezone = (timezones ?? []).firstWhereOrNull(
          (zone) {
            return zone.countryCode == country?.isoCode;
          },
        ) ??
        timezones?.singleWhere((zone) {
          return zone.id == SAUDIA_TIMEZONE_ID;
        });

    if (context.mounted) {
      setState(() {
        timeZoneController?.text = timezone?.name ?? '';
      });
    }
  }

  void _dismissKeyboard() {
    FocusScope.of(context).unfocus();
  }

  void _initializeData() {
    textProvider = context.read<DependencyManager>().text;
    iconsProvider = context.read<DependencyManager>().icons;
    localizationProvider = context.read<DependencyManager>().localization;

    student = context.read<StudentProfileProvider>().student;

    studentLongitude = student?.longitude;
    studentLatitude = student?.latitude;

    appBarTitle = localizationProvider.resources.getWithKey(FORMS_MYPROFILE);
    actionButtonLabel = localizationProvider.resources.getWithKey(COMMON_SAVE);
    nameLabel =
        localizationProvider.resources.getWithKey(STUDENT_FORMS_FULLNAME);
    genderFieldLabel = localizationProvider.resources.getWithKey(FORMS_GENDER);
    birthDateFieldLabel =
        localizationProvider.resources.getWithKey(FORMS_DATEOFBIRTH);
    nationalityLabel =
        localizationProvider.resources.getWithKey(FORMS_NATIONALITY);
    emailFieldLabel = localizationProvider.resources.getWithKey(FORMS_EMAIL);
    phoneNumberLabel =
        localizationProvider.resources.getWithKey(FORMS_PHONENUMBER);
    phoneNumberHint =
        localizationProvider.resources.getWithKey(CONTACTUS_WHATSAPPNUMBER);
    whatsappBoxLabel =
        localizationProvider.resources.getWithKey(FORMS_WHATSAPPNUMBER);
    changeWhatsappLabel =
        localizationProvider.resources.getWithKey(COMMON_CHANGEWHATSAPPNUMBER);
    cityFieldLabel = localizationProvider.resources.getWithKey(FORMS_CITY);
    timeZoneFieldLabel =
        localizationProvider.resources.getWithKey(FORMS_TIMEZONE);
    privacyLabel = localizationProvider.resources.getWithKey(FORMS_ABOUTINFO);
    receiveSMSLabel =
        localizationProvider.resources.getWithKey(FORMS_ABOUTINFO);
    missingFieldsLabel = localizationProvider.resources.getWithKey(FORMS_FAILD);

    _typeLabel =
        localizationProvider.resources.getWithKey(STUDENT_ADDITIONAL_INFO_TYPE);
    _levelLabel = localizationProvider.resources
        .getWithKey(STUDENT_ADDITIONAL_INFO_LEVEL);
    _curriculumLabel = localizationProvider.resources
        .getWithKey(STUDENT_ADDITIONAL_INFO_CURRICULUM);
    _yearLabel =
        localizationProvider.resources.getWithKey(STUDENT_ADDITIONAL_INFO_YEAR);

    genders = localizationProvider.lookups.getWithKey(GENDER);
    nationalities = localizationProvider.lookups
        .getWithKey(NATIONALITY)
        .cast<Nationality>()
        .toList();

    timezones = localizationProvider.lookups.getWithKey(TIMEZONE);

    _language = localizationProvider.locals.language;

    _types = [
      localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_TYPE_STUDENT),
      localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_TYPE_PARENT),
    ];
    _levels = localizationProvider.lookups.getWithKey(TRAINEETYPE);
    _curriculums = localizationProvider.lookups.getWithKey(CURRICULUMS);
    _years = [
      localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_FIRST),
      localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_SECOND),
      localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_THIRD),
      localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_FOURTH),
      localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_FIFTH),
      localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_SIXTH),
    ];
    _collegeYears = [
      localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_FIRST),
      localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_SECOND),
      localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_THIRD),
      localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_FOURTH),
      localizationProvider.resources
          .getWithKey(STUDENT_ADDITIONAL_INFO_YEAR_MORETHANFOURTH),
    ];

    countryCodes = localizationProvider.countryCodes.getAll();
    languages = localizationProvider.languages.getAll();
    levels = localizationProvider.languagesLevels.getAll();

    nameController = TextEditingController(text: student?.name);
    birthDayController = TextEditingController(text: student?.dateOfBirth);
    emailController = TextEditingController(text: student?.email);
    phoneNumberController = TextEditingController(text: student?.phoneNumber);
    whatsappNumberController =
        TextEditingController(text: student?.whatsappNumber);
    cityController = TextEditingController(text: student?.city);
    cityController?.addListener(_getSuggestedPlaces);

    genderController = TextEditingController(
        text: (genders ?? [])
                .firstWhereOrNull(
                  (gender) => gender.id == student?.genderId,
                )
                ?.name ??
            '');
    timeZoneController = TextEditingController(
        text: (timezones ?? [])
                .firstWhereOrNull((element) => element.id == student?.timezone)
                ?.name ??
            '');

    placeOfResidence = countryCodes?.firstWhereOrNull((CountryCode country) =>
        country.id == student?.placeOrResidenceCountryId);
    placeOfResidenceController = TextEditingController(
        text: placeOfResidence != null ? placeOfResidence?.name : '');

    final nationality = nationalities?.firstWhereOrNull(
      (nationality) =>
          (nationality.id == student?.nationalityId) ||
          (nationality.id == student?.additionalInfo?.nationalityId),
    );
    nationalityController = TextEditingController(
        text: nationality != null ? nationality.name : ' ');

    final phoneCode = countryCodes?.firstWhereOrNull(
        (countryCode) => countryCode.id == student?.phoneCountryCodeId);
    final whatsappCode = countryCodes?.firstWhereOrNull(
        (countryCode) => countryCode.id == student?.whatsAppCountryCodeId);

    phoneCodeController = TextEditingController(
        text: phoneCode != null ? phoneCode.countryCode : '');
    whatsappCodeController = TextEditingController(
        text: whatsappCode != null ? whatsappCode.countryCode : '');

    languagesController = TextEditingController();

    placeOfResidenceController?.addListener(_onResidenceChange);

    _fillAdditionalInfo();

    _errors = {
      _typeLabel: false,
      _levelLabel: false,
      _curriculumLabel: false,
      _yearLabel: false,
    };
  }

  void _fillAdditionalInfo() {
    if (student == null || student?.additionalInfo == null) return;
    if (student?.additionalInfo?.type != null) {
      _selectedType = student?.additionalInfo?.type;
    }
    if (student?.additionalInfo?.levelId != null) {
      _selectedLevel = student?.additionalInfo?.levelId;
    }
    if (student?.additionalInfo?.year != null) {
      if (_selectedLevel != null) {
        final studentYear = student?.additionalInfo?.year ?? 0;
        final years = _getNumOfYears()!;
        if (studentYear <= years) {
          _selectedYear = studentYear;
        }
      }
    }
    if (student?.additionalInfo?.curriculumId != null) {
      _selectedCurriculum = student?.additionalInfo?.curriculumId;
    }
  }

  Future<void> getStudent() async {
    final authProvider = context.read<AuthenticationProvider>();
    final studentProfileProvider = context.read<StudentProfileProvider>();
    await studentProfileProvider.getProfile(
        authProvider.accessToken, Shard().languageId);
  }
}
