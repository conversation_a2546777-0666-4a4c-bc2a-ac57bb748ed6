import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_callkit_incoming/flutter_callkit_incoming.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:modarby/Instructor/Screens/Instructor_home.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/CollectDataProvider.dart';
import 'package:modarby/Providers/CommonProvider.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/InstructorDashboardProvider.dart';
import 'package:modarby/Providers/SearchProvider.dart';
import 'package:modarby/Providers/StudentDashboardProvider.dart';
import 'package:modarby/Providers/StudentProfileProvider.dart';
import 'package:modarby/Providers/search_result_tutor_provider.dart';
import 'package:modarby/Widgets/CustomAppBar.dart';
import 'package:modarby/Widgets/NotificationFullScreen.dart';
import 'package:modarby/Widgets/NotificationPopUp.dart';
import 'package:modarby/Widgets/StudentDrawer.dart';
import 'package:modarby/Widgets/flip_if_rtl.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/config/themes/images.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/notifications/firebase_messaging_impl.dart';
import 'package:modarby/core/utilities/CommonFunctions.dart';
import 'package:modarby/core/utilities/DeepLinking.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';
import 'package:modarby/core/utilities/navigation_service.dart';
import 'package:modarby/core/utilities/permition_handler.dart';
import 'package:modarby/core/utilities/shard.dart';
import 'package:modarby/core/utilities/storage.dart';
import 'package:modarby/features/auth/arguments/auth_argument.dart';
import 'package:modarby/features/auth/login/login_page.dart';
import 'package:modarby/features/serach_instructor/Search.dart';
import 'package:modarby/features/voice_call/data/call-cleanup.dart';
import 'package:provider/provider.dart';

import '../../Providers/MessagesProvider.dart';
import '../../Widgets/BottomAppBar.dart';
import '../../features/messages/presentation/views/Messages.dart';
import '../Screens/Lessons.dart';
import '../Screens/Profile.dart';

class StudentHomeScreen extends StatefulWidget {
  static const routeName = '/StudentHome';

  @override
  _StudentHomeScreenState createState() => _StudentHomeScreenState();
}

String? homeTabLabel;
String? fabLabel;
String? lessonsTabLabel;
String? messageTabLabel;
String? profileTabLabel;
String? loginTabLabel;
String? phoneNumber;

String? whatsappStarterMessage;

const fabMargin = 30.0;
const iconScale = 1.2;
const timer = 500;

class _StudentHomeScreenState extends State<StudentHomeScreen> {
  late IconsProvider iconsProvider;

  late LocalizationProvider localizationProvider;
  late SearchProvider searchProvider;
  late SearchResultProvider searchResultProvider;

  int _notificationsUnReadCount = 0;

  final mainCategoriesListPadding = 8.0;
  final iconSide = 24.sp;
  final fabHeight = 120.0;
  final GlobalKey<ScaffoldState> drawerKey = new GlobalKey<ScaffoldState>();
  bool isVisible = true;

  final PageController _controller = PageController(
    initialPage: 0,
  );
  int? _currentIndex = 0;
  bool isKeyboardOpen = false;
  StreamSubscription<bool>? keyboardVisibilitySubscription;
  late StreamSubscription<Uri> deepLinkStream;

  @override
  void initState() {
    super.initState();
    Map<String, dynamic>? args;
    Future.delayed(const Duration(microseconds: 300), () {
      setState(() {
        try {
          args = ModalRoute.of(context)!.settings.arguments
              as Map<String, dynamic>?;
        } catch (e) {
          args = ModalRoute.of(context)!.settings.arguments
              as Map<String, dynamic>;
          log(e.toString());
        }
      });
      if (args != null && args!.containsKey('PAGE_INDEX')) {
        onTabTapped(args!['PAGE_INDEX']);
      }
    });
    // WidgetsBinding.instance.addPostFrameCallback((_) => _showPopUps());
    FireBaseMessagingImpl.fireBaseMessagingImpl
        .handleNotificationsWhenAppClosed();
    _initDeepLink();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _checkAnnouncement();
      _loadNotification();
      _getVoiceSetting();
      StaticVar.context.read<CollectDataProvider>().addUser();
      _cleanupForReceiverCalls();
      _checkPermitionFlutterIncomingIntent();
      _checkDataIncomingCall();
    });

    keyboardVisibilitySubscription =
        KeyboardVisibilityController().onChange.listen((bool visible) {
      setState(() {
        isKeyboardOpen = visible;
      });
    });
  }

  void _checkDataIncomingCall() {
    Shard().loadCallSavedData();
  }

  void _cleanupForReceiverCalls() {
    final cleanService = CallCleanupService();
    if (StaticVar.context.read<AuthenticationProvider>().accessToken != null) {
      cleanService.startPeriodicCleanupForReceiver(
        context.read<StudentProfileProvider>().student?.userId ?? '',
      );
    }
  }

  Future<void> _checkPermitionFlutterIncomingIntent() async {
    if (Platform.isAndroid) {
      final status = await PermissionWrapper.checkFullIntentPermission();
      if (status.inverted) {
        await FlutterCallkitIncoming.requestFullIntentPermission();
      }
    }
  }

  @override
  void dispose() {
    deepLinkStream.cancel();
    _controller.dispose();
    keyboardVisibilitySubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    iconsProvider = context.watch<DependencyManager>().icons;
    localizationProvider = context.watch<DependencyManager>().localization;
    searchResultProvider = context.watch<SearchResultProvider>();
    _notificationsUnReadCount =
        context.watch<InstructorDashboardProvider>().numberUnreadCount;
    final isLoggedIn = context.watch<AuthenticationProvider>().isLoggedIn;

    loadScreenLabels();

    final arguments = AuthArgument(
      routeScreen: StudentHomeScreen.routeName,
      comeFromRegister: true,
      showLookLogin: true,
      comeFromOtherPlace: false,
      index: _currentIndex,
      needMoreInfo: (getIt<Storage>().whatsappNumber ?? '').isNotEmpty,
    );

    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: ThemeColors.primaryColor,
        key: drawerKey,
        drawer: StaticVar.isStudent ||
                getIt<Storage>().instructorComeFromViewSimilarTutors.inverted
            ? _currentIndex == 0
                ? null
                : const StudentDrawer()
            : null,
        appBar: StaticVar.isStudent ||
                getIt<Storage>().instructorComeFromViewSimilarTutors.inverted
            ? _currentIndex == 0
                ? null
                : AppBarWidget(
                    notificationsUnReadCount: _notificationsUnReadCount,
                    appearNotification: isLoggedIn,
                  )
            : _buildBackButton(),
        bottomNavigationBar: Visibility(
          replacement: SizedBox.shrink(),
          visible: StaticVar.isStudent ||
                  getIt<Storage>().instructorComeFromViewSimilarTutors.inverted
              ? isKeyboardOpen
                  ? false
                  : true
              : false,
          child: FABBottomAppBar(
            centerItemText: fabLabel,
            backgroundColor: ThemeColors.white,
            selectedColor: ThemeColors.black,
            onTabSelected: onTabTapped,
            selectedIndex: _currentIndex,
            color: ThemeColors.darkGrey,
            items: [
              FABBottomAppBarItem(
                icon: iconsProvider.parametrizedIcon(
                  iconsProvider.searchIconBottomBar,
                  width: iconSide,
                  height: iconSide,
                ),
                selectedIcon: iconsProvider.parametrizedIcon(
                  iconsProvider.searchIconSolidBottomBar,
                  width: iconSide,
                  height: iconSide,
                  color: ThemeColors.color26467A,
                ),
                text: homeTabLabel,
              ),
              FABBottomAppBarItem(
                icon: iconsProvider.parametrizedIcon(
                  iconsProvider.presentation,
                  width: iconSide,
                  height: iconSide,
                ),
                selectedIcon: iconsProvider.parametrizedIcon(
                  iconsProvider.presentationSolidSelected,
                  width: iconSide,
                  height: iconSide,
                ),
                text: lessonsTabLabel,
              ),
              FABBottomAppBarItem(
                icon: iconsProvider.parametrizedIcon(
                  iconsProvider.whatsappButton,
                  width: iconSide,
                  height: iconSide,
                ),
                selectedIcon: iconsProvider.parametrizedIcon(
                  iconsProvider.whatsappButton,
                  width: iconSide,
                  height: iconSide,
                  color: ThemeColors.color26467A,
                ),
                text: NAV_SUPPORT.translate(),
              ),
              FABBottomAppBarItem(
                icon: iconsProvider.parametrizedIcon(
                  iconsProvider.messageNew,
                  width: iconSide,
                  height: iconSide,
                ),
                selectedIcon: iconsProvider.parametrizedIcon(
                  iconsProvider.messageNewSelected,
                  width: iconSide,
                  height: iconSide,
                  color: ThemeColors.color26467A,
                ),
                text: messageTabLabel,
              ),
              FABBottomAppBarItem(
                icon: iconsProvider.parametrizedIcon(
                  iconsProvider.profile,
                  width: iconSide,
                  height: iconSide,
                ),
                selectedIcon: iconsProvider.parametrizedIcon(
                  iconsProvider.profileSolid,
                  width: iconSide,
                  height: iconSide,
                  color: ThemeColors.color26467A,
                ),
                text: profileTabLabel,
              )
            ],
          ),
        ),
        body: Stack(
          children: <Widget>[
            PageView(
              controller: _controller,
              onPageChanged: onPageChanged,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                StudentSearchScreen(),
                isLoggedIn == true
                    ? StudentLessonsScreen()
                    : LoginPage(
                        authArgument: arguments,
                      ),
                Container(),
                isLoggedIn == true
                    ? MessagesScreen()
                    : LoginPage(
                        authArgument: arguments,
                      ),
                isLoggedIn == true
                    ? StudentProfileScreen()
                    : LoginPage(
                        authArgument: arguments,
                      )
              ],
            ),
          ],
        ),
      ),
    );
  }

  void onPageChanged(int page) {
    setState(() {
      HapticFeedback.lightImpact();
      _currentIndex = page;
    });
  }

  Future<void> onTabTapped(int? index) async {
    final previousIndex = _currentIndex;
    if (index == 2) {
      launchWhatsApp(
          phone: phoneNumber ?? '', message: whatsappStarterMessage ?? '');
      _currentIndex = previousIndex;
      setState(() {});
    } else {
      setState(() {
        _controller.jumpToPage(index!);
        _currentIndex = index;
      });
    }
  }

  void loadScreenLabels() {
    final language = localizationProvider.locals.language;
    fabLabel = localizationProvider.resources.getWithKey(NAV_SUPPORT);
    lessonsTabLabel = localizationProvider.resources.getWithKey(FORMS_LESSONS);
    messageTabLabel = localizationProvider.resources.getWithKey(FORMS_MESSAGES);
    profileTabLabel =
        localizationProvider.resources.getWithKey(FORMS_MYPROFILE);
    homeTabLabel = splitLabel(language);
    loginTabLabel = localizationProvider.resources.getWithKey(NAV_SIGNIN);
    phoneNumber =
        localizationProvider.resources.getWithKey(CONTACTUS_WHATSAPPNUMBER);
    whatsappStarterMessage = localizationProvider.resources
        .getWithKey(MOBILE_WHATSAPPSTARTUPMESSAGE);
  }

  String splitLabel(language) {
    return language == 'en'
        ? localizationProvider.resources
            .getWithKey(NAV_HOMEPAGE)
            .toString()
            .split(' ')
            .first
        : localizationProvider.resources
            .getWithKey(NAV_HOMEPAGE)
            .toString()
            .split(' ')
            .last;
  }

  Future<void> _checkComeFromWeb() async {
    await Future.delayed(const Duration(milliseconds: 500));
    final url = getIt<Storage>().urlComeFromWeb ?? '';
    final autProvider = StaticVar.context.read<AuthenticationProvider>();
    if (autProvider.isLoggedIn && url.isNotEmpty) {
      getIt<Storage>().urlComeFromWeb = '';
      await StaticVar.context.read<StudentDashboardProvider>().resetLastPage();
      DeepLinking.deepLinking.handleURI(
        Uri.parse(url),
        removeBack: false,
      );
    }
  }

  Future<void> _checkAnnouncement() async {
    final commonProvider = context.read<CommonProvider>();
    final autProvider = context.read<AuthenticationProvider>();
    if (autProvider.isLoggedIn) {
      final announcement = await commonProvider.announcement(
          autProvider.accessToken ?? '', Shard().languageId);
      if (announcement != null &&
          (announcement.hasAnnouncement ?? false) &&
          (announcement.announcementUrl ?? '').isNotEmpty) {
        if (announcement.showFull ?? false) {
          await Navigator.push<void>(
            StaticVar.context,
            MaterialPageRoute(
              builder: (context) => NotificationFullScreen(
                url: announcement.announcementUrl ?? '',
                fromAnnouncement: true,
                announcementModel: announcement,
              ),
            ),
          );
          _checkComeFromWeb();
        } else {
          await showDialog(
            context: StaticVar.context,
            builder: (_) => NotificationPopUp(
              url: announcement.announcementUrl ?? '',
              fromAnnouncement: true,
              announcementModel: announcement,
            ),
          );
          _checkComeFromWeb();
        }
      } else {
        _checkComeFromWeb();
      }
    } else {
      _checkComeFromWeb();
    }
  }

  void _loadNotification() {
    final authProvider = StaticVar.context.read<AuthenticationProvider>();
    if (authProvider.isLoggedIn) {
      StaticVar.context
          .read<InstructorDashboardProvider>()
          .getMyNotifications(authProvider.accessToken, 0, 15);
      StaticVar.context
          .read<MessagesProvider>()
          .getUserChat(authProvider.accessToken, 1);
    }
  }

  void _getVoiceSetting() {
    StaticVar.context.read<InstructorDashboardProvider>().getVoiceSetting();
  }

  Future<void> _initDeepLink() async {
    deepLinkStream = DeepLinking.deepLinking.listenDynamicLinks();
  }

  AppBar _buildBackButton() {
    return AppBar(
      backgroundColor: ThemeColors.white,
      elevation: 0,
      leading: _buildLeading(),
    );
  }

  Widget _buildLeading() {
    return IconButton(
      onPressed: () {
        getIt<Storage>().instructorComeFromViewSimilarTutors = false;
        NavigationService.instance
            .navigateToAndRemove(InstructorHomeScreen.routeName);
      },
      icon: _buildArrow(),
    );
  }

  Widget _buildArrow() {
    return FlipIfRTL(builder: (context) {
      return SvgPicture.asset(
        Images.bookingBackIcon,
        width: 24.w,
        height: 24.w,
      );
    });
  }

  Future<bool> _onWillPop() async {
    if (StaticVar.isStudent.inverted &&
        getIt<Storage>().instructorComeFromViewSimilarTutors) {
      NavigationService.instance
          .navigateToAndRemove(InstructorHomeScreen.routeName);
      return false;
    } else {
      // will close app
      return true;
    }
  }
}
