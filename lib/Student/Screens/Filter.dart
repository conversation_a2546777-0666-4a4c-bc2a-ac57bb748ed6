import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/DependencyManager/TextProviders/TextProvider.dart';
import 'package:modarby/Providers/SearchProvider.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:provider/provider.dart';

import '../../Enums/FontWeights.dart';
import '../../Models/Search/SearchListDTO.dart';
import '../../Widgets/ProfileSelectBox.dart';
import '../../Widgets/SimpleAppBar.dart';
import '../../Widgets/SubmitButton.dart';
import '../../Widgets/SwitchButtonCardGroup.dart';

class StudentFilterScreen extends StatefulWidget {
  static const routeName = '/StudentFilter';

  @override
  State<StudentFilterScreen> createState() => _StudentFilterScreenState();
}

class _StudentFilterScreenState extends State<StudentFilterScreen> {
  late TextProvider _textProvider;
  late IconsProvider _iconsProvider;

  late LocalizationProvider _localizationProvider;

  late SearchProvider _searchProvider;
  SearchListDTO? _currentQuery;

  String? _currency;
  List? _nationalities;
  List? _genders;

  String? _title;
  String? _resetLabel;

  String? _priceLabel;
  String? _nationalityLabel;
  String? _genderLabel;
  String? _defaultSelection;
  // String? _insbLabel;
  String? _arabicSpeakerLabel;
  String? _hasVideoLabel;
  String? _hasPackagesLabel;
  String? _hasMasterExperienceLabel;

  String? _enabledLabel;
  String? _disabledLabel;

  String? _submitLabel;

  int? _selectedMode;

  late double _minPrice;
  late double _maxPrice;
  RangeValues? _rangeValues;

  final _onlineModeId = 1;

  final _nationalityController = TextEditingController();
  final _genderController = TextEditingController();

  // final _insbController = TextEditingController();
  final _arabicSpeakerController = TextEditingController();
  final _hasVideoController = TextEditingController();
  final _hasPackagesController = TextEditingController();
  final _hasMasterExperienceController = TextEditingController();

  final _bodyHorizontalMargin = 15.0;
  final _bodyVerticalMargin = 15.0;
  final _contentMargin = 15.0;
  final _internalMargin = 10.0;

  final _iconSize = 16.0;
  final _buttonHeight = 50.0;

  @override
  void initState() {
    _initializeData();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    _initializeWatchers();
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar() as PreferredSizeWidget?,
      body: Container(
        width: MediaQuery.of(context).size.width,
        margin: EdgeInsets.symmetric(horizontal: _bodyHorizontalMargin),
        child: _buildBody(),
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildAppBar() {
    return SimpleAppBar(
      title: _title,
      trailing: _buildReset(),
    );
  }

  Widget _buildReset() {
    return ActionChip(
      backgroundColor: ThemeColors.grayE5E5EA,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: ThemeColors.grayE5E5EA,
        ),
      ),
      avatar: _iconsProvider.reset,
      label: _textProvider.buildNormalText3(_resetLabel),
      onPressed: _reset,
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: _bodyVerticalMargin),
          _buildPriceField(),
          SizedBox(height: _contentMargin),
          _buildGenderField(),
          SizedBox(height: _contentMargin),
          _buildNationalityField(),
          SizedBox(height: _contentMargin),
          // _buildCurriculumField(),
          // SizedBox(height: _contentMargin * 2),
          // _buildInsbField(),
          // SizedBox(height: _contentMargin),
          // _buildArabicSpeakerField(),
          // SizedBox(height: _contentMargin),
          // _buildHasVideoField(),
          // SizedBox(height: _contentMargin),
          _buildHasPackagesField(),
          SizedBox(height: _contentMargin),
          _buildHasMasterExperienceField(),
          SizedBox(height: _bodyVerticalMargin),
        ],
      ),
    );
  }

  Widget _buildPriceField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildPriceLabel(),
        SizedBox(height: _internalMargin),
        _buildPriceRange(),
        SizedBox(height: _internalMargin),
        _buildPriceSlider(),
      ],
    );
  }

  Widget _buildPriceLabel() {
    return _textProvider.buildNormalText3(
      _priceLabel,
      weight: FontWeights.medium,
    );
  }

  Widget _buildPriceRange() {
    return Center(
      child: _textProvider.buildNormalText3(
        '${_rangeValues?.start.toStringAsFixed(2)} $_currency - ${_rangeValues?.end.toStringAsFixed(2)} $_currency',
        weight: FontWeights.medium,
      ),
    );
  }

  Widget _buildPriceSlider() {
    return RangeSlider(
      min: _minPrice,
      max: _maxPrice,
      values: _rangeValues ?? const RangeValues(100, 1000),
      onChanged: _sliderOnChange,
      activeColor: ThemeColors.accentColor,
      inactiveColor: ThemeColors.defaultBorderColor,
    );
  }

  Widget _buildNationalityField() {
    return ProfileSelectBox(
      label: _nationalityLabel,
      options: [
        _defaultSelection,
        ..._nationalities!.map((nationality) => nationality.name)
      ].toList(),
      controller: _nationalityController,
      isSearchable: true,
    );
  }

  Widget _buildGenderField() {
    return ProfileSelectBox(
      label: _genderLabel,
      options: [_defaultSelection, ..._genders!.map((gender) => gender.name)]
          .toList(),
      controller: _genderController,
      isSearchable: false,
    );
  }

  // Widget _buildInsbField() {
  //   return _buildSwitch(
  //     _iconsProvider.survey,
  //     _insbLabel,
  //     _insbController,
  //   );
  // }

  Widget _buildArabicSpeakerField() {
    return _buildSwitch(
      _iconsProvider.language,
      _arabicSpeakerLabel,
      _arabicSpeakerController,
    );
  }

  Widget _buildHasVideoField() {
    return _buildSwitch(
      _iconsProvider.film,
      _hasVideoLabel,
      _hasVideoController,
    );
  }

  Widget _buildHasPackagesField() {
    return _buildSwitch(
      _iconsProvider.myPackages,
      _hasPackagesLabel,
      _hasPackagesController,
    );
  }

  Widget _buildHasMasterExperienceField() {
    return _buildSwitch(
      _iconsProvider.award,
      _hasMasterExperienceLabel,
      _hasMasterExperienceController,
    );
  }

  Widget _buildSwitch(
      Widget icon, String? label, TextEditingController controller) {
    return SwitchButtonCardGroup(
      icon: _buildSwitchIcon(icon),
      cardTitle: label,
      controller: controller,
      tabOneTitle: _enabledLabel,
      tabTwoTitle: _disabledLabel,
      isGender: false,
    );
  }

  Widget _buildSwitchIcon(Widget icon) {
    return _iconsProvider.parametrizedIcon(
      icon,
      width: _iconSize,
      fit: BoxFit.fitWidth,
    );
  }

  Widget _buildBottomBar() {
    return Padding(
      padding: EdgeInsets.all(_bodyHorizontalMargin),
      child: _buildSubmit(),
    );
  }

  Widget _buildSubmit() {
    return SizedBox(
      height: _buttonHeight,
      child: SubmitButton(
        color: ThemeColors.color26467A,
        text: _submitLabel,
        behaviour: _submit,
      ),
    );
  }

  void _initializeData() {
    _textProvider = context.read<DependencyManager>().text;
    _iconsProvider = context.read<DependencyManager>().icons;
    _localizationProvider = context.read<DependencyManager>().localization;

    _searchProvider = context.read<SearchProvider>();

    _currency = _localizationProvider.locals.currency?.name;

    _nationalities = _localizationProvider.lookups.getWithKey(NATIONALITY);
    _genders = _localizationProvider.lookups.getWithKey(GENDER);

    _title = _localizationProvider.resources.getWithKey(FILTERS_FILTERSTITLE);
    _resetLabel =
        _localizationProvider.resources.getWithKey(FILTERS_RESETFILTER);

    _priceLabel =
        _localizationProvider.resources.getWithKey(FILTERS_PRICERANGE);

    _nationalityLabel =
        _localizationProvider.resources.getWithKey(FILTERS_NATIONALITY);
    _genderLabel = _localizationProvider.resources.getWithKey(FILTERS_GENDER);
    _defaultSelection =
        _localizationProvider.resources.getWithKey(FILTERS_SELECT);

    // _insbLabel =
    //     _localizationProvider.resources.getWithKey(FILTERS_INSTANTBOOKING);
    _arabicSpeakerLabel =
        _localizationProvider.resources.getWithKey(FILTERS_SPEAKARABIC);
    _hasVideoLabel =
        _localizationProvider.resources.getWithKey(FILTERS_HASVIDEO);
    _hasPackagesLabel =
        _localizationProvider.resources.getWithKey(FILTERS_HASPACKAGES);
    _hasMasterExperienceLabel =
        _localizationProvider.resources.getWithKey(FILTERS_HASMASTEREXPERIENCE);

    _enabledLabel = _localizationProvider.resources.getWithKey(FILTER_YES);
    _disabledLabel = _localizationProvider.resources.getWithKey(FILTER_NO);

    _submitLabel =
        _localizationProvider.resources.getWithKey(FILTERS_APPLYFILTER);
  }

  void _initializeWatchers() {
    _currentQuery = context.watch<SearchProvider>().currentSearchQuery;
    if (_currentQuery != null) _fillData();
  }

  void _fillData() {
    _selectedMode = _currentQuery!.mode;
    _minPrice = min(_searchProvider.results?.minPriceCalculated ?? 100,
        _currentQuery?.minPrice?.toDouble() ?? 0);
    _maxPrice = max(_searchProvider.results?.maxPriceCalculated ?? 1000,
        _currentQuery?.maxPrice?.toDouble() ?? 0);
    _rangeValues = RangeValues(
      _currentQuery?.minPrice?.toDouble() ??
          _searchProvider.results?.minPriceCalculated ??
          100,
      _currentQuery?.maxPrice?.toDouble() ??
          _searchProvider.results?.maxPriceCalculated ??
          1000,
    );
    if (_currentQuery!.nationality != null) {
      _nationalityController.text = (_nationalities ?? []).firstWhereOrNull(
            (nationality) {
              return nationality.id == _currentQuery!.nationality;
            },
          )?.name ??
          '';
    }
    if (_currentQuery!.gender != null) {
      _genderController.text = (_genders ?? []).firstWhereOrNull((gender) {
            return gender.id == _currentQuery!.gender;
          })?.name ??
          '';
    }

    // _insbController.text = _putSwitch(_currentQuery!.isInstantBooking);
    _arabicSpeakerController.text = _putSwitch(_currentQuery!.isArabicSpeaker);
    _hasVideoController.text = _putSwitch(_currentQuery!.hasVideo);
    _hasPackagesController.text = _putSwitch(_currentQuery!.hasPackages);
    _hasMasterExperienceController.text =
        _putSwitch(_currentQuery!.hasMasterExperience);
  }

  Future<void> _reset() async {
    _selectedMode = _onlineModeId;
    _rangeValues = null;

    // _curriculumController.text = _defaultSelectionCurriculum ?? '';
    _nationalityController.text = _defaultSelection ?? '';
    _genderController.text = _defaultSelection ?? '';

    // _insbController.text = _putSwitch(false);
    _arabicSpeakerController.text = _putSwitch(false);
    _hasVideoController.text = _putSwitch(false);
    _hasPackagesController.text = _putSwitch(false);
    _hasMasterExperienceController.text = _putSwitch(false);

    final dto = _generateDTO();
    Navigator.pop(context, dto);
  }

  void _sliderOnChange(RangeValues values) {
    setState(() => _rangeValues = values);
  }

  Future<void> _submit() async {
    final dto = _generateDTO();
    Navigator.pop(context, dto);
  }

  SearchListDTO _generateDTO() {
    final dto = _currentQuery!;
    dto.index = 0;
    dto.mode = _selectedMode;
    dto.minPrice = _rangeValues?.start.floor();
    dto.maxPrice = _rangeValues?.end.floor();
    dto.nationality = (_nationalities ?? [])
        .firstWhereOrNull(
          (nationality) => nationality.name == _nationalityController.text,
        )
        ?.id;
    dto.gender = (_genders ?? [])
        .firstWhereOrNull(
          (gender) => gender.name == _genderController.text,
        )
        ?.id;
    // dto.isInstantBooking = _parseSwitch(_insbController.text);
    dto.isArabicSpeaker = _parseSwitch(_arabicSpeakerController.text);
    dto.hasVideo = _parseSwitch(_hasVideoController.text);
    dto.hasPackages = _parseSwitch(_hasPackagesController.text);
    dto.hasMasterExperience = _parseSwitch(_hasMasterExperienceController.text);
    return dto;
  }

  bool _parseSwitch(String value) {
    if (value == '1') return false;
    return true;
  }

  String _putSwitch(bool? value) {
    if (value != null && value) return '0';
    return '1';
  }
}
