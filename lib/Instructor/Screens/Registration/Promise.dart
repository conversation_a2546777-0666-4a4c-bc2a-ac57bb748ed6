// ignore_for_file: must_be_immutable

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modarby/Enums/Roles.dart';
import 'package:modarby/Instructor/Screens/Instructor_home.dart';
import 'package:modarby/Instructor/Screens/Registration/SignupAbout.dart';
import 'package:modarby/Models/Authentication/RegisterDTO.dart';
import 'package:modarby/Models/InstructorProfile/GetProfileDTO.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/CollectDataProvider.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/DependencyManager/TextProviders/TextProvider.dart';
import 'package:modarby/Providers/InstructorDashboardProvider.dart';
import 'package:modarby/Providers/InstructorProfileProvider.dart';
import 'package:modarby/Providers/MessagesProvider.dart';
import 'package:modarby/Providers/StudentProfileProvider.dart';
import 'package:modarby/SharedScreens/NotificationsPermission.dart';
import 'package:modarby/Student/Screens/Home.dart';
import 'package:modarby/Widgets/custom_button_widget.dart';
import 'package:modarby/Widgets/sticky_bottom_appbar.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/config/themes/images.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/utilities/error_parser.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/navigation_service.dart';
import 'package:modarby/core/utilities/shard.dart';
import 'package:modarby/core/utilities/storage.dart';
import 'package:modarby/features/auth/arguments/auth_argument.dart';
import 'package:modarby/features/auth/widgets/custom_app_bar_auth_widget.dart';
import 'package:modarby/features/first_installation/start_installation_page.dart';
import 'package:provider/provider.dart';

import '../../../Models/Authentication/LoginDTO.dart';
import '../../../Models/Authentication/StudentRegisterDTO.dart';
import '../../../Widgets/ConfirmationDialog.dart';
import '../../../core/Utilities/ProgressIndicators.dart';
import '../../../core/Utilities/Snackbars.dart';
import 'OnlineTermsLink.dart';

class InstructorPromisePage extends StatefulWidget {
  static const routeName = '/InstructorPromisePage';
  final AuthArgument? argument;
  InstructorPromisePage({
    super.key,
    required this.argument,
  });

  @override
  State<InstructorPromisePage> createState() => _InstructorPromisePageState();
}

class _InstructorPromisePageState extends State<InstructorPromisePage> {
  late TextProvider _textProvider;

  late IconsProvider _iconsProvider;

  late LocalizationProvider _localizationProvider;

  late InstructorProfileProvider _instructorProfileProvider;

  late InstructorDashboardProvider _instructorDashboardProvider;

  late AuthenticationProvider _authProvider;

  late StudentProfileProvider _studentProvider;

  late MessagesProvider _messagesProvider;

  late String? _screenTitle;

  late String? _sectionOneTitle;

  late String? _sectionOneSubtitle;

  late String? _sectionTwoText;

  late String? _sectionThreeText;

  late String? _sectionTFourthText;

  late String? _acceptButtonLabel;

  late String? _refuseButtonLabel;

  late String? _dialogTitle;

  late String? _dialogSubmitLabel;

  final _contentMargin = 15.0;

  final _textMargin = 8.0;

  int _currentIconIndex = 0;
  final List<String> _icons = [
    Images.promiseAnimationIcon3,
    Images.promiseAnimationIcon2,
  ];

  void _startIconAnimation() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _currentIconIndex = (_currentIconIndex + 1) % _icons.length;
        });
        _startIconAnimation();
      }
    });
  }

  @override
  void initState() {
    // TODO: implement initState
    _startIconAnimation();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    _initializeData(context);
    return Scaffold(
      backgroundColor: ThemeColors.white,
      body: _buildBody(context),
      bottomNavigationBar: _bottom(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return SafeArea(
      child: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: _buildContent(context),
      ),
    );
  }

  Widget _appBar() {
    return CustomAppBarAuthWidget(
      title: _screenTitle,
      showBackTitle: true,
      showBack: widget.argument?.showBackInAdditionalInfo ?? true,
      showClose: false,
      showTitle: true,
      valueProgress: 4.9 / 5,
      onClickClose: _onClickClose,
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        _appBar(),
        Expanded(
          child: Stack(
            alignment: Alignment.center,
            children: [
              Positioned.fill(
                top: 50.sp,
                child: ClipPath(
                  clipper: MyClipper(),
                  child: Container(
                    padding: EdgeInsetsDirectional.only(top: 90.sp),
                    color: ThemeColors.colorF8F8F8,
                    child: _content(context),
                  ),
                ),
              ),
              PositionedDirectional(
                top: 9.sp,
                child: _buildAnimationIcons(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAnimationIcons() {
    return Center(
      child: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Images.promiseAnimationIcon1),
          ),
        ),
        child: ClipRect(
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 600),
            transitionBuilder: (Widget child, Animation<double> animation) {
              return SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0.0, 2.0),
                  end: Offset.zero,
                ).animate(CurvedAnimation(
                  parent: animation,
                  curve: Curves.easeInOut,
                  // reverseCurve: Curves.easeOutBack,
                )),
                child: FadeTransition(
                  opacity: CurvedAnimation(
                    parent: animation,
                    curve: Curves.easeInOut,
                  ),
                  child: child,
                ),
              );
            },
            child: Image.asset(
              _icons[_currentIconIndex],
              key: ValueKey<int>(_currentIconIndex),
              height: 108.sp,
              width: 77.w,
            ),
          ),
        ),
      ),
    );
  }

  Widget _bottom(BuildContext context) {
    return StickyBottomAppBar(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _agreeWithYes(context),
              _notAgreeWithCancel(context),
            ],
          ),
        ],
      ),
    );
  }

  Widget _notAgreeWithCancel(BuildContext context) {
    return CustomButtonWidget(
      margin: EdgeInsetsDirectional.only(
        start: 8.w,
      ),
      padding: EdgeInsetsDirectional.only(
        top: 14.sp,
        bottom: 14.sp,
        start: 8.w,
        end: 8.w,
      ),
      onPressed: () => _confirmDisagree(context),
      title: _refuseButtonLabel,
      titleColor: ThemeColors.black,
      fontWeightTitle: FontWeight.w600,
      sizeTitle: 15,
      radius: 14,
      colorButton: ThemeColors.colorF2F2F7,
      enabled: true,
    );
  }

  Widget _agreeWithYes(BuildContext context) {
    return Expanded(
      child: CustomButtonWidget(
        colorButton: ThemeColors.color26467A,
        padding: EdgeInsetsDirectional.only(
          top: 14.sp,
          bottom: 14.sp,
          start: 8.w,
          end: 8.w,
        ),
        onPressed: getIt<Storage>().role == Roles.STUDENT ||
                getIt<Storage>().role == Roles.PARENT
            ? () => _checkNotificationAndRegister(context)
            : () => _checkNotificationAndRegisterTutor(context),
        title: _acceptButtonLabel,
        titleColor: ThemeColors.white,
        fontWeightTitle: FontWeight.w600,
        sizeTitle: 15,
        radius: 14,
        enabled: true,
      ),
    );
  }

  Widget _buildSectionOne() {
    return Column(
      children: [
        _textProvider.buildNormalText1(
          _sectionOneTitle,
          weight: FontWeight.w500,
          color: ThemeColors.color1C1C1E,
          align: TextAlign.center,
          spEnabled: true,
        ),
        SizedBox(height: _textMargin),
        _textProvider.buildNormalText2(
          _sectionOneSubtitle,
          weight: FontWeight.w700,
          color: ThemeColors.color1C1C1E,
          align: TextAlign.center,
          spEnabled: true,
        ),
      ],
    );
  }

  Widget _buildDescription(BuildContext context) {
    return Column(
      children: [
        _buildSectionTwo(),
        SizedBox(height: _contentMargin),
        _buildSectionThree(),
        SizedBox(height: _contentMargin),
        _buildSectionTerms(context),
      ],
    );
  }

  Widget _buildSectionTwo() {
    return _textProvider.buildNormalText2(
      _sectionTwoText,
      align: TextAlign.center,
      color: ThemeColors.color636366,
      weight: FontWeight.w500,
      spEnabled: true,
    );
  }

  Widget _buildSectionThree() {
    return _textProvider.buildNormalText2(
      _sectionTFourthText,
      align: TextAlign.center,
      color: ThemeColors.color636366,
      weight: FontWeight.w500,
      spEnabled: true,
    );
  }

  // Widget _buildSectionThree() {
  //   String addLineBeforeWord(String text, String targetWord) {
  //     List<String> words = text.split(' ');
  //     for (int i = 0; i < words.length; i++) {
  //       if (words[i] == targetWord || words[i] == 'pledge') {
  //         final l = words[i].length;
  //         //List<int> numberList = createNumberList(endNumber);

  //         words[i] = '\n\n${words[i]}';
  //       }
  //     }
  //     return words.join(' ');
  //   }

  //   String addEmptyLinesBeforeNumbers(String text) {
  //     List<String> lines = text.split('. ');
  //     List<String> modifiedLines = [];
  //     for (String line in lines) {
  //       modifiedLines.add('\n\n$line');
  //     }
  //     return modifiedLines.join('. ');
  //   }

  //   return _textProvider.buildNormalText2(
  //     addEmptyLinesBeforeNumbers(
  //       _sectionThreeText.toString(),
  //     ),
  //     align: TextAlign.center,
  //     weight: FontWeight.w500,
  //     color: ThemeColors.color636366,
  //     spEnabled: true,
  //   );
  // }

  bool get isAr => _localizationProvider.locals.language == 'ar';

  Widget _buildSectionTerms(BuildContext context) {
    // String addLineBeforeWord(String text, String targetWord) {
    //   List<String> words = text.split(' ');
    //   for (int i = 0; i < words.length; i++) {
    //     if (words[i] == targetWord) {
    //       words[i] = '\n${words[i]}';
    //     }
    //   }
    //   return words.join(' ');
    // }

    return RichText(
        text: TextSpan(
      //text: addLineBeforeWord(_sectionThreeText.toString(), 'اتعهد'),
      text: _sectionThreeText,
      style: const TextStyle(
          color: ThemeColors.blueStatusText,
          fontWeight: FontWeight.w500,
          fontFamily: 'Cairo',
          fontSize: 16),
      recognizer: TapGestureRecognizer()
        ..onTap = () {
          Navigator.pushNamed(
            context,
            InstructorOnlineTermsLink.routeName,
          );
          //launch(isAr ? 'https://www.modarby.com/ar/instructor-policy' : 'https://www.modarby.com/en/instructor-policy');
        },
    ));
  }

  Future<void> _confirmDisagree(BuildContext context) async {
    final isConfirmed = await showDialog(
      context: context,
      builder: (_) => ConfirmationDialog(
        submitColor: ThemeColors.color26467A,
        colorCancel: ThemeColors.white,
        colorBorderCancel: ThemeColors.grayE5E5EA,
        colorCancelLabel: ThemeColors.black,
        icon: _iconsProvider.warning,
        title: _dialogTitle,
        submitLabel: _dialogSubmitLabel,
        hasCancel: true,
      ),
    );
    if (isConfirmed) {
      getIt<Storage>().clearDataWizard();
      NavigationService.instance
          .navigateToAndRemove(StartInstallationPage.routeName);
    }
  }

  Future<void> _checkNotificationAndRegisterTutor(BuildContext context) async {
    final notificationEnabled =
        await context.read<CollectDataProvider>().getNotificationsAllowed();

    if (notificationEnabled) {
      _registerInstructor(context);
    } else {
      widget.argument?.fromUpdate = true;
      await NavigationService.instance.navigateTo(
        NotificationsPermissionScreen.routeName,
        args: widget.argument,
      );
      _registerInstructor(context);
    }
  }

  Future<void> _registerInstructor(BuildContext context) async {
    ProgressIndicators.loadingDialog(context);
    try {
      final dto = _generateRegisterDTOInstructor(context);
      await _authProvider.registerInstructor(dto ?? RegisterDTO());
      await _loginInstructor(context);
      Navigator.pushNamedAndRemoveUntil(
              context, InstructorSignupAboutScreen.routeName, (route) => false,
              arguments: true)
          .then((value) {
        NavigationService.instance
            .navigateToAndRemove(InstructorHomeScreen.routeName, args: {});
      });
    } catch (error) {
      final errorException = ErrorParser().parseError(error);

      Navigator.pop(context);
      Snackbars.danger(context, errorException.message);
    }
  }

  Future<void> _registerStudent(BuildContext context) async {
    ProgressIndicators.loadingDialog(context);
    try {
      final dto = _generateRegisterDTO(context);
      await _authProvider.registerStudent(dto);
      await _loginStudent(context);
      Shard().doSearch(
        widget.argument?.firstInstallationArguments,
      );
      Navigator.pop(context);
      getIt<Storage>().registrationDataWizard = true;
      _goToRouteSpecific(context);
    } catch (error) {
      Navigator.pop(context);
      final errorException = ErrorParser().parseError(error);
      Snackbars.danger(context, errorException.message);
    }
  }

  RegisterDTO? _generateRegisterDTOInstructor(BuildContext context) {
    final RegisterDTO? dto = widget.argument?.registerDTO;
    dto?.currencyId = _localizationProvider.locals.currency?.id;
    return dto;
  }

  StudentRegisterDTO? _generateRegisterDTO(BuildContext context) {
    final StudentRegisterDTO? dto = widget.argument?.studentRegisterDTO;
    dto?.currencyId = _localizationProvider.locals.currency?.id;
    return dto;
  }

  Future _loginStudent(BuildContext context) async {
    try {
      final dto = _generateLoginDTO(context);
      await _authProvider.login(dto);
      await _loadProfile(dto.languageId);
      await _loadMessages();
    } catch (error) {
      Navigator.pop(context);
      final errorException = ErrorParser().parseError(error);
      Snackbars.danger(context, errorException.message);
    }
  }

  Future _loginInstructor(BuildContext context) async {
    try {
      final dto = _generateLoginInstructorDTO(context);
      await _authProvider.login(dto);
      await _loadProfileInstructor(dto.languageId);
      await _loadMessages();
      Navigator.pop(context);
    } catch (error) {
      Navigator.pop(context);
      final errorException = ErrorParser().parseError(error);
      Snackbars.danger(context, errorException.message);
    }
  }

  LoginDTO _generateLoginDTO(BuildContext context) {
    final StudentRegisterDTO dto =
        widget.argument?.studentRegisterDTO ?? StudentRegisterDTO();
    return LoginDTO(
      languageId: dto.languageId,
      countryCodeId: dto.countryCodeId,
      identifier: dto.phoneNumber,
      password: dto.pinCode,
      deviceToken: dto.deviceToken,
      loginMethod: LoginDTO.WHATSAPP_METHOD_KEY,
    );
  }

  LoginDTO _generateLoginInstructorDTO(BuildContext context) {
    final RegisterDTO dto = widget.argument?.registerDTO ?? RegisterDTO();
    return LoginDTO(
      languageId: dto.languageId,
      countryCodeId: dto.countryCodeId,
      identifier: dto.phoneNumber,
      password: dto.pinCode,
      deviceToken: dto.deviceToken,
      loginMethod: LoginDTO.WHATSAPP_METHOD_KEY,
    );
  }

  Future<void> _loadProfile(int? languageId) async {
    await _studentProvider.getProfile(
      _authProvider.accessToken,
      languageId,
    );
  }

  Future<void> _loadProfileInstructor(int? languageId) async {
    final dto = _generateInstructorProfileDTO();
    await _instructorProfileProvider.getProfile(dto);
  }

  GetProfileDTO _generateInstructorProfileDTO() {
    return GetProfileDTO(
      accessToken: _authProvider.accessToken,
      languageId: _localizationProvider.locals.languageId,
      currencyId: _localizationProvider.locals.currency?.id,
    );
  }

  Future<void> _loadMessages() async {
    await _messagesProvider.getUserChat(_authProvider.accessToken, 1);
  }

  void _initializeData(BuildContext context) {
    _textProvider = context.watch<DependencyManager>().text;
    _iconsProvider = context.watch<DependencyManager>().icons;

    _localizationProvider = context.watch<DependencyManager>().localization;
    _instructorProfileProvider = context.read<InstructorProfileProvider>();
    _instructorDashboardProvider = context.watch<InstructorDashboardProvider>();
    _authProvider = context.watch<AuthenticationProvider>();
    _studentProvider = context.watch<StudentProfileProvider>();
    _messagesProvider = context.watch<MessagesProvider>();

    _screenTitle = _localizationProvider.resources
        .getWithKey(INSTRUCTOR_COMMITMENT_TOPHEADER);
    _sectionOneTitle = _localizationProvider.resources
        .getWithKey(INSTRUCTOR_COMMITMENT_FIRSTTITLE);
    _sectionOneSubtitle = _localizationProvider.resources
        .getWithKey(INSTRUCTOR_COMMITMENT_SECONDTITLE);
    _sectionTwoText = _localizationProvider.resources
        .getWithKey(INSTRUCTOR_COMMITMENT_FIRSTDESCRIPTION);
    _sectionThreeText = _localizationProvider.resources
        .getWithKey(INSTRUCTOR_COMMITMENT_SECONDDESCRIPTION);
    _sectionTFourthText = _localizationProvider.resources
        .getWithKey(INSTRUCTOR_COMMITMENT_THIRDDESCRIPTION);
    _acceptButtonLabel =
        _localizationProvider.resources.getWithKey(COMMITMENT_AGREE);
    _refuseButtonLabel =
        _localizationProvider.resources.getWithKey(COMMITMENT_CANCEL);

    _dialogTitle = _localizationProvider.resources
        .getWithKey(REGISTRATION_CANCELREGISTRATIONHEADER);
    _dialogSubmitLabel = _localizationProvider.resources.getWithKey(COMMON_YES);
  }

  void _onClickClose() {}

  Widget _content(BuildContext context) {
    return Padding(
      padding: EdgeInsetsDirectional.only(start: 16.w, end: 16.w),
      child: SingleChildScrollView(
        child: Column(
          children: [
            _buildSectionOne(),
            SizedBox(height: 14.sp),
            _buildDescription(context),
          ],
        ),
      ),
    );
  }

  Future<void> _goToRouteSpecific(BuildContext context) async {
    if (widget.argument?.index != null) {
      NavigationService.instance
          .navigateToAndRemove(StudentHomeScreen.routeName, args: {
        'PAGE_INDEX': widget.argument?.index,
      });
    } else {
      if (widget.argument?.comeFromOtherPlace ?? false) {
        NavigationService.instance.navigateToAndRemove(
            widget.argument?.routeScreen ?? StudentHomeScreen.routeName);
      } else {
        goToMessageOrLessons();
      }
    }
  }

  void goToMessageOrLessons() {
    if ((_messagesProvider.conversationMessages ?? []).isNotEmpty) {
      NavigationService.instance
          .navigateToAndRemove(StudentHomeScreen.routeName, args: {
        'PAGE_INDEX': 3,
      });
    } else {
      NavigationService.instance
          .navigateToAndRemove(StudentHomeScreen.routeName, args: {
        'PAGE_INDEX': 1,
      });
    }
  }

  Future<void> _checkNotificationAndRegister(BuildContext context) async {
    final notificationEnabled =
        await context.read<CollectDataProvider>().getNotificationsAllowed();

    if (notificationEnabled) {
      _registerStudent(context);
    } else {
      widget.argument?.fromUpdate = true;
      await NavigationService.instance.navigateTo(
        NotificationsPermissionScreen.routeName,
        args: widget.argument,
      );
      _registerStudent(context);
    }
  }
}

class MyClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
    path.moveTo(0, size.height);
    path.lineTo(0, size.height * 0.1);
    path.quadraticBezierTo(size.width / 2, 0, size.width, size.height * 0.1);
    path.lineTo(size.width, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return true;
  }
}
