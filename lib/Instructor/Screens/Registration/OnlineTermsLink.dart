import 'package:flutter/material.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../Widgets/SimpleAppBar.dart';
import '../../../core/Utilities/ProgressIndicators.dart';

class InstructorOnlineTermsLink extends StatefulWidget {
  static const routeName = '/InstructorOnlineTermsLink';

  @override
  _InstructorOnlineTermsLinkState createState() =>
      _InstructorOnlineTermsLinkState();
}

class _InstructorOnlineTermsLinkState extends State<InstructorOnlineTermsLink> {
  late WebViewController _controller;

  String? _title;
  bool _showLoading = true;

  @override
  void initState() {
    _initializeData();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    _initializeController();
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: SimpleAppBar(title: _title),
      body: Stack(
        children: [
          _buildWebView(),
          _showLoading ? ProgressIndicators.loadingIndicator() : SizedBox(),
        ],
      ),
    );
  }

  WebViewWidget _buildWebView() {
    return WebViewWidget(controller: _controller);
  }

  void _initializeData() {
    final localizationProvider = context.read<DependencyManager>().localization;
    _title = localizationProvider.resources.getWithKey(FORMS_TERMSOFSERVICE);
  }

  bool get isAr {
    final localizationProvider = context.read<DependencyManager>().localization;
    return localizationProvider.locals.language == 'ar';
  }

  void _initializeController() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(_buildNavigationDelegate())
      ..loadRequest(Uri.parse(isAr
          ? 'https://www.modarby.com/ar/instructor-policy'
          : 'https://www.modarby.com/en/instructor-policy'));
  }

  NavigationDelegate _buildNavigationDelegate() {
    return NavigationDelegate(
      onProgress: _onProgress,
      onPageFinished: _onPageFinished,
    );
  }

  void _onProgress(int progress) {
    if (mounted) {
      setState(() => _showLoading = true);
    }
  }

  void _onPageFinished(String url) {
    if (mounted) {
      setState(() => _showLoading = false);
    }
  }
}
