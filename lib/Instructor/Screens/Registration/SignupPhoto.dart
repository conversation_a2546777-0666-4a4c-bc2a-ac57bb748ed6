// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:modarby/Instructor/Screens/Registration/signup_additional_page/signup_instructor_additional_argument.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/DependencyManager/TextProviders/TextProvider.dart';
import 'package:modarby/Providers/InstructorProfileProvider.dart';
import 'package:modarby/Widgets/InfoMessage.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';
import 'package:provider/provider.dart';

import '../../../Enums/FontWeights.dart';
import '../../../Models/InstructorProfile/Instructor.dart';
import '../../../Widgets/InstructorProfileSubmit.dart';
import '../../../Widgets/ProfileCard.dart';
import '../../../Widgets/ProgressAppBar.dart';
import '../../../core/Utilities/Snackbars.dart';
import '../../../core/config/themes/images.dart';
import 'signup_additional_page/SignupAdditionalInfo.dart';

class InstructorSignupPhotoScreen extends StatelessWidget {
  static const routeName = '/InstructorSignupPhoto';

  late bool _isMissing;

  late TextProvider _textProvider;
  late IconsProvider _iconsProvider;
  late LocalizationProvider _localizationProvider;

  Instructor? _instructor;

  String? _title;
  String? _firstSectionTitle;
  String? _firstSectionDescription;
  String? _firstSectionTip;

  String? _tipsTitle;
  late List<String?> _tips;

  String? _photoRequired;

  final _progressIndicatorValue = 2 / 9;
  final _progressIndicatorTextValue = '2/9';

  final _bodyHorizontalMargin = 15.0;
  final _bodyVerticalMargin = 20.0;
  final _contentMargin = 25.0;
  final _textMargin = 10.0;

  final _tipTileHorizontalPadding = 15.0;
  final _tipTileVerticalPadding = 10.0;

  @override
  Widget build(BuildContext context) {
    _initializeData(context);
    return Scaffold(
      appBar: _buildAppBar() as PreferredSizeWidget?,
      body: Container(
        width: MediaQuery.of(context).size.width,
        padding: EdgeInsets.symmetric(horizontal: _bodyHorizontalMargin),
        child: _buildBody(context),
      ),
    );
  }

  Widget _buildAppBar() {
    return ProgressAppBar(
      title: _title,
      value: _progressIndicatorValue,
      textValue: _progressIndicatorTextValue,
    );
  }

  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              Column(
                children: [
                  SizedBox(height: _bodyVerticalMargin),
                  _buildFirstSection(),
                  SizedBox(height: _contentMargin),
                  _buildTipsSection(),
                  SizedBox(height: _contentMargin),
                ],
              ),
              Positioned.fill(
                child: IgnorePointer(
                  ignoring: isVerified
                      .inverted, // Disable interaction if isWidgetEnabled is false
                  child: Container(
                    color: Colors
                        .transparent, // Set opacity using color's alpha channel
                    // Add any child widgets you want to overlay here
                    // For example, you can add a loading spinner or a message
                  ),
                ),
              ),
            ],
          ),
          _buildSubmitButtons(context),
          SizedBox(height: _bodyVerticalMargin),
        ],
      ),
    );
  }

  bool get isVerified => _instructor?.isVerified ?? false;
  Widget _buildVerifiedNote() {
    return Padding(
      padding: EdgeInsets.only(top: _contentMargin / 2),
      child: InfoMessage(
        icon: SvgPicture.asset(
          Images.infoSignUpTagsNoteIcon,
          width: 20.w,
          height: 20.w,
        ),
        message: noteVerifiedTutorPhoto.translate(),
        backgroundColor: ThemeColors.colorFCEFC6FF,
        foregroundColor: ThemeColors.colorFCEF6FF,
        textColor: ThemeColors.colorB54708,
      ),
    );
  }

  Widget _buildFirstSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTitle(_firstSectionTitle),
        // SizedBox(height: _textMargin),
        // _buildSubTitle(_firstSectionDescription),
        if (isVerified) SizedBox(height: 10.sp),
        if (isVerified) _buildVerifiedNote(),
        SizedBox(height: _contentMargin),
        ProfileCard(isStudent: false, isSamplePhoto: false),
        SizedBox(height: _textMargin),
        _buildSubTitle(_firstSectionTip),
      ],
    );
  }

  Widget _buildTipsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTitle(_tipsTitle),
        SizedBox(height: _contentMargin),
        _iconsProvider.persons,
        SizedBox(height: _contentMargin),
        _buildTips(),
      ],
    );
  }

  Widget _buildTips() {
    return Column(
      children: _tips.map((tip) => _buildTipTile(tip)).toList(),
    );
  }

  Widget _buildTipTile(String? tip) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: _tipTileVerticalPadding),
      child: Row(
        children: [
          _iconsProvider.greenCircleCheck,
          SizedBox(width: _tipTileHorizontalPadding),
          Expanded(child: _textProvider.buildNormalText3(tip)),
        ],
      ),
    );
  }

  Widget _buildSubmitButtons(BuildContext context) {
    return InstructorProfileSubmit(
      skipBehaviour: () => _navigate(context),
      submitBehaviour: () => _submit(context),
    );
  }

  Widget _buildTitle(String? text) {
    return _textProvider.buildTitle2(text, weight: FontWeights.semiBold);
  }

  Widget _buildSubTitle(String? text) {
    return _textProvider.buildNormalText4(text);
  }

  void _submit(BuildContext context) {
    if (_instructor?.picture == null || (_instructor?.picture ?? '').isEmpty) {
      Snackbars.danger(context, _photoRequired ?? '');
      return;
    }
    _navigate(context);
  }

  void _navigate(BuildContext context) {
    if (_isMissing) return Navigator.pop(context);
    final signupInstructorAdditionalArgument =
        SignupInstructorAdditionalArgument(isMissing: _isMissing);
    Navigator.pushNamed(
      context,
      InstructorSignupAdditionalInfoScreen.routeName,
      arguments: signupInstructorAdditionalArgument,
    );
  }

  void _initializeData(BuildContext context) {
    _isMissing = ModalRoute.of(context)!.settings.arguments as bool? ?? false;

    _textProvider = context.watch<DependencyManager>().text;
    _iconsProvider = context.watch<DependencyManager>().icons;
    _localizationProvider = context.watch<DependencyManager>().localization;

    _instructor = context.watch<InstructorProfileProvider>().instructor;

    _title = _localizationProvider.resources.getWithKey(FORMS_PROFILEPHOTO);

    _firstSectionTitle = _localizationProvider.resources
        .getWithKey(FORMS_PHOTOGREATFIRSTIMPRESSION);
    _firstSectionDescription =
        _localizationProvider.resources.getWithKey(FORMS_PHOTOGETMOSTSTUDENTS);
    _firstSectionTip =
        _localizationProvider.resources.getWithKey(FORMS_PHOTOTIPDESC);

    _tipsTitle = _localizationProvider.resources.getWithKey(FORMS_PHOTOTIPS);
    _tips = [
      _localizationProvider.resources.getWithKey(FORMS_PHOTOTIPS1),
      _localizationProvider.resources.getWithKey(FORMS_PHOTOTIPS2),
      _localizationProvider.resources.getWithKey(FORMS_PHOTOTIPS3),
      _localizationProvider.resources.getWithKey(FORMS_PHOTOTIPS4),
      _localizationProvider.resources.getWithKey(FORMS_PHOTOTIPS6),
      _localizationProvider.resources.getWithKey(FORMS_PHOTOTIPS7),
    ];

    _photoRequired = _localizationProvider.resources.getWithKey(FILEREQUIRED);
  }
}
