import 'package:flutter/material.dart';
import 'package:modarby/Instructor/Screens/Registration/signup_additional_page/signup_instructor_additional_argument.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/DependencyManager/TextProviders/TextProvider.dart';
import 'package:modarby/Providers/InstructorProfileProvider.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/utilities/error_parser.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/shard.dart';
import 'package:modarby/core/utilities/storage.dart';
import 'package:provider/provider.dart';

import '../../../Enums/Availability.dart';
import '../../../Instructor/Screens/Registration/SignupDescription.dart';
import '../../../Models/Content/TimeZone.dart';
import '../../../Models/InstructorProfile/AddAvailabilityDTO.dart';
import '../../../Models/InstructorProfile/DayTime.dart';
import '../../../Models/InstructorProfile/GetProfileDTO.dart';
import '../../../Models/InstructorProfile/Instructor.dart';
import '../../../Widgets/InstructorProfileSubmit.dart';
import '../../../Widgets/ProfileSelectBox.dart';
import '../../../Widgets/ProgressAppBar.dart';
import '../../../core/Utilities/ProgressIndicators.dart';
import '../../../core/Utilities/Snackbars.dart';
import '../update_arab_names.dart';
import '../update_select_tags.dart';
import 'signup_additional_page/finish_instructor_need_more_info.dart';

class InstructorSignupAvailabilityScreen extends StatefulWidget {
  static const routeName = '/InstructorSignupAvailability';
  final SignupInstructorAdditionalArgument? argument;

  const InstructorSignupAvailabilityScreen({super.key, this.argument});
  @override
  _InstructorSignupAvailabilityScreenState createState() =>
      _InstructorSignupAvailabilityScreenState();
}

class _InstructorSignupAvailabilityScreenState
    extends State<InstructorSignupAvailabilityScreen> {
  late TextProvider _textProvider;
  late IconsProvider _iconsProvider;

  late LocalizationProvider _localizationProvider;

  late AuthenticationProvider _authProvider;
  late InstructorProfileProvider _profileProvider;

  Instructor? _instructor;
  List<DayTime>? _dayTimes;

  String? _title;
  String? _firstSectionTitle;
  String? _firstSectionDescription;
  String? _secondSectionTitle;
  String? _secondSectionDescription;

  String? _timezoneFieldLabel;
  String? _timezoneFieldDescription;

  String? _availabilityFirstOptionLabel;
  String? _availabilitySecondOptionLabel;

  String? _setAvailabilityLabel;

  String? _fromLabel;
  String? _toLabel;
  String? _addMore;

  String? _timeErrorMessage;
  String? _missingFieldsLabel;

  List<TimeZone>? _timezones;
  TextEditingController? _timezoneController;

  AvailabilityOption? _selectedAvailabilityOption;

  late Map<WeekDay, String?> _weekDaysLabels;
  late Map<WeekDay, List<List<TextEditingController>>> _availableTimes;
  late Map<WeekDay, bool> _errorTimes;

  final _progressIndicatorValue = 6 / 9;
  final _progressIndicatorTextValue = '6/9';

  final _bodyHorizontalMargin = 15.0;
  final _bodyVerticalMargin = 20.0;
  final _contentMargin = 20.0;
  final _internalMargin = 5.0;
  final _dividerThickness = 2.0;

  final _timeSlotPadding = 15.0;
  final _deleteTimeSlotPadding = 35.0;

  final _defaultFromId = 16;
  final _defaultToId = 20;

  @override
  void initState() {
    _initializeData();
    _fillInstructorData();
    _loadData();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    _watchers();
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar() as PreferredSizeWidget?,
      body: Container(
        width: MediaQuery.of(context).size.width,
        padding: EdgeInsets.symmetric(horizontal: _bodyHorizontalMargin),
        child: _buildBody(),
      ),
    );
  }

  Widget _buildAppBar() {
    return ProgressAppBar(
      title: _title,
      value: _progressIndicatorValue,
      textValue: _progressIndicatorTextValue,
    );
  }

  Widget? _buildBody() {
    if (_dayTimes == null) return _buildLoadingIndicator();
    return _buildContent();
  }

  Widget? _buildLoadingIndicator() {
    return ProgressIndicators.loadingIndicator();
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: _bodyVerticalMargin),
          _buildSectionAvailability(
              _firstSectionTitle, _firstSectionDescription),
          SizedBox(height: _contentMargin),
          _buildSection(_secondSectionTitle, _secondSectionDescription),
          SizedBox(height: _contentMargin),
          _buildTimezoneField(),
          SizedBox(height: _contentMargin),
          _buildAvailabilityOptions(),
          SizedBox(height: _contentMargin),
          _buildAvailableTimes(),
          SizedBox(height: _contentMargin),
          _buildSubmitButtons(),
          SizedBox(height: _bodyVerticalMargin),
        ],
      ),
    );
  }

  Widget _buildSection(String? title, String? description) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _textProvider.buildNormalText2(title),
        SizedBox(height: _internalMargin),
        _textProvider.buildNormalText4(
          description,
          color: ThemeColors.darkGrey,
        ),
      ],
    );
  }

  Widget _buildSectionAvailability(String? title, String? description) {
    return _textProvider.buildNormalText3(
      description,
      color: ThemeColors.darkGrey,
    );
  }

  Widget _buildTimezoneField() {
    return ProfileSelectBox(
      label: _timezoneFieldLabel,
      controller: _timezoneController,
      options: _timezones!.map((timezone) => timezone.name).toList(),
    );
  }

  Widget _buildAvailabilityOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _textProvider.buildNormalText2(
          _timezoneFieldDescription,
        ),
        SizedBox(height: _internalMargin),
        _buildAvailabilityOptionTile(
          _availabilityFirstOptionLabel,
          AvailabilityOption.SEND_REQUEST,
        ),
        SizedBox(height: _internalMargin),
        _buildAvailabilityOptionTile(
          _availabilitySecondOptionLabel,
          AvailabilityOption.BOOK_DIRECTLY,
        ),
        SizedBox(height: _internalMargin),
        _buildDivider(),
      ],
    );
  }

  Widget _buildAvailabilityOptionTile(String? title, AvailabilityOption value) {
    // * Couldn't use RadioListTile As Content Padding Is Not Supported In Flutter V1.22.6
    return ListTile(
      contentPadding: EdgeInsets.zero,
      onTap: () => _availabilityOptionOnChange(value),
      title: Row(
        children: [
          _buildAvailabilityOptionTileRadioButton(value),
          _buildAvailabilityOptionTileTitle(title),
        ],
      ),
    );
  }

  Widget _buildAvailabilityOptionTileRadioButton(AvailabilityOption value) {
    return Radio(
      activeColor: ThemeColors.accentColor,
      value: value,
      groupValue: _selectedAvailabilityOption,
      onChanged: _availabilityOptionOnChange,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  Widget _buildAvailabilityOptionTileTitle(String? title) {
    return Expanded(
      child: _textProvider.buildNormalText3(title),
    );
  }

  Widget _buildDivider() {
    return Divider(
      thickness: _dividerThickness,
      color: ThemeColors.lightBorder,
    );
  }

  Widget _buildAvailableTimes() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _textProvider.buildNormalText2(_setAvailabilityLabel),
        SizedBox(height: _internalMargin),
        _buildWeekDaysList(),
      ],
    );
  }

  Widget _buildWeekDaysList() {
    return Column(
      children: WeekDay.values.map((day) => _buildWeekDay(day)).toList(),
    );
  }

  Widget _buildWeekDay(WeekDay day) {
    return Column(
      children: [
        CheckboxListTile(
          activeColor: ThemeColors.accentColor,
          contentPadding: EdgeInsets.zero,
          controlAffinity: ListTileControlAffinity.leading,
          value: _availableTimes[day]!.isNotEmpty,
          title: _textProvider.buildNormalText3(_weekDaysLabels[day]),
          onChanged: (value) => _weekDaysOnChange(value!, day),
          checkColor: ThemeColors.white,
        ),
        _buildWeekDayTimeSlots(day),
        _buildTimeError(day),
        _buildAddMoreButton(day),
        SizedBox(height: _contentMargin),
      ],
    );
  }

  Widget _buildWeekDayTimeSlots(WeekDay day) {
    return Column(
      children: _availableTimes[day]!
          .asMap()
          .entries
          .map((time) => _buildTimeSlot(day, time.key))
          .toList(),
    );
  }

  Widget _buildTimeSlot(WeekDay day, int index) {
    final fromController = _availableTimes[day]![index].first;
    final toController = _availableTimes[day]![index].last;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildTimeSlotField(fromController, _fromLabel),
        _buildTimeSlotField(toController, _toLabel),
        _buildDeleteTimeSlotField(day, index),
      ],
    );
  }

  Widget _buildTimeSlotField(TextEditingController controller, String? label) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.all(_timeSlotPadding),
        child: ProfileSelectBox(
          label: label,
          controller: controller,
          isSearchable: false,
          options: _dayTimes!.map((e) => e.text).toList(),
        ),
      ),
    );
  }

  Widget _buildDeleteTimeSlotField(WeekDay day, int index) {
    return IconButton(
      padding: EdgeInsets.only(top: _deleteTimeSlotPadding),
      icon: Icon(
        Icons.delete_outline_outlined,
        color: ThemeColors.accentColor,
      ),
      onPressed: () => _deleteTimeSlot(day, index),
    );
  }

  Widget _buildTimeError(WeekDay day) {
    return Visibility(
      visible: _errorTimes[day]!,
      child: _textProvider.buildNormalText3(
        _timeErrorMessage,
        color: ThemeColors.darkRed,
      ),
    );
  }

  Widget _buildAddMoreButton(WeekDay day) {
    if (_availableTimes[day]!.isEmpty) return const SizedBox.shrink();
    return GestureDetector(
      onTap: () => _addTimeSlot(day),
      child: _buildAddMoreButtonContent(),
    );
  }

  Widget _buildAddMoreButtonContent() {
    return Row(
      children: [
        _iconsProvider.add,
        SizedBox(width: _internalMargin),
        _textProvider.buildNormalText3(
          _addMore,
          color: ThemeColors.activeChoiceColor,
        ),
      ],
    );
  }

  Widget _buildSubmitButtons() {
    return InstructorProfileSubmit(
      withSkip: (widget.argument?.fromUpdate ?? false).inverted,
      skipBehaviour: _navigate,
      submitBehaviour: _submitMethod,
    );
  }

  void _availabilityOptionOnChange(AvailabilityOption? value) {
    setState(() {
      _selectedAvailabilityOption = value;
    });
  }

  void _weekDaysOnChange(bool value, WeekDay day) {
    if (value) return _addTimeSlot(day);
    setState(() {
      _availableTimes[day]!
          .map((times) => times.map((controller) => controller.dispose()));
      _availableTimes[day]!.clear();
      _validateTimes(day);
    });
  }

  void _deleteTimeSlot(WeekDay day, int index) {
    _availableTimes[day]![index].map((controller) => controller.dispose());
    _availableTimes[day]!.removeAt(index);
    _validateTimes(day);
  }

  void _addTimeSlot(WeekDay day) {
    setState(() {
      _availableTimes[day]?.add([
        TextEditingController(text: _dayTimes?[_defaultFromId].text),
        TextEditingController(text: _dayTimes?[_defaultToId].text),
      ]);
      _addTimesListeners(day);
    });
    _validateTimes(day);
  }

  void _submitMethod() async {
    final validated = _validateTimeErrors();
    if (!validated) return Snackbars.danger(context, _missingFieldsLabel!);
    ProgressIndicators.loadingDialog(context);
    try {
      final availabilityDTO = _generateAvailabilityDTO();
      final getProfileDTO = _generateGetProfileDTO();
      await _profileProvider.addAvailability(availabilityDTO, getProfileDTO);
      Navigator.pop(context);
      _navigate();
    } catch (error) {
      Navigator.pop(context);
      final errorException = ErrorParser().parseError(error);
      Snackbars.danger(context, errorException.message);
    }
  }

  bool _validateTimeErrors() {
    bool isValid = false;
    for (final day in WeekDay.values) {
      if (_availableTimes[day]!.isNotEmpty) {
        isValid = true;
        break;
      }
    }
    if (!isValid &&
        _selectedAvailabilityOption == AvailabilityOption.BOOK_DIRECTLY) {
      Snackbars.danger(context, _timeErrorMessage!);
      return false;
    }
    for (final day in WeekDay.values) {
      if (_errorTimes[day]!) return false;
    }
    return true;
  }

  Future<void> _navigate() async {
    if (widget.argument?.fromUpdate ?? false) {
      if ((_instructor?.isVerified == true &&
          _instructor?.showUpdateArabicName == true &&
          _instructor?.ignoreArabicNameNotification == false)) {
        Navigator.push(context, MaterialPageRoute(builder: (context) {
          return InstructorUpdateArabNamesScreen(arg: true);
        }));
        return;
      } else if (_instructor?.isUpdateTags == true) {
        Navigator.push(context, MaterialPageRoute(builder: (context) {
          return UpdateInstructorTagsPage(argu: true);
        }));
        return;
      } else {
        Navigator.popAndPushNamed(
          context,
          FinishInstructorNeedMoreInfo.routeName,
        );
      }
      return;
    }
    if (widget.argument?.isMissing ?? false) return Navigator.pop(context);
    Navigator.pushNamed(context, InstructorSignupDescriptionScreen.routeName);
  }

  AddAvailabilityDTO _generateAvailabilityDTO() {
    return AddAvailabilityDTO(
      accessToken: _authProvider.accessToken,
      languageId: _localizationProvider.locals.languageId,
      timeZoneId: _parseTimeZoneId(),
      availabilityMode: _selectedAvailabilityOption,
      times: _parseAvailableTimes(),
    );
  }

  GetProfileDTO _generateGetProfileDTO() {
    return GetProfileDTO(
      accessToken: _authProvider.accessToken,
      languageId: _localizationProvider.locals.languageId,
      currencyId: _localizationProvider.locals.currency?.id,
      timeZoneId: _parseTimeZoneId(),
    );
  }

  int? _parseTimeZoneId() {
    final timezone = _timezones!.firstWhere((zone) {
      return zone.name == _timezoneController!.text;
    });
    return timezone.id;
  }

  Map<WeekDay, List<TimeDTO>> _parseAvailableTimes() {
    final Map<WeekDay, List<TimeDTO>> times = {};
    for (final day in _availableTimes.entries) {
      times[day.key] = day.value.map(
        (time) {
          return TimeDTO(
            fromHourId: _parseTimeId(time.first.text),
            toHourId: _parseTimeId(time.last.text),
          );
        },
      ).toList();
    }
    return times;
  }

  String? _parseTimeId(String value) {
    final time = _dayTimes!.firstWhere((time) => time.text == value);
    return time.value;
  }

  void _initializeData() {
    _textProvider = context.read<DependencyManager>().text;
    _iconsProvider = context.read<DependencyManager>().icons;
    _localizationProvider = context.read<DependencyManager>().localization;

    _authProvider = context.read<AuthenticationProvider>();
    _profileProvider = context.read<InstructorProfileProvider>();
    _instructor = context.read<InstructorProfileProvider>().instructor;

    _title = _localizationProvider.resources.getWithKey(FORMS_AVAILABILITY);
    _firstSectionTitle =
        _localizationProvider.resources.getWithKey(FORMS_AVAILABILITY);
    _firstSectionDescription =
        _localizationProvider.resources.getWithKey(FORMS_AVAILABILITYDESC);
    _secondSectionTitle =
        _localizationProvider.resources.getWithKey(FORMS_AVAILABILITYTIMEZONE);
    _secondSectionDescription = _localizationProvider.resources
        .getWithKey(FORMS_AVAILABILITYTIMEZONEDESC);

    _timezoneFieldLabel = _localizationProvider.resources
        .getWithKey(FORMS_AVAILABILITYCHOOSETIMEZONE);
    _timezoneFieldDescription = _localizationProvider.resources
        .getWithKey(AVAILABILITY_AVAILABILITYMODENOTE);

    _availabilityFirstOptionLabel = _localizationProvider.resources
        .getWithKey(FORMS_AVAILABILITYMODEREQUEST);
    _availabilitySecondOptionLabel =
        _localizationProvider.resources.getWithKey(FORMS_AVAILABILITYMODEBOOK);

    _setAvailabilityLabel =
        _localizationProvider.resources.getWithKey(FORMS_SETAVAILABILITY);

    _fromLabel = _localizationProvider.resources.getWithKey(FORMS_FROM);
    _toLabel = _localizationProvider.resources.getWithKey(FORMS_TO);
    _addMore = _localizationProvider.resources.getWithKey(BTN_ADDMORE);

    _timeErrorMessage = _localizationProvider.resources
        .getWithKey(AVAILABILITY_VALIDATION_TOSHOULDBELESSTHANFROM);
    _missingFieldsLabel =
        _localizationProvider.resources.getWithKey(FORMS_FAILD);

    _timezones =
        _localizationProvider.lookups.getWithKey(TIMEZONE).cast<TimeZone>();
    _timezoneController = TextEditingController();

    _weekDaysLabels = {
      WeekDay.SATURDAY:
          _localizationProvider.resources.getWithKey(CALENDAR_DAYS_SATURDAY),
      WeekDay.SUNDAY:
          _localizationProvider.resources.getWithKey(CALENDAR_DAYS_SUNDAY),
      WeekDay.MONDAY:
          _localizationProvider.resources.getWithKey(CALENDAR_DAYS_MONDAY),
      WeekDay.TUESDAY:
          _localizationProvider.resources.getWithKey(CALENDAR_DAYS_TUESDAY),
      WeekDay.WEDNESDAY:
          _localizationProvider.resources.getWithKey(CALENDAR_DAYS_WEDNESDAY),
      WeekDay.THURSDAY:
          _localizationProvider.resources.getWithKey(CALENDAR_DAYS_THURSDAY),
      WeekDay.FRIDAY:
          _localizationProvider.resources.getWithKey(CALENDAR_DAYS_FRIDAY),
    };

    _availableTimes = {
      for (final day in WeekDay.values) day: <List<TextEditingController>>[]
    };
    _errorTimes = {for (final day in WeekDay.values) day: false};
  }

  void _fillInstructorData() {
    if (_instructor != null) {
      _fillInstructorTimezone();
      _fillAvailabilityMode();
      _fillAvailableTimesInDay(
          _instructor?.saturdayTimes ?? [], WeekDay.SATURDAY);
      _fillAvailableTimesInDay(_instructor?.sundayTimes ?? [], WeekDay.SUNDAY);
      _fillAvailableTimesInDay(_instructor?.mondayTimes ?? [], WeekDay.MONDAY);
      _fillAvailableTimesInDay(
          _instructor?.tuesdayTimes ?? [], WeekDay.TUESDAY);
      _fillAvailableTimesInDay(
          _instructor?.wednesdayTimes ?? [], WeekDay.WEDNESDAY);
      _fillAvailableTimesInDay(
          _instructor?.thursdayTimes ?? [], WeekDay.THURSDAY);
      _fillAvailableTimesInDay(_instructor?.fridayTimes ?? [], WeekDay.FRIDAY);
    }
  }

  void _fillInstructorTimezone() {
    if (_instructor!.timezone != null) {
      TimeZone timezone = _timezones!.singleWhere((zone) {
        return zone.id == _instructor!.timezone;
      });
      _timezoneController!.text = timezone.name!;
      return;
    }
    _fillDefaultTimezone();
  }

  void _fillDefaultTimezone() {
    const SAUDIA_TIMEZONE_ID = 1780;
    TimeZone initialTimezone = _timezones!.singleWhere((zone) {
      return zone.id == SAUDIA_TIMEZONE_ID;
    });

    if (_localizationProvider.locals.countryCodeExists) {
      final code = getIt<Storage>().countryCode;
      final timeZone = Shard().getTimezoneObjectWithIsoCountryCode(code);
      if (timeZone != null) initialTimezone = timeZone;
    }

    _timezoneController!.text = initialTimezone.name!;
  }

  void _fillAvailabilityMode() {
    // * Add Default Value
    _selectedAvailabilityOption = AvailabilityOption.SEND_REQUEST;
    if (_instructor!.availabilityMode == 2) {
      _selectedAvailabilityOption = AvailabilityOption.BOOK_DIRECTLY;
    }
  }

  void _fillAvailableTimesInDay(List<Time> times, WeekDay day) {
    for (final time in times) {
      _availableTimes[day]!.add([
        TextEditingController(text: time.fromTimeText),
        TextEditingController(text: time.toTimeText),
      ]);
      _addTimesListeners(day);
    }
  }

  void _addTimesListeners(WeekDay day) {
    _availableTimes[day]!.last.first.addListener(() => _validateTimes(day));
    _availableTimes[day]!.last.last.addListener(() => _validateTimes(day));
  }

  void _validateTimes(WeekDay day) {
    _errorTimes[day] = false;

    for (final time in _availableTimes[day]!) {
      final from =
          _dayTimes!.firstWhere((dayTime) => dayTime.text == time.first.text);
      final to =
          _dayTimes!.firstWhere((dayTime) => dayTime.text == time.last.text);
      if (int.parse(from.value!) >= int.parse(to.value!)) {
        _errorTimes[day] = true;
        break;
      }
    }

    _availableTimes[day]!.asMap().forEach((key, value) {
      final from =
          _dayTimes!.firstWhere((dayTime) => dayTime.text == value.first.text);
      final to =
          _dayTimes!.firstWhere((dayTime) => dayTime.text == value.last.text);

      _availableTimes[day]!.asMap().forEach((otherKey, otherValue) {
        if (key != otherKey) {
          final otherFrom = _dayTimes!
              .firstWhere((dayTime) => dayTime.text == otherValue.first.text);
          final otherTo = _dayTimes!
              .firstWhere((dayTime) => dayTime.text == otherValue.last.text);
          if ((int.parse(otherFrom.value!) <= int.parse(from.value!) &&
                  int.parse(from.value!) <= int.parse(otherTo.value!)) ||
              (int.parse(otherFrom.value!) <= int.parse(to.value!) &&
                  int.parse(to.value!) <= int.parse(otherTo.value!))) {
            _errorTimes[day] = true;
          }
        }
      });
    });

    setState(() => {});
  }

  void _loadData() async {
    await _profileProvider.getDayTimes();
  }

  void _watchers() {
    _dayTimes = context.watch<InstructorProfileProvider>().dayTimes;
  }
}
