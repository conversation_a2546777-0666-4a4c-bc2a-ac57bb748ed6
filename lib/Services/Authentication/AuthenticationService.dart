import 'package:chopper/chopper.dart';
import 'package:modarby/core/interceptors/connectivity_interceptor.dart';
import 'package:modarby/core/proxy/proxy_service.dart';
import 'package:modarby/core/utilities/logger_service.dart';

import '../../Enums/URLS.dart';

part 'AuthenticationService.chopper.dart';

@ChopperApi(baseUrl: 'api')
abstract class AuthenticationService extends ChopperService {
  static const LOGIN_PATH = 'StudentIntegration/Login';
  static const REGISTER_WHATSAPP_PATH = 'Registration/StepOne';
  static const REGISTER_VERIFY_PATH = 'Registration/VerifyWhatsappCode';
  static const REGISTER_STUDENT_PATH = 'Registration/Student';
  static const REGISTER_INSTRUCTOR_PATH = 'Registration/INSTRUCTOR';
  static const FORGOT_PIN_PATH = 'Registration/ForgotPin';
  static const CHANGE_PIN_PATH = 'Registration/ChangePin';
  static const UPDATE_LOGIN_PATH = 'Registration/UpdateLoginByWhatsapp';
  static const VALIDATE_USER = 'Registration/ValidateUser';
  static const saveStudentDataWizardInfoPath = 'Registration/SaveStudentInfo';
  static const saveInstructorSearch = 'Instructor/UpdateSearchInfo';
  static const getSavedInstructorSearch = 'Instructor/SearchInfo';
  static const updateCountryCodeInstructor = 'Instructor/UpdateCountryCode';
  static const whoIam = 'common/whoami';
  static const getStudentDataWizardInfoPath = 'Registration/GetStudentInfo';
  static const REGISTER_PATH = 'StudentIntegration/Register';
  static const CHANGE_PASSWORD_PATH = 'StudentIntegration/ChangePassword';
  static const LOGOUT_PATH = 'StudentIntegration/Logout';
  static const SEND_CODE_PATH = 'StudentIntegration/GenerateCodeForgotPassword';
  static const VERIFY_CODE_PATH =
      'StudentIntegration/ValidateCodeForgotPassword';
  static const RESET_PASSWORD_PATH = 'StudentIntegration/SetPassword';
  static const DEACTIVATE_ACCOUNT_PATH = 'StudentIntegration/DeactivatAccount';
  static const AUTHORIZATION = 'Authorization';

  static AuthenticationService create() {
    final httpClient = ProxyService.getHttpClient();
    final client = ChopperClient(
      baseUrl: Uri.parse(URLS().BASE_URL),
      services: [
        _$AuthenticationService(),
      ],
      interceptors: [
        HttpLoggingInterceptorCustom(),
        ConnectivityInterceptor(),
      ],
      converter: JsonConverter(),
      client: httpClient != null ? ProxyHttpClient(httpClient) : null,
    );
    return _$AuthenticationService(client);
  }

  @Post(path: LOGIN_PATH, headers: {contentTypeKey: formEncodedHeaders})
  Future<Response> login(@Body() Map<String, dynamic> body);

  @Post(path: VALIDATE_USER)
  Future<Response> validateUser(@Body() Map<String, dynamic> body);

  @Post(path: REGISTER_WHATSAPP_PATH)
  Future<Response> registerWhatsapp(@Body() Map<String, dynamic> body);

  @Post(path: REGISTER_VERIFY_PATH)
  Future<Response> registerVerify(@Body() Map<String, dynamic> body);

  @Post(path: REGISTER_STUDENT_PATH)
  Future<Response> registerStudent(@Body() Map<String, dynamic>? body);

  @Post(path: REGISTER_INSTRUCTOR_PATH)
  Future<Response> registerInstructor(@Body() Map<String, dynamic> body);

  @Post(path: FORGOT_PIN_PATH)
  Future<Response> forgotPin(@Body() Map<String, dynamic> body);

  @Post(path: CHANGE_PIN_PATH)
  Future<Response> changePin(
      @Header(AUTHORIZATION) String token, @Body() Map<String, dynamic> body);

  @Post(path: UPDATE_LOGIN_PATH)
  Future<Response> updateLogin(
      @Header(AUTHORIZATION) String token, @Body() Map<String, dynamic> body);

  @Post(path: updateCountryCodeInstructor)
  Future<Response> setUpdateCountryCodeInstructor(
      @Header(AUTHORIZATION) String token, @Body() Map<String, dynamic> body);

  // ! Not Yet

  @Post(path: REGISTER_PATH)
  Future<Response> register(@Body() Map<String, dynamic> body);

  @Post(path: CHANGE_PASSWORD_PATH)
  Future<Response> changePassword(
      @Header(AUTHORIZATION) String token, @Body() Map<String, dynamic> body);

  @Post(path: LOGOUT_PATH)
  Future<Response> logout(
      @Header(AUTHORIZATION) String token, @Body() Map<String, dynamic> body);

  @Post(path: SEND_CODE_PATH)
  Future<Response> sendResetCode(@Body() Map<String, dynamic> body);

  @Post(path: VERIFY_CODE_PATH)
  Future<Response> verifyResetCode(@Body() Map<String, dynamic> body);

  @Post(path: RESET_PASSWORD_PATH)
  Future<Response> resetPassword(@Body() Map<String, dynamic> body);

  @Get(path: getStudentDataWizardInfoPath)
  Future<Response> getStudentDataWizardInfo(
    @Query('WhatsappNumber') whatsappNumber,
    @Query('WhatsappCountryCodeId') whatsappCountryCodeId,
    @Query('deviceToken') deviceToken,
  );

  @Post(path: saveStudentDataWizardInfoPath)
  Future<Response> saveStudentDataWizardInfo(
    @Header(AUTHORIZATION) String token,
    @Body() Map<String, dynamic> body,
  );
  @Post(path: saveInstructorSearch)
  Future<Response> saveInstructorSearchInfo(
    @Header(AUTHORIZATION) String token,
    @Body() Map<String, dynamic> body,
  );
  @Get(path: getSavedInstructorSearch)
  Future<Response> getSavedInstructorSearchInfo(
    @Header(AUTHORIZATION) String token,
  );
  @Get(path: whoIam)
  Future<Response> getWhoIam(
    @Header(AUTHORIZATION) String token,
  );

  @Post(path: DEACTIVATE_ACCOUNT_PATH)
  Future<Response> deactivateAccount(@Header(AUTHORIZATION) String token);
}
