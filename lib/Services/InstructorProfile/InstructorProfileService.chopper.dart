// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'InstructorProfileService.dart';

// **************************************************************************
// ChopperGenerator
// **************************************************************************

// ignore_for_file: always_put_control_body_on_new_line, always_specify_types, prefer_const_declarations, unnecessary_brace_in_string_interps
class _$InstructorProfileService extends InstructorProfileService {
  _$InstructorProfileService([ChopperClient? client]) {
    if (client == null) return;
    this.client = client;
  }

  @override
  final definitionType = InstructorProfileService;

  @override
  Future<Response<dynamic>> getProfile(
    String token,
    int? languageId,
    int? currencyId,
    int timeZoneId,
  ) {
    final Uri $url = Uri.parse('api/Instructor/Profile');
    final Map<String, dynamic> $params = <String, dynamic>{
      'languageId': languageId,
      'currencyId': currencyId,
      'timeZoneId': timeZoneId,
    };
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      parameters: $params,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> updateAbout(
    String token,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('api/Instructor/Profile');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> updateArabicName(
    String token,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('api/Instructor/UpdateArabicName');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> uploadPhoto(
    String token,
    String image,
  ) {
    final Uri $url = Uri.parse('api/Instructor/MobilePhoto');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final List<PartValue> $parts = <PartValue>[
      PartValueFile<String>(
        'image',
        image,
      )
    ];
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      parts: $parts,
      multipart: true,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> removePhoto(
    String token,
    int? languageId,
  ) {
    final Uri $url = Uri.parse('api/Instructor/RemovePhoto');
    final Map<String, dynamic> $params = <String, dynamic>{
      'languageId': languageId
    };
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      parameters: $params,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> addAdditionalInfo(
    String token,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('api/Instructor/MobilePreferences');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> addSubjects(
    String token,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('api/Instructor/MobileSubjects');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> updateSubjectTags(
    String token,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('api/Instructor/UpdateSubjectTags');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> addResume(
    String token,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('api/Instructor/MobileResume');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> addAvailability(
    String token,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('api/Instructor/MobileAvailability');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> addDescription(
    String token,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('api/Instructor/MobileDescription');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> uploadVideo(
    String token,
    int? languageId,
    String videoUrl,
    String? video,
  ) {
    final Uri $url = Uri.parse('api/Instructor/MobileVideo');
    final Map<String, dynamic> $params = <String, dynamic>{
      'languageId': languageId,
      'videoUrl': videoUrl,
    };
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final List<PartValue> $parts = <PartValue>[
      PartValueFile<String?>(
        'video',
        video,
      )
    ];
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      parts: $parts,
      multipart: true,
      parameters: $params,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getMissing(
    String token,
    int? languageId,
  ) {
    final Uri $url = Uri.parse('api/Instructor/MobileMissing');
    final Map<String, dynamic> $params = <String, dynamic>{
      'languageId': languageId
    };
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      parameters: $params,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> updateAdditionalInfo(
    String token,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('api/Instructor/MobilePreferencesUpdate');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> updatePackages(
    String token,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('api/Instructor/UpdatePackages');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> updateDisplayAnnouncement(
    String token, {
    Map<String, dynamic> body = const <String, dynamic>{},
  }) {
    final Uri $url = Uri.parse('api/Instructor/UpdateDisplayAnnouncements');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> updateCurriculums(
    String token,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('api/Instructor/UpdateCurriculums');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getInstructorCurrencies(int? languageId) {
    final Uri $url = Uri.parse('api/MobileIntegration/GetInstructorCurrencies');
    final Map<String, dynamic> $params = <String, dynamic>{
      'languageId': languageId
    };
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      parameters: $params,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getDayTimes() {
    final Uri $url = Uri.parse('api/MobileIntegration/TimesInDay');
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }
}
