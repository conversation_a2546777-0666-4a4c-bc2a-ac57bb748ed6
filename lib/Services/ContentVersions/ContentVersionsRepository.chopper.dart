// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ContentVersionsRepository.dart';

// **************************************************************************
// ChopperGenerator
// **************************************************************************

// ignore_for_file: always_put_control_body_on_new_line, always_specify_types, prefer_const_declarations, unnecessary_brace_in_string_interps
class _$ContentVersionsRepository extends ContentVersionsRepository {
  _$ContentVersionsRepository([ChopperClient? client]) {
    if (client == null) return;
    this.client = client;
  }

  @override
  final definitionType = ContentVersionsRepository;

  @override
  Future<Response<dynamic>> contentsVersions() {
    final Uri $url = Uri.parse('api/MobileIntegration/GetContentsVersions');
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> resources() {
    final Uri $url = Uri.parse('api/MobileIntegration/GetResources');
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> countryCodes() {
    final Uri $url = Uri.parse('api/MobileIntegration/GetCountryCodes');
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> currencies() {
    final Uri $url = Uri.parse('api/MobileIntegration/GetRootCurrencies');
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> languages() {
    final Uri $url = Uri.parse('api/MobileIntegration/GetLanguagesSpokens');
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> languageLevels() {
    final Uri $url = Uri.parse('api/MobileIntegration/GetLanguageSpokenLevels');
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> lookups() {
    final Uri $url = Uri.parse('api/MobileIntegration/GetLookups');
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> subjects() {
    final Uri $url = Uri.parse('api/MobileIntegration/GetSubjects');
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> subSubjects() {
    final Uri $url = Uri.parse('api/MobileIntegration/GetSubSubjects');
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> topics() {
    final Uri $url = Uri.parse('api/MobileIntegration/GetTopics');
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> subjectsImages() {
    final Uri $url = Uri.parse('api/MobileIntegration/GetSubjectImages');
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> popularSubjects() {
    final Uri $url = Uri.parse('api/MobileIntegration/GetPopularSubSubjects');
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> appVersion(String version) {
    final Uri $url = Uri.parse('api/MobileIntegration/GetStatusByVersion');
    final Map<String, dynamic> $params = <String, dynamic>{'version': version};
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      parameters: $params,
    );
    return client.send<dynamic, dynamic>($request);
  }
}
