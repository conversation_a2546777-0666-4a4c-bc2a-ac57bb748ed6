import 'dart:io';

import 'package:chopper/chopper.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:modarby/core/interceptors/connectivity_interceptor.dart';
import 'package:modarby/core/proxy/proxy_service.dart';
import 'package:modarby/core/utilities/logger_service.dart';

import '../../../Enums/URLS.dart';

part 'MessagesService.chopper.dart';

@ChopperApi(baseUrl: 'api/Messages')
abstract class MessagesService extends ChopperService {
  static const GET_MESSAGES_PATH = 'GetMessages';
  static const GET_CONVERSATION_PATH = 'GetMessagesDetails';
  static const SEND_MESSAGE_PATH = 'AddMessage';
  static const ADD_ATTACHMENT_PATH = 'UploadAttachment';
  static const CHECK_CONTENT_PATH = 'CheckBlocking';
  static const AUTHORIZATION = 'Authorization';
  static const deleteMessage = 'DeleteMessage';
  static const getStudentInfo = 'GetStudentInfo';
  static const userInfo = 'GetUserInfo';

  static MessagesService create() {
    HttpClient? httpClient;

    if (StaticVar.proxy.isNotEmpty) {
      // Retrieve the system proxy settings
      final proxy = StaticVar.proxy;
      print("System proxy: $proxy");

      if (proxy.isNotEmpty) {
        httpClient = HttpClient()
          ..findProxy = (uri) {
            return "PROXY $proxy";
          }
          ..badCertificateCallback =
              (X509Certificate cert, String host, int port) {
            // Allow self-signed certificates for debugging (e.g., Charles)
            return true;
          };
      }
    }
    final client = ChopperClient(
      baseUrl: Uri.parse(URLS().BASE_URL),
      services: [
        _$MessagesService(),
      ],
      interceptors: [
        HttpLoggingInterceptorCustom(),
        ConnectivityInterceptor(),
      ],
      converter: JsonConverter(),
      client: httpClient != null ? ProxyHttpClient(httpClient) : null,
    );
    return _$MessagesService(client);
  }

  @Get(path: GET_MESSAGES_PATH)
  Future<Response> getMessages(
    @Header(AUTHORIZATION) String token,
    @Query() int pageIndex,
  );

  @Get(path: GET_CONVERSATION_PATH)
  Future<Response> getConversation(
    @Header(AUTHORIZATION) String token,
    @Query() String? targetUserId,
    @Query() int? pageIndex,
    @Query() int? pageSize,
  );

  @Post(path: SEND_MESSAGE_PATH)
  Future<Response> sendMessage(
    @Header(AUTHORIZATION) String token,
    @Body() Map<String, dynamic> body,
  );

  @Post(path: ADD_ATTACHMENT_PATH)
  @multipart
  Future<Response> addAttachment(
    @Header(AUTHORIZATION) String token,
    @PartFile() String file,
    @Part() int attchmentTypeId,
  );

  @Post(path: CHECK_CONTENT_PATH)
  Future<Response> checkContent(
    @Header(AUTHORIZATION) String token,
    @Body() Map<String, dynamic> body,
  );
  @Post(path: deleteMessage)
  @multipart
  Future<Response> deleteMessageService(
    @Header(AUTHORIZATION) String token,
    @Body() Map<String, dynamic> body,
  );

  @Get(path: getStudentInfo)
  Future<Response> getStudentInfoMessage(
    @Header(AUTHORIZATION) String token,
    @Query('userId') String userId,
  );
  @Get(path: userInfo)
  Future<Response> getUserInfo(
    @Header(AUTHORIZATION) String token,
    @Query('uId') String userId,
    @Query('userType') int userType,
  );
}
