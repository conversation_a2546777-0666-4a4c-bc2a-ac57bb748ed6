import 'package:modarby/Models/Booking/my_tutors_model.dart';

import '../../Models/Booking/Booking.dart';
import '../../Models/Booking/BookingInfo.dart';
import '../../Models/Booking/LessonsBookingDTO.dart';
import '../../Models/Booking/PackageBookingDTO.dart';
import '../../Models/Booking/RescheduleDTO.dart';
import '../../Models/Exceptions/MessageException.dart';
import 'BookingService.dart';

class BookingRepository {
  static const TOKEN_TYPE = 'Bearer';
  static const SUCCESS = 'success';
  static const MESSAGE = 'Message';

  late BookingService _bookingService;

  BookingRepository() {
    _bookingService = BookingService.create();
  }

  Future<List<int>> getLessonDurations(bool isPackage) async {
    final response = await _bookingService.getDurations(isPackage);
    try {
      final durations = List<int>.from(response.body);
      return durations;
    } catch (error) {
      throw MessageException(response.error as String?, 'getDurations');
    }
  }

  Future<List<int>> getPackageHours() async {
    final response = await _bookingService.getHours();
    try {
      final hours = List<int>.from(response.body);
      return hours;
    } catch (error) {
      throw MessageException(response.error as String?, 'getPackageHours');
    }
  }

  Future<Booking> bookLessons(LessonsBookingDTO dto) async {
    final body = dto.toJson();
    final response =
        await _bookingService.bookLessons(_adjustToken(dto.accessToken), body);
    try {
      final booking = bookingFromJson(response.body);
      return booking;
    } catch (error) {
      throw MessageException(response.error as String?, 'bookLessons');
    }
  }

  Future<bool> bookLessonsThroughPackage(LessonsBookingDTO dto) async {
    final body = dto.toJson();
    final response = await _bookingService.bookLessonsThroughPackage(
        _adjustToken(dto.accessToken), body);
    if (response.isSuccessful && response.body[SUCCESS]) return true;
    if (response.isSuccessful && !response.body[SUCCESS]) {
      throw MessageException(
          response.body[MESSAGE], 'bookLessonsThroughPackage');
    }
    throw MessageException(
        response.error as String?, 'bookLessonsThroughPackage');
  }

  Future<Booking> bookPackage(PackageBookingDTO dto) async {
    final body = dto.toJson();
    final response =
        await _bookingService.bookPackage(_adjustToken(dto.accessToken), body);
    try {
      final booking = bookingFromJson(response.body);
      return booking;
    } catch (error) {
      print(response.error);
      throw MessageException(response.error as String?, 'bookPackage');
    }
  }

  Future<bool> reschedule(RescheduleDTO dto) async {
    final body = dto.toJson();
    final response =
        await _bookingService.reschedule(_adjustToken(dto.accessToken), body);
    if (response.isSuccessful) return true;
    throw MessageException(response.error as String?, 'reschedule');
  }

  Future<List<BookingInfo>> bookingInfo(
      String? accessToken, String? bulkGuid) async {
    final response =
        await _bookingService.bookingInfo(_adjustToken(accessToken), bulkGuid);
    try {
      final bookingInfo = bookingInfoFromJson(response.body);
      return bookingInfo;
    } catch (error) {
      throw MessageException(response.error as String?, 'bookingInfo');
    }
  }

  Future<List<MyTutorsModel>> bookedInstructors({
    String? accessToken,
    required int page,
    required int perPage,
  }) async {
    final response = await _bookingService.bookedInstructors(
      _adjustToken(accessToken),
      page,
      perPage,
    );
    try {
      final result = listMyTutorsModelFromJson(response.body);
      return result;
    } catch (error) {
      throw MessageException(response.error as String?, 'bookedInstructors');
    }
  }

  String _adjustToken(String? accessToken) => '$TOKEN_TYPE $accessToken';
}
