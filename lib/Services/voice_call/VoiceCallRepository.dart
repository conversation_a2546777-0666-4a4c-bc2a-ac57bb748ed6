import 'package:modarby/Models/Common/CallResponse.dart';
import 'package:modarby/Models/calls/CanCallModel.dart';
import 'package:modarby/Services/voice_call/VoiceCallServices.dart';
import 'package:modarby/core/utilities/shard.dart';

import '../../Models/Exceptions/MessageException.dart';

class VoiceCallRepository {
  static const TOKEN_TYPE = 'Bearer';

  late VoiceCallServices _voiceCallService;

  VoiceCallRepository() {
    _voiceCallService = VoiceCallServices.create();
  }

  Future<CallResponse> generateAgoraToken(
      String receiverId, String token) async {
    final response =
        await _voiceCallService.generateAgoraToken(_adjustToken(token), {
      'ReceiverId': receiverId,
      'LanguageId': Shard().languageId,
    });
    try {
      final data = CallResponse.fromJson(response.body);
      return data;
    } catch (error) {
      throw MessageException(response.error as String?, 'generateAgoraToken');
    }
  }

  Future<CanCallModel> canCall({
    required String receiverUserId,
    required String token,
  }) async {
    final response = await _voiceCallService.canCall(_adjustToken(token), {
      'ReceiverUserId': receiverUserId,
    });
    try {
      final data = CanCallModel.fromJson(response.body);
      return data;
    } catch (error) {
      throw MessageException(error as String?, 'canCall');
    }
  }

  Future<void> updateCanCall({
    required String receiverUserId,
    required bool isAllowed,
    required String token,
  }) async {
    try {
      await _voiceCallService.updateCanCall(_adjustToken(token), {
        'ReceiverUserId': receiverUserId,
        'IsAllowed': isAllowed,
      });
    } catch (error) {
      throw MessageException(error as String?, 'canCall');
    }
  }

  Future<void> stopRecordAudio({
    required String channelName,
    required String token,
  }) async {
    try {
      await _voiceCallService.stopCalling(_adjustToken(token), {
        'ChannelName': channelName,
      });
    } catch (error) {
      throw MessageException(error as String?, 'generateAgoraToken');
    }
  }

  Future<void> startRecordAudio({
    required String channelName,
    required String token,
  }) async {
    try {
      await _voiceCallService.startCalling(_adjustToken(token), {
        'ChannelName': channelName,
      });
    } catch (error) {
      throw MessageException(error as String?, 'generateAgoraToken');
    }
  }

  String _adjustToken(String accessToken) => '$TOKEN_TYPE $accessToken';
}
