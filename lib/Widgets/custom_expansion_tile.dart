import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modarby/core/config/themes/colors.dart';

class CustomExpansionTile extends StatelessWidget {
  final Widget title;
  final Widget? leading;
  final Widget? trailing;
  final List<Widget> children;
  final ValueChanged<bool>? onExpansionChanged;
  final bool initiallyExpanded;
  final EdgeInsetsGeometry? childrenPadding;
  final bool? userIsNew;
  CustomExpansionTile({
    required this.title,
    this.leading,
    this.trailing,
    this.children = const <Widget>[],
    this.onExpansionChanged,
    this.initiallyExpanded = false,
    this.userIsNew = false,
    this.childrenPadding,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        (!(userIsNew ?? false))
            ? Container(
                color: ThemeColors.colorF2F2F7,
                child: ListTile(
                  leading: leading,
                  title: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      trailing ??
                          Icon(initiallyExpanded
                              ? Icons.expand_less
                              : Icons.expand_more),
                      SizedBox(
                        width: 5.w,
                      ),
                      Expanded(child: title),
                    ],
                  ),
                  onTap: () {
                    onExpansionChanged?.call(!initiallyExpanded);
                  },
                ),
              )
            : const SizedBox.shrink(),
        if (initiallyExpanded)
          Padding(
            padding: childrenPadding ?? EdgeInsets.zero,
            child: Column(
              children: children,
            ),
          ),
      ],
    );
  }
}
