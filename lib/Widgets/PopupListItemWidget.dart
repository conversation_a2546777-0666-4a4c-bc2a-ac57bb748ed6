// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../Enums/FontWeights.dart';
import '../Providers/DependencyManager/DependencyManager.dart';
import '../Providers/DependencyManager/TextProviders/TextProvider.dart';
import '../UIDTOs/Search/SearchSubject.dart';

class PopupListItemWidget extends StatelessWidget {
  PopupListItemWidget(this.item, {Key? key}) : super(key: key);

  final SearchSubject? item;
  late TextProvider textProvider;

  @override
  Widget build(BuildContext context) {
    textProvider = context.watch<DependencyManager>().text;
    return Container(
      padding: const EdgeInsets.all(12),
      child: textProvider.buildNormalText3(item?.name ?? '',
          weight: FontWeights.semiBold),
    );
  }
}
