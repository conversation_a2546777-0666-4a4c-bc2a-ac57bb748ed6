import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:modarby/Widgets/custom_button_widget.dart';
import 'package:modarby/Widgets/custom_text_widget.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/config/themes/images.dart';
import 'package:modarby/core/helper/show_bottom_sheet/i_show_bottom_sheet.dart';
import 'package:modarby/core/helper/show_bottom_sheet/show_bottom_sheet_input.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';

Future<void> checkNotificationsPermeation({
  required BuildContext context,
  required VoidCallback skip,
  required VoidCallback accept,
}) async {
  getIt<IShowBottomSheetHelper>().showBottomSheet(ShowBottomSheetInput(
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(10.0),
      ),
    ),
    isDismissible: false,
    enableDrag: true,
    isScrollControlled: true,
    Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(20.sp),
            topLeft: Radius.circular(20.sp),
          ),
        ),
        height: MediaQuery.of(context).size.height * 0.55,
        child: NotificationWidget(
          acceptAction: accept,
          skipAction: skip,
        )),
  ));
}

class NotificationWidget extends StatelessWidget {
  const NotificationWidget({
    Key? key,
    required this.acceptAction,
    required this.skipAction,
  }) : super(key: key);
  final VoidCallback acceptAction;
  final VoidCallback skipAction;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(20.sp),
            topLeft: Radius.circular(20.sp),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 16.sp),
            SvgPicture.asset(
              Images.notificationRequestIcon,
              width: 87.sp,
              height: 87.sp,
            ),
            SizedBox(height: 14.sp),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: CustomTextWidget(
                    title: MOBILE_PERMISSIONS_NOTIFICATIONS_TITLE.translate(),
                    size: 28,
                    paddingEnd: 16.w,
                    paddingStart: 16.w,
                    textAlign: TextAlign.center,
                    color: ThemeColors.black,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
            SizedBox(height: 18.sp),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: CustomTextWidget(
                    title: MOBILE_PERMISSIONS_NOTIFICATIONS_TEXT.translate(),
                    size: 15,
                    textAlign: TextAlign.center,
                    paddingEnd: 16.w,
                    paddingStart: 16.w,
                    color: ThemeColors.color636366,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
            const Spacer(),
            _button(context),
            SizedBox(height: 18.sp),
          ],
        ),
      ),
    );
  }

  Widget _button(BuildContext context) {
    return Column(
      children: [
        CustomButtonWidget(
          colorButton: ThemeColors.color26467A,
          sizeTitle: 17,
          onPressed: acceptAction,
          titleColor: ThemeColors.white,
          enabled: true,
          margin: EdgeInsetsDirectional.only(
            start: 16,
            end: 16,
          ),
          padding: EdgeInsetsDirectional.only(
            top: 14.sp,
            bottom: 14.sp,
          ),
          title: MOBILE_PERMISSIONS_NOTIFICATIONS_SUBMIT.translate(),
        ),
        _laterWidget(context),
        SizedBox(
          height: 15,
        )
      ],
    );
  }

  Widget _laterWidget(BuildContext context) {
    return GestureDetector(
      onTap: skipAction,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomTextWidget(
            title: maybeLaterTitle.translate(),
            color: ThemeColors.color5856D6,
            size: 16,
            fontWeight: FontWeight.w400,
            paddingTop: 16.sp,
          ),
        ],
      ),
    );
  }
}
