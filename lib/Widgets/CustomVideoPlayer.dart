import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

import '../Enums/URLS.dart';

class CustomVideoPlayer extends StatefulWidget {
  CustomVideoPlayer({this.videoName, this.videoUrl, this.videoFile});

  final videoUrl;
  final videoName;
  final videoFile;

  final baseUrl = '${URLS().BASE_URL}UploadedFiles/Instructor/Vidoes/';

  @override
  _CustomVideoPlayerState createState() => _CustomVideoPlayerState();
}

class _CustomVideoPlayerState extends State<CustomVideoPlayer> {
  late VideoPlayerController _controller;
  late ChewieController chewieController;
  late bool isIos;

  @override
  void initState() {
    super.initState();
    if (widget.videoFile != null) {
      _controller = VideoPlayerController.file(widget.videoFile)
        ..initialize().then((_) {
          setState(() {});
        });
    } else if (widget.videoUrl == null) {
      _controller = VideoPlayerController.networkUrl(
          Uri.parse(widget.baseUrl + widget.videoName))
        ..initialize().then((_) {
          setState(() {});
        });
    } else {
      _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl))
        ..initialize().then((_) {
          setState(() {});
        });
    }
    chewieController = ChewieController(
      videoPlayerController: _controller,
      autoPlay: false,
      looping: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    isIos = Theme.of(context).platform == TargetPlatform.iOS;
    return isIos
        ? Scaffold(
            body: Center(
              child: _controller.value.isInitialized
                  ? AspectRatio(
                      aspectRatio: _controller.value.aspectRatio,
                      child: VideoPlayer(_controller),
                    )
                  : Container(
                      color: Colors.black,
                      width: double.infinity,
                      child: Center(
                        child: CircularProgressIndicator(
                          backgroundColor: Colors.white,
                        ),
                      ),
                    ),
            ),
            floatingActionButton: FloatingActionButton(
              onPressed: () {
                setState(() {
                  _controller.value.isPlaying
                      ? _controller.pause()
                      : _controller.play();
                });
              },
              child: Icon(
                _controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
              ),
            ),
          )
        : Container(
            color: Colors.black,
            child: _controller.value.isInitialized
                ? Chewie(
                    controller: chewieController,
                  )
                : Container(
                    color: Colors.black,
                    width: double.infinity,
                    child: Center(
                      child: CircularProgressIndicator(
                        backgroundColor: Colors.white,
                      ),
                    ),
                  ),
          );
  }

  @override
  void dispose() {
    _controller.dispose();
    chewieController.dispose();
    super.dispose();
  }
}
