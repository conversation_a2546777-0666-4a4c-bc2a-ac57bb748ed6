import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import 'package:modarby/Models/InstructorProfile/Instructor.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:provider/provider.dart';

import '../Enums/FontWeights.dart';
import '../Providers/DependencyManager/DependencyManager.dart';
import '../Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import '../Providers/DependencyManager/TextProviders/TextProvider.dart';

class BookingPackagePreviewCard extends StatefulWidget {
  final int? index;
  final String hours;
  final int? type;
  final Instructor? instructor;

  BookingPackagePreviewCard({
    required this.index,
    required this.hours,
    required this.type,
    required this.instructor,
  });

  @override
  _BookingPackagePreviewCardState createState() =>
      _BookingPackagePreviewCardState();
}

class _BookingPackagePreviewCardState extends State<BookingPackagePreviewCard> {
  late TextProvider _textProvider;

  late LocalizationProvider _localizationProvider;

  String? _idLabel;
  String? _discountLabel;
  String? _durationLabel;
  String? _hoursLabel;
  String? _typeLabel;

  final _margin = 15.0;
  final _borderRadius = 8.0;

  @override
  void initState() {
    _initializeData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: _margin),
      decoration: _buildDecoration(),
      child: _buildBody(),
    );
  }

  BoxDecoration _buildDecoration() {
    return BoxDecoration(
      border: Border.all(color: ThemeColors.defaultBorderColor),
      borderRadius: BorderRadius.circular(_borderRadius),
    );
  }

  Widget _buildBody() {
    return Column(
      children: _buildContent(),
    );
  }

  List<Widget> _buildContent() {
    return ListTile.divideTiles(
      context: context,
      tiles: [
        _buildInfoTile(_idLabel, '${widget.index}'),
        _buildInfoTile(_discountLabel, '${_calculateDiscount()?.truncate()}%'),
        _buildInfoTile(_durationLabel, '${_calculateDuration()} $_hoursLabel'),
        _buildInfoTile(_typeLabel, '${_parseType()}'),
      ],
    ).toList();
  }

  Widget _buildInfoTile(String? key, String value) {
    return ListTile(
      title: _textProvider.buildNormalText3(key),
      trailing: _textProvider.buildNormalText3(
        value,
        weight: FontWeights.medium,
      ),
    );
  }

  double? _calculateDiscount() {
    final hours = int.tryParse(widget.hours) ?? 0;
    final package = widget.instructor?.packages?.firstWhereOrNull(
        (package) => package.from! <= hours && hours <= package.to!);
    if (package == null) return 0;
    return package.discount;
  }

  int _calculateDuration() {
    final hours = int.tryParse(widget.hours) ?? 0;
    return hours;
  }

  String? _parseType() {
    final type = widget.instructor?.teachingModes
        ?.firstWhereOrNull((mode) => mode.id == widget.type);
    if (type == null) return '';
    return type.name;
  }

  void _initializeData() {
    _textProvider = context.read<DependencyManager>().text;
    _localizationProvider = context.read<DependencyManager>().localization;

    _idLabel = _localizationProvider.resources.getWithKey(PACKAGES_PACKAGE_ID);
    _discountLabel =
        _localizationProvider.resources.getWithKey(PACKAGES_DISCOUNT);
    _durationLabel =
        _localizationProvider.resources.getWithKey(PACKAGE_DURATION);
    _hoursLabel = _localizationProvider.resources.getWithKey(BOOKING_HOURS);
    _typeLabel = _localizationProvider.resources.getWithKey(PACKAGE_TYPE);
  }
}
