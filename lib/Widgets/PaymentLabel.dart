// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:modarby/Enums/FontWeights.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/DependencyManager/TextProviders/TextProvider.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:provider/provider.dart';

class PaymentLabel extends StatelessWidget {
  late TextProvider textProvider;
  late LocalizationProvider localizationProvider;
  String? optionsPerCountryLabel;

  @override
  Widget build(BuildContext context) {
    initializeProviders(context);
    loadScreenLabels();

    const summaryPadding = 15.0;
    return Padding(
        padding: EdgeInsets.all(summaryPadding),
        child: textProvider.buildNormalText4(optionsPerCountryLabel,
            color: ThemeColors.sublineColor, weight: FontWeights.regular));
  }

  void initializeProviders(BuildContext context) {
    textProvider = context.watch<DependencyManager>().text;
    localizationProvider = context.watch<DependencyManager>().localization;
  }

  void loadScreenLabels() {
    optionsPerCountryLabel =
        localizationProvider.resources.getWithKey(BOOKING_PAYMENTCOUNTRYHINT);
  }
}
