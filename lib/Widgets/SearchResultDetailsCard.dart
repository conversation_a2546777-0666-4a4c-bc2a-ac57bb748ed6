// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:modarby/Models/InstructorProfile/Instructor.dart';
import 'package:modarby/Widgets/custom_text_widget.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/config/themes/images.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:provider/provider.dart';

import '../Enums/FontWeights.dart';
import '../Providers/CommonProvider.dart';
import '../Providers/DependencyManager/DependencyManager.dart';
import '../Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import '../Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import '../Providers/DependencyManager/TextProviders/TextProvider.dart';
import '../Widgets/TextRow.dart';
import '../core/config/static_var.dart';
import '../core/utilities/language/LanguagesKeys.dart';
import 'search_result_details_sheet.dart';

class SearchCardDetails extends StatelessWidget {
  final String? tutoringYears;
  final String? modarbOverview;
  final String? modarbLocation;
  final List modarbSpokenLanguages;
  final List modarbSubjects;
  final List<List<InstructorTags>> modarbTags;
  final int? studentsCount;
  final int? lessonsCount;
  final int? maxTextLines;
  final _internalMargin = 10.0;
  final _elevation = 0.0;
  SearchCardDetails({
    Key? key,
    required this.tutoringYears,
    required this.modarbOverview,
    required this.modarbLocation,
    required this.modarbSpokenLanguages,
    required this.modarbSubjects,
    required this.modarbTags,
    required this.studentsCount,
    required this.lessonsCount,
    this.maxTextLines,
  }) : super(key: key);

  late TextProvider textProvider;
  late IconsProvider iconsProvider;
  late LocalizationProvider localizationProvider;

  String? certifiedLabel;
  String? tutoringYearsLabel;
  String? locationLabel;
  String? languagesLabel;
  String? subjectsLabel;
  String? tagsLabel;
  String? studentsLabel;
  String? lessonsLabel;

  String subjectsBody = '';
  String tagsBody = '';

  final padding = 10.0;
  final iconSize = 12.0;
  final sectionsPadding = 10.0;
  final tileMargin = 8.0;
  bool get isTagsEnabled => StaticVar.context.read<CommonProvider>().applyTags;
  int get subjectsLength => modarbSubjects.length;
  int get tagsLength => modarbTags.isNotEmpty
      ? modarbTags
          .expand((element) => element.map((e) => e.name).toSet().toList())
          .length
      : 0;

  @override
  Widget build(BuildContext context) {
    initializeProviders(context);
    loadScreenLabels();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (tutoringYears != null &&
            (tutoringYears ?? '').isNotEmpty &&
            tutoringYears != '0')
          TextRow(
            textProvider: textProvider,
            title: certifiedLabel,
            body: '${_handleTutoringYears()} $tutoringYearsLabel',
            icon: _buildIcon(Images.bookingCertificateIcon),
          ),
        if (tutoringYears != null &&
            (tutoringYears ?? '').isNotEmpty &&
            tutoringYears != '0')
          SizedBox(height: sectionsPadding),
        TextRow(
          textProvider: textProvider,
          title: locationLabel,
          body: modarbLocation,
          icon: _buildIcon(Images.bookingLocationIcon),
        ),
        SizedBox(height: sectionsPadding),
        LanguageRow(
          title: languagesLabel,
          body: modarbSpokenLanguages.map((result) => result).toList(),
          icon: _buildIcon(Images.bookingSpeakIcon),
        ),
        SizedBox(height: sectionsPadding),
        _buildTextRowSubjects(),
        SizedBox(height: sectionsPadding),
        if (!modarbTags.isNullOrEmpty && isTagsEnabled) _buildTextRowTags()
      ],
    );
  }

  Widget _buildTextRowSubjects() {
    return (subjectsLength > 3)
        ? InkWell(
            onTap: () => _showDetails(
              subjectsLabel ?? '',
              subjectsLength,
              modarbSubjects.map((result) => result.name).toList(),
            ),
            child: TextRow(
              textProvider: textProvider,
              title: subjectsLabel,
              body: subjectsBody,
              moreThan3: true,
              length: '${subjectsLength - 3}+',
              icon: _buildIcon(Images.bookingSubjectIcon),
            ),
          )
        : TextRow(
            textProvider: textProvider,
            title: subjectsLabel,
            body: subjectsBody,
            icon: _buildIcon(Images.bookingSubjectIcon),
          );
  }

  Widget _buildTextRowTags() {
    return (tagsLength > 3)
        ? InkWell(
            onTap: () => _showDetails(
              tagsLabel ?? '',
              tagsLength,
              modarbTags
                  .expand((element) =>
                      element.map((e) => e.name ?? '').toSet().toList())
                  .toSet()
                  .toList(),
            ),
            child: TextRow(
              textProvider: textProvider,
              title: tagsLabel,
              body: tagsBody,
              moreThan3: true,
              length: '${tagsLength - 3}+',
              icon: _buildIcon(Images.bookingTagIcon),
            ),
          )
        : TextRow(
            textProvider: textProvider,
            title: tagsLabel,
            body: tagsBody,
            icon: _buildIcon(Images.bookingTagIcon),
          );
  }

  _showDetails(String title, int length, List<dynamic>? details) {
    showModalBottomSheet(
      constraints: BoxConstraints(
        maxHeight: (tagsLength > 9)
            ? MediaQuery.of(StaticVar.context).size.height * 0.5
            : MediaQuery.of(StaticVar.context).size.height * 0.33,
      ),
      context: StaticVar.context,
      backgroundColor: ThemeColors.primaryColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(10),
        ),
      ),
      builder: (_) => SearchResultDetailsBottomSheet(
        parentWidget: CustomTextWidget(
          title: '$title: $length',
          color: ThemeColors.black,
          fontWeight: FontWeight.bold,
          size: 20,
        ),
        childWidget: _buildSubSubjectTagsChip(details),
      ),
    );
  }

  Widget _buildSubSubjectTagsChip(List<dynamic>? details) {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          children: [
            Wrap(
              alignment: WrapAlignment.start,
              spacing: _internalMargin,
              runSpacing: -6,
              children: (details ?? []).map((detail) {
                return _buildDetailsChip(detail);
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsChip(String name) {
    return FilterChip(
      padding: EdgeInsets.zero,
      labelPadding: const EdgeInsets.symmetric(horizontal: 5),
      pressElevation: _elevation,
      backgroundColor: ThemeColors.colorF2F2F7,
      selectedColor: ThemeColors.colorF2F2F7,
      shape: _buildChipShape(),
      label: _buildSubjectName(name),
      showCheckmark: false,
      selected: false,
      onSelected: (_) {},
    );
  }

  Widget _buildSubjectName(String? name) {
    return CustomTextWidget(
      title: name,
      fontWeight: FontWeights.regular,
      color: ThemeColors.color1C1C1E,
      size: 12,
    );
  }

  StadiumBorder _buildChipShape() {
    return StadiumBorder(
      side: BorderSide(
        color: ThemeColors.colorE5E5EA,
      ),
    );
  }

  String? _handleTutoringYears() {
    final language = localizationProvider.locals.language;
    if (language == LanguagesKeys.ENGLISH) return tutoringYears;
    return tutoringYears?.split('-').reversed.join('-');
  }

  Widget _buildIcon(String icon) {
    return SvgPicture.asset(
      icon,
      width: 11.w,
      height: 11.w,
    );
  }

  void loadScreenLabels() {
    certifiedLabel = localizationProvider.resources
        .getWithKey(INSTRUCTOR_CERTIFIEDTEACHERLABEL);
    tutoringYearsLabel = localizationProvider.resources
        .getWithKey(INSTRUCTOR_TOTALYEARSOFEXPERIENCE);
    locationLabel =
        localizationProvider.resources.getWithKey(BOOKING_INSTRUCTOR_LOCATION);
    languagesLabel =
        localizationProvider.resources.getWithKey(BOOKING_INSTRUCTOR_SPEAK);
    subjectsLabel =
        localizationProvider.resources.getWithKey(PUBLICPROFILE_TEACH);
    tagsLabel = localizationProvider.resources.getWithKey(SEARCH_TAGS);
    studentsLabel =
        localizationProvider.resources.getWithKey(INSTRUCTOR_TOTAL_STUDENTS);
    lessonsLabel =
        localizationProvider.resources.getWithKey(INSTRUCTOR_TOTAL_LESSONS);

    subjectsBody =
        modarbSubjects.map((result) => result.name).toList().take(3).join(',');

    tagsBody = modarbTags.isNotEmpty
        ? modarbTags
            .expand((element) => element.map((e) => e.name).toSet().toList())
            .take(3)
            .join(',')
            .trim()
        : '';
  }

  void initializeProviders(BuildContext context) {
    textProvider = context.watch<DependencyManager>().text;
    iconsProvider = context.watch<DependencyManager>().icons;

    localizationProvider = context.watch<DependencyManager>().localization;
  }
}

class MiniSearchCardDetails extends StatefulWidget {
  final String? overview;
  final String? tutoringYears;
  final List? modarbSpokenLanguages;
  final String? description;

  MiniSearchCardDetails({
    required this.overview,
    required this.tutoringYears,
    required this.modarbSpokenLanguages,
    required this.description,
  });

  @override
  _MiniSearchCardDetailsState createState() => _MiniSearchCardDetailsState();
}

class _MiniSearchCardDetailsState extends State<MiniSearchCardDetails> {
  late TextProvider _textProvider;
  late IconsProvider _iconsProvider;

  late LocalizationProvider _localizationProvider;

  int? _currentMaxLines;

  String? _certifiedLabel;
  String? _tutoringYearsLabel;
  String? _languagesLabel;

  String? _showMoreLabel;
  String? _showLessLabel;

  final _padding = 10.0;
  final _margin = 6.0;
  final _iconSize = 12.0;
  final _maxLines = 2;

  @override
  void initState() {
    _initializeData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildOverview(),
        if (widget.tutoringYears != null &&
            (widget.tutoringYears ?? '').isNotEmpty &&
            widget.tutoringYears != '0')
          TextRow(
            textProvider: _textProvider,
            title: _certifiedLabel,
            body: '${_handleTutoringYears()} $_tutoringYearsLabel',
            icon: _buildIcon(_iconsProvider.award),
          ),
        if (widget.tutoringYears != null &&
            (widget.tutoringYears ?? '').isNotEmpty &&
            widget.tutoringYears != '0')
          SizedBox(height: _margin),
        LanguageRow(
          title: _languagesLabel,
          body: widget.modarbSpokenLanguages,
          icon: _buildIcon(_iconsProvider.speak),
        ),
        SizedBox(height: _margin),
        _buildDescription(),
        _buildReadMoreOrLess(),
      ],
    );
  }

  Widget _buildOverview() {
    return Padding(
      padding: EdgeInsets.all(_padding),
      child: _textProvider.buildNormalText3(
        '${widget.overview}',
        color: ThemeColors.titleColor,
        weight: FontWeights.semiBold,
        maxLines: _maxLines,
      ),
    );
  }

  String? _handleTutoringYears() {
    final language = _localizationProvider.locals.language;
    if (language == LanguagesKeys.ENGLISH) return widget.tutoringYears;
    return widget.tutoringYears?.split('-').reversed.join('-');
  }

  Widget _buildDescription() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _padding),
      child: _textProvider.buildNormalText4(
        '${widget.description}',
        color: ThemeColors.titleColor,
        weight: FontWeights.medium,
        maxLines: _currentMaxLines,
        overflow: _currentMaxLines == _maxLines ? TextOverflow.ellipsis : null,
      ),
    );
  }

  Widget _buildReadMoreOrLess() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _padding),
      child: InkWell(
        onTap: _switchReadMoreOrLess,
        child: _textProvider.buildNormalText4(
          _getReadMoreOrLessLabel(),
          weight: FontWeights.medium,
          color: ThemeColors.activeChoiceColor,
        ),
      ),
    );
  }

  Widget _buildIcon(Widget icon) {
    return _iconsProvider.parametrizedIcon(icon, width: _iconSize);
  }

  void _initializeData() {
    _textProvider = context.read<DependencyManager>().text;
    _iconsProvider = context.read<DependencyManager>().icons;
    _localizationProvider = context.read<DependencyManager>().localization;

    _currentMaxLines = _maxLines;

    _certifiedLabel = _localizationProvider.resources
        .getWithKey(INSTRUCTOR_CERTIFIEDTEACHERLABEL);
    _tutoringYearsLabel = _localizationProvider.resources
        .getWithKey(INSTRUCTOR_TOTALYEARSOFEXPERIENCE);
    _languagesLabel =
        _localizationProvider.resources.getWithKey(BOOKING_INSTRUCTOR_SPEAK);

    _showMoreLabel =
        _localizationProvider.resources.getWithKey(MOBILE_SHOWMORE);
    _showLessLabel =
        _localizationProvider.resources.getWithKey(MOBILE_SHOWLESS);
  }

  String? _getReadMoreOrLessLabel() {
    if (_currentMaxLines == _maxLines) return _showMoreLabel;
    return _showLessLabel;
  }

  void _switchReadMoreOrLess() {
    if (_currentMaxLines == _maxLines) {
      return setState(() => _currentMaxLines = null);
    }
    return setState(() => _currentMaxLines = _maxLines);
  }
}

class TextRow extends StatelessWidget {
  const TextRow({
    Key? key,
    required this.title,
    required this.body,
    required this.icon,
    this.moreThan3,
    this.length,
    required this.textProvider,
  }) : super(key: key);

  final Widget icon;
  final bool? moreThan3;
  final String? title, body, length;
  final TextProvider textProvider;

  @override
  Widget build(BuildContext context) {
    const sectionsMargin = 8.0;
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(width: 16.w),
        icon,
        SizedBox(width: 5.w),
        CustomTextWidget(
          title: title,
          color: ThemeColors.color1C1C1E,
          fontWeight: FontWeight.w400,
          size: 12,
        ),
        const SizedBox(
          width: sectionsMargin,
        ),
        (moreThan3 ?? false)
            ? Expanded(
                child: Padding(
                  padding: EdgeInsetsDirectional.only(end: 16.w),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Flexible(
                        child: textProvider.buildText(
                          body ?? '',
                          14,
                          FontWeight.w500,
                          ThemeColors.color1C1C1E,
                          align: TextAlign.start,
                          spEnabled: true,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(width: 5.w),
                      Container(
                        padding: const EdgeInsets.all(3),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            color: ThemeColors.color8E8E93,
                            borderRadius: BorderRadius.circular(3)),
                        child: Center(
                          child: CustomTextWidget(
                            textAlign: TextAlign.center,
                            title: length,
                            color: ThemeColors.white,
                            fontWeight: FontWeight.w400,
                            size: 12,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              )
            : Expanded(
                child: Padding(
                  padding: EdgeInsetsDirectional.only(end: 16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      textProvider.buildText(
                        body ?? '',
                        14,
                        FontWeight.w500,
                        ThemeColors.color1C1C1E,
                        align: TextAlign.start,
                        spEnabled: true,
                        overflow: TextOverflow.visible,
                      ),
                    ],
                  ),
                ),
              ),
      ],
    );
  }
}
