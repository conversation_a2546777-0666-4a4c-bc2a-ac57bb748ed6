// ignore_for_file: must_be_immutable

import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:modarby/Widgets/custom_text_widget.dart';
import 'package:modarby/Widgets/image_loading.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/utilities/error_parser.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';
import 'package:provider/provider.dart';

import '../Models/Reviews/DeleteReviewDTO.dart';
import '../Models/Reviews/Review.dart';
import '../Providers/AuthenticationProvider.dart';
import '../Providers/DependencyManager/DependencyManager.dart';
import '../Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import '../Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import '../Providers/DependencyManager/TextProviders/TextProvider.dart';
import '../Providers/ReviewsProvider.dart';
import '../Widgets/ConfirmationDialog.dart';
import '../Widgets/ReviewDialog.dart';
import '../core/utilities/ProgressIndicators.dart';
import '../core/utilities/Snackbars.dart';

class ReviewTile extends StatelessWidget {
  final Review? review;
  final bool isReviews;

  ReviewTile({required this.review, required this.isReviews, Key? key})
      : super(key: key);

  late TextProvider _textProvider;
  late IconsProvider _iconsProvider;
  late LocalizationProvider _localizationProvider;

  late AuthenticationProvider _authProvider;
  late ReviewsProvider _reviewsProvider;

  late bool _isCurrentUser;

  String? _modifyLabel;
  String? _deleteLabel;
  String? _deleteDialogTitle;
  String? _deleteDialogDescription;
  String? _deleteDialogSubmit;
  final _itemCount = 5;

  @override
  Widget build(BuildContext context) {
    _initializeData(context);
    return Container(
      decoration: isReviews
          ? null
          : BoxDecoration(
              border: Border.all(color: ThemeColors.grayE5E5EA),
              borderRadius: BorderRadius.circular(12)),
      padding: EdgeInsetsDirectional.only(
        start: 12.w,
        end: 12.w,
        top: isReviews ? 14.sp : 12.sp,
        bottom: isReviews ? 8.sp : 12.sp,
      ),
      margin: isReviews
          ? null
          : EdgeInsetsDirectional.only(
              top: 16.sp,
            ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAvatar(),
          SizedBox(width: 5.w),
          Flexible(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildName(),
                    _buildRating(),
                  ],
                ),
                SizedBox(height: 10.sp),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Flexible(child: _buildReview()),
                  ],
                ),
              ],
            ),
          ),
          if (!_isCurrentUser) SizedBox(width: 5.w),
          _buildOptions(context),
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return ImageLoading(
      colorBackgroundErrorWidget: _getAvatarColor(),
      imageUrl: review?.studentPicture ?? '',
      errorWidget: _buildAvatarPlaceholder(),
      placeHolderWidget: _buildAvatarPlaceholder(),
      borderRadius: 5,
      width: 24.w,
      height: 24.w,
    );
  }

  Widget? _buildAvatarPlaceholder() {
    final placeholder = _getAvatarPlaceholder();
    return CustomTextWidget(
      title: placeholder,
      size: 10,
      fontWeight: FontWeight.w400,
      color: ThemeColors.primaryColor,
    );
  }

  Widget _buildName() {
    return Flexible(
      child: CustomTextWidget(
        title: review?.studentName ?? '',
        fontWeight: FontWeight.bold,
        size: 14,
        color: ThemeColors.color1C1C1E,
        textOverflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildRating() {
    return Row(
      children: [
        _buildRatingDate(),
        SizedBox(width: 4.w),
        _buildRatingBar(),
        SizedBox(width: 4.w),
        _buildRateNum()
      ],
    );
  }

  Widget _buildRateNum() {
    return CustomTextWidget(
      title: (review?.rating ?? 0).toDouble().toString(),
      fontWeight: FontWeight.bold,
      size: 12,
      color: ThemeColors.color171725,
    );
  }

  Widget _buildRatingBar() {
    return Icon(
      Icons.star,
      color: ThemeColors.colorFF9500,
      size: 10,
    );
  }

  bool get isAr => _localizationProvider.locals.language == 'ar';

  Widget _buildRatingDate() {
    return CustomTextWidget(
      title:
          DateFormat('dd/MM/yyyy').format(review?.dateTime ?? DateTime.now()),
      color: ThemeColors.color171725,
      size: 10,
      fontWeight: FontWeight.w400,
    );
  }

  Widget _buildReview() {
    // if (review?.reviewText == null || (review?.reviewText ?? '').isEmpty) {
    //   return const SizedBox.shrink();
    // }
    return CustomTextWidget(
      title: (review?.reviewText == null || (review?.reviewText ?? '').isEmpty)
          ? NO_TEXT_REVIEWS.translate()
          : '${review?.reviewText}',
      color: ThemeColors.color1C1C1E,
      style: (review?.reviewText == null || (review?.reviewText ?? '').isEmpty)
          ? FontStyle.italic
          : null,
      fontWeight: FontWeight.w400,
      height: 1.3,
      size: 12,
      maxLine: isReviews ? 5 : 3,
      textOverflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildOptions(BuildContext context) {
    if (!_isCurrentUser) return const SizedBox.shrink();
    return PopupMenuButton(
      child: const Icon(Icons.more_horiz),
      itemBuilder: (context) => [
        PopupMenuItem(
          child: _textProvider.buildNormalText3(_modifyLabel),
          value: _modifyLabel,
        ),
        PopupMenuItem(
          child: _textProvider.buildNormalText3(_deleteLabel),
          value: _deleteLabel,
        ),
      ],
      onSelected: (dynamic value) async {
        if (value == _modifyLabel) return await _showReviewsDialog(context);
        if (value == _deleteLabel) return await _delete(context);
      },
    );
  }

  void _initializeData(BuildContext context) {
    _textProvider = context.watch<DependencyManager>().text;
    _iconsProvider = context.watch<DependencyManager>().icons;

    _localizationProvider = context.watch<DependencyManager>().localization;

    _authProvider = context.watch<AuthenticationProvider>();
    _reviewsProvider = context.watch<ReviewsProvider>();

    _isCurrentUser = _authProvider.userId == review?.studentGuid;

    _modifyLabel = _localizationProvider.resources.getWithKey(REVIEWS_MODIFY);
    _deleteLabel = _localizationProvider.resources.getWithKey(REVIEWS_DELETE);
    _deleteDialogTitle =
        _localizationProvider.resources.getWithKey(REVIEWS_DELETE_DIALOG_TITLE);
    _deleteDialogDescription = _localizationProvider.resources
        .getWithKey(REVIEWS_DELETE_DIALOG_DESCRIPTION);
    _deleteDialogSubmit =
        _localizationProvider.resources.getWithKey(COMMON_YES);
  }

  Color? _getAvatarColor() {
    return Colors.primaries[Random().nextInt(Colors.primaries.length)];
  }

  String _getAvatarPlaceholder() {
    final fullName =
        review?.studentName?.split(' ').where((name) => name.isNotEmpty);
    final placeholder = fullName?.map((name) => name.characters.first);
    return (placeholder ?? []).join().toUpperCase();
  }

  Future<void> _showReviewsDialog(BuildContext context) async {
    return await showDialog(
      context: context,
      builder: (_) => ReviewDialog(),
    );
  }

  Future<void> _delete(BuildContext context) async {
    final isConfirmed = await _confirmDelete(context);
    if (!isConfirmed) return;
    ProgressIndicators.loadingDialog(context);
    try {
      final dto = _generateDeleteDTO();
      await _reviewsProvider.deleteReview(dto);
      Navigator.pop(context);
    } catch (error) {
      Navigator.pop(context);
      final errorException = ErrorParser().parseError(error);
      Snackbars.danger(context, errorException.message);
    }
  }

  Future<bool> _confirmDelete(BuildContext context) async {
    final isConfirmed = await showDialog(
      context: context,
      builder: (_) => ConfirmationDialog(
        submitColor: ThemeColors.color26467A,
        colorCancel: ThemeColors.white,
        colorCancelLabel: ThemeColors.black,
        colorBorderCancel: ThemeColors.grayE5E5EA,
        icon: _iconsProvider.warning,
        title: _deleteDialogTitle,
        subtitle: _deleteDialogDescription,
        submitLabel: _deleteDialogSubmit,
        hasCancel: true,
      ),
    );
    if (isConfirmed != null && isConfirmed) return true;
    return false;
  }

  DeleteReviewDTO _generateDeleteDTO() {
    return DeleteReviewDTO(
      accessToken: _authProvider.accessToken,
      guid: review?.instructorGuid,
      id: review?.id,
    );
  }
}
