import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:provider/provider.dart';

import '../Enums/FontWeights.dart';
import '../Providers/DependencyManager/DependencyManager.dart';
import '../Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import '../Providers/DependencyManager/TextProviders/TextProvider.dart';

class ProfileFormField extends StatefulWidget {
  final String? label, hint;
  final TextEditingController? controller;
  final int? minLines;
  final int? maxLines;
  final int? maxLength;
  final bool? secure;
  final Function? validator;
  final Function? onChanged;
  final List<TextInputFormatter>? formatters;
  final TextInputAction action;
  final TextInputType keyboard;
  final double contentPadding;
  final double? height;
  final EdgeInsets? padding;
  final bool isEnabled;
  final bool isFilled;
  final bool isReadOnly;

  ProfileFormField(
      {this.label,
      this.height,
      this.hint,
      this.padding,
      required this.controller,
      this.minLines,
      this.maxLines,
      this.maxLength,
      this.secure,
      this.validator,
      this.onChanged,
      this.formatters,
      this.action = TextInputAction.done,
      this.keyboard = TextInputType.text,
      this.contentPadding = 20.0,
      this.isEnabled = true,
      this.isFilled = false,
      this.isReadOnly = false});

  @override
  _ProfileFormFieldState createState() => _ProfileFormFieldState();
}

class _ProfileFormFieldState extends State<ProfileFormField> {
  late TextProvider textProvider;
  late IconsProvider iconsProvider;

  final titleBottomMargin = 10.0;
  final fieldFontSize = 14.0;
  final errorFontSize = 12.0;
  final fieldBorderRadius = 5.0;
  final fieldBorderWidth = 1.0;

  bool isSecured = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    textProvider = context.watch<DependencyManager>().text;
    iconsProvider = context.watch<DependencyManager>().icons;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null)
          textProvider.buildNormalText3(widget.label,
              weight: FontWeights.medium),
        if (widget.label != null) SizedBox(height: titleBottomMargin),
        buildField(context),
      ],
    );
  }

  Widget buildField(BuildContext context) {
    return widget.height != null
        ? SizedBox(
            height: widget.height,
            child: TextFormField(
              style: textProvider.buildStyle(fieldFontSize, FontWeights.regular,
                  ThemeColors.black1A1818, null, null),
              controller: widget.controller,
              validator: widget.validator as String? Function(String?)?,
              onChanged: widget.onChanged as String? Function(String?)?,
              obscureText: widget.secure != null ? isSecured : false,
              inputFormatters: widget.formatters,
              enabled: widget.isEnabled,
              keyboardType: widget.keyboard,
              textInputAction: widget.action,
              maxLines:
                  widget.secure != null && isSecured ? 1 : widget.maxLines,
              maxLength: widget.maxLength,
              readOnly: widget.isReadOnly,
              maxLengthEnforcement: MaxLengthEnforcement.enforced,
              decoration: InputDecoration(
                filled: widget.isFilled,
                fillColor: ThemeColors.secondaryColor,
                contentPadding:
                    widget.padding ?? EdgeInsets.all(widget.contentPadding),
                hintText: widget.hint,
                hintStyle: textProvider.buildStyle(fieldFontSize,
                    FontWeights.regular, ThemeColors.bodyTextColor, null, null),
                suffixIcon:
                    widget.secure != null ? buildSecureButton() : SizedBox(),
                enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(fieldBorderRadius),
                    borderSide: BorderSide(
                        color: ThemeColors.defaultBorderColor,
                        width: fieldBorderWidth)),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(fieldBorderRadius),
                  borderSide: BorderSide(
                      color: ThemeColors.accentColor, width: fieldBorderWidth),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(fieldBorderRadius),
                  borderSide: BorderSide(
                      color: ThemeColors.accentColor, width: fieldBorderWidth),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(fieldBorderRadius),
                  borderSide: BorderSide(
                      color: ThemeColors.accentColor, width: fieldBorderWidth),
                ),
                errorStyle: textProvider.buildStyle(errorFontSize,
                    FontWeights.regular, ThemeColors.accentColor, null, null),
              ),
            ),
          )
        : TextFormField(
            style: textProvider.buildStyle(fieldFontSize, FontWeights.regular,
                ThemeColors.black1A1818, null, null),
            controller: widget.controller,
            validator: widget.validator as String? Function(String?)?,
            obscureText: widget.secure != null ? isSecured : false,
            inputFormatters: widget.formatters,
            enabled: widget.isEnabled,
            keyboardType: widget.keyboard,
            textInputAction: widget.action,
            maxLines: widget.secure != null && isSecured ? 1 : widget.maxLines,
            maxLength: widget.maxLength,
            readOnly: widget.isReadOnly,
            maxLengthEnforcement: MaxLengthEnforcement.enforced,
            onChanged: widget.onChanged as String? Function(String?)?,
            decoration: InputDecoration(
              filled: widget.isFilled,
              fillColor: ThemeColors.secondaryColor,
              contentPadding:
                  widget.padding ?? EdgeInsets.all(widget.contentPadding),
              hintText: widget.hint,
              hintStyle: textProvider.buildStyle(fieldFontSize,
                  FontWeights.regular, ThemeColors.bodyTextColor, null, null),
              suffixIcon:
                  widget.secure != null ? buildSecureButton() : SizedBox(),
              enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(fieldBorderRadius),
                  borderSide: BorderSide(
                      color: ThemeColors.defaultBorderColor,
                      width: fieldBorderWidth)),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(fieldBorderRadius),
                borderSide: BorderSide(
                    color: ThemeColors.accentColor, width: fieldBorderWidth),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(fieldBorderRadius),
                borderSide: BorderSide(
                    color: ThemeColors.accentColor, width: fieldBorderWidth),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(fieldBorderRadius),
                borderSide: BorderSide(
                    color: ThemeColors.accentColor, width: fieldBorderWidth),
              ),
              errorStyle: textProvider.buildStyle(errorFontSize,
                  FontWeights.regular, ThemeColors.accentColor, null, null),
            ),
          );
  }

  Widget buildSecureButton() {
    return IconButton(
      icon:
          isSecured ? iconsProvider.securedField : iconsProvider.unsecuredField,
      onPressed: () {
        setState(() {
          isSecured = !isSecured;
        });
      },
    );
  }
}
