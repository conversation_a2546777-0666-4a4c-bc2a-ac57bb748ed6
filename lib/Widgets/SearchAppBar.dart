// ignore_for_file: must_be_immutable

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:modarby/Widgets/custom_text_widget.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/config/themes/images.dart';
import 'package:modarby/core/utilities/shard.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';

import '../Enums/FontWeights.dart';
import '../Enums/URLS.dart';
import '../Providers/AuthenticationProvider.dart';
import '../Providers/DependencyManager/DependencyManager.dart';
import '../Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import '../Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import '../Providers/DependencyManager/TextProviders/TextProvider.dart';
import '../Providers/SearchProvider.dart';
import '../SharedScreens/NotificationsHistoryStudentScreen.dart';

class SearchAppBar extends StatelessWidget implements PreferredSizeWidget {
  SearchAppBar({
    super.key,
    this.editSearchWidget,
    this.onClickSubject,
    this.onClickBack,
    this.numberUnreadCount = 0,
  });

  Widget? editSearchWidget;
  late IconsProvider _iconsProvider;
  late LocalizationProvider _localizationProvider;

  late SearchProvider _searchProvider;
  late TextProvider _textProvider;

  final VoidCallback? onClickSubject;
  final VoidCallback? onClickBack;
  final int numberUnreadCount;

  final _height = 60.0;
  final _margin = 8.0;
  final appBarPadding = 15.0;
  bool get isAr => _localizationProvider.locals.language == 'ar';

  @override
  Widget build(BuildContext context) {
    _initializeData(context);
    final isLoggedIn = context.watch<AuthenticationProvider>().isLoggedIn;

    return Column(
      children: [
        SizedBox(
          height: 10.sp,
        ),
        AppBar(
          toolbarHeight: 47.sp,
          elevation: 0.0,
          centerTitle: true,
          backgroundColor: Colors.white,
          leading: Builder(
            builder: (context) => Container(
              child: (IconButton(
                padding: EdgeInsets.symmetric(horizontal: appBarPadding / 2),
                icon: _iconsProvider.circleMenu,
                onPressed: () {
                  Scaffold.of(context).openDrawer();
                },
              )),
            ),
          ),
          title: _buildMajor(context),
          actions: [
            _buildShareButton(),
            if (isLoggedIn == true) _notificationLogs(),
            SizedBox(width: _margin * 2.5),
          ],
        ),
      ],
    );
  }

  Widget _notificationLogs() {
    return Builder(builder: (context) {
      return GestureDetector(
        onTap: () => Navigator.pushNamed(
            context, NotificationsHistoryStudentScreen.routeName),
        child: Align(
          alignment: Alignment.center,
          child: Badge(
              // alignment: Alignment.bottomCenter,
              backgroundColor: ThemeColors.accentColor,
              offset: isAr ? Offset(7, -8) : Offset(7, -8),
              label: _textProvider.buildNormalText4('$numberUnreadCount',
                  weight: FontWeights.semiBold, color: ThemeColors.white),
              isLabelVisible: (numberUnreadCount != 0 && numberUnreadCount > 0),
              child: SvgPicture.asset('assets/images/notification.svg',
                  fit: BoxFit.scaleDown)),
        ),
      );
    });
  }

  String get majorName =>
      Shard().getMajorName(_searchProvider.currentSearchQuery?.major?.id) ??
      (_searchProvider.currentSearchQuery?.major?.name ?? '');

  Widget _buildMajor(BuildContext context) {
    return InkWell(
      onTap: onClickSubject,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
            child: CustomTextWidget(
              title: majorName,
              fontWeight: FontWeight.w600,
              size: 16,
              color: ThemeColors.white,
            ),
          ),
          SizedBox(width: _margin),
          Icon(
            Icons.keyboard_arrow_down_rounded,
            color: ThemeColors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildShareButton() {
    return IconButton(
      onPressed: _share,
      icon: SvgPicture.asset(
        Images.bookingShareIcon,
        width: 24.w,
        height: 24.w,
      ),
    );
  }

  void _initializeData(BuildContext context) {
    _iconsProvider = context.watch<DependencyManager>().icons;
    _localizationProvider = context.watch<DependencyManager>().localization;
    _textProvider = context.read<DependencyManager>().text;

    _searchProvider = context.watch<SearchProvider>();
  }

  void _share() {
    final query = _searchProvider.currentSearchQuery?.toJson();
    query?['page'] = 0;
    final params = query?.map((key, value) => MapEntry(key, value.toString()));
    final language = _localizationProvider.locals.language;
    final authority = Uri.parse(URLS().BASE_URL).authority;
    final uri = Uri.https(authority, '$language/list', params);

    // Check if running on iOS platform and if it's an iPad
    if (Platform.isIOS && _isIpad()) {
      // Get the reference to the current widget's render box
      final RenderBox? box = StaticVar.context.findRenderObject() as RenderBox?;

      // For iPad specifically, provide a non-zero Rect
      Share.share(uri.toString(),
          sharePositionOrigin: box != null
              ? Rect.fromLTRB(box.size.width / 2, box.size.height / 2,
                  box.size.width, box.size.height)
              : Rect.fromLTRB(0, 0, 100, 100) // Fallback for iPad
          );
    } else {
      // For iPhone and other platforms, use the original implementation
      Share.share(uri.toString());
    }
  }

// Helper method to detect if the device is an iPad
  bool _isIpad() {
    // The most reliable way to detect iPad in Flutter
    final data = MediaQueryData.fromView(WidgetsBinding.instance.window);
    return Platform.isIOS &&
        data.size.shortestSide >= 600; // iPads have 768+ points in portrait
  }

  @override
  Size get preferredSize => Size.fromHeight(_height);
}
