import 'package:flutter/material.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class YoutubePopUp extends StatefulWidget {
  final String? url;

  const YoutubePopUp({required this.url, Key? key}) : super(key: key);

  @override
  _YoutubePopUpState createState() => _YoutubePopUpState();
}

class _YoutubePopUpState extends State<YoutubePopUp> {
  YoutubePlayerController? _videoController;

  final _contentMargin = 20.0;
  final _internalPadding = 5.0;
  final _iconSize = 20.0;

  @override
  void initState() {
    _initializeData();
    super.initState();
  }

  @override
  void dispose() {
    _videoController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientaion) {
        switch (orientaion) {
          case Orientation.portrait:
            return _buildBody();
          case Orientation.landscape:
            return _buildVideo();
          default:
            return _buildBody();
        }
      },
    );
  }

  Widget _buildBody() {
    return GestureDetector(
      onTap: () => Navigator.pop(context),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Container(
          width: MediaQuery.of(context).size.width,
          child: _buildContent(),
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        SizedBox(height: _contentMargin),
        _buildClose(),
        const Expanded(child: SizedBox()),
        _buildVideo(),
        const Expanded(child: SizedBox()),
      ],
    );
  }

  Widget _buildClose() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _internalPadding),
      child: CircleAvatar(
        backgroundColor: Colors.black,
        child: _buildCloseContent(),
      ),
    );
  }

  Widget _buildCloseContent() {
    return IconButton(
      iconSize: _iconSize,
      color: Colors.white,
      icon: const Icon(Icons.close),
      onPressed: () => Navigator.pop(context),
    );
  }

  Widget _buildVideo() {
    return YoutubePlayer(
      controller: _videoController!,
      aspectRatio: 16 / 9,
      showVideoProgressIndicator: true,
      progressColors: const ProgressBarColors(
        playedColor: ThemeColors.accentColor,
        bufferedColor: ThemeColors.accentColor,
      ),
    );
  }

  void _initializeData() {
    final videoId = YoutubePlayer.convertUrlToId(widget.url!)!;
    _videoController = YoutubePlayerController(
      initialVideoId: videoId,
      flags:
          const YoutubePlayerFlags(autoPlay: true, mute: false, forceHD: true),
    );
  }
}
