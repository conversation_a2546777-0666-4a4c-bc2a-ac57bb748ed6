// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:modarby/Widgets/custom_text_widget.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:provider/provider.dart';

import '../../Enums/FontWeights.dart';
import '../../Models/live_tutors/tutor_settings/get_distrub_values.dart';
import '../../Providers/AuthenticationProvider.dart';
import '../../Providers/emergency_settings_provider.dart';
import '../../core/Utilities/Snackbars.dart';
import '../../core/config/themes/images.dart';
import '../../core/utilities/error_parser.dart';

class GenericListingSheetTimes extends StatefulWidget {
  GenericListingSheetTimes(
      {Key? key,
      required this.title,
      this.allowMultipleSelection = false,
      this.heightFactor,
      this.selectHour = 0,
      this.showTopDivider = true,
      this.showSearchBar = false,
      required this.icon,
      required this.subTitle,
      required this.languageId})
      : super(key: key);

  final String title, subTitle;
  final bool allowMultipleSelection;
  final double? heightFactor;
  final bool showTopDivider;
  final bool showSearchBar;
  final Widget icon;
  int selectHour;
  int languageId;
  //final TextEditingController msg;

  @override
  _GenericListingSheetTimesState createState() =>
      _GenericListingSheetTimesState();
}

class _GenericListingSheetTimesState extends State<GenericListingSheetTimes> {
  late EmergencySettingsProvider _emergencyProvider;
  late AuthenticationProvider authenticationProvider;
  List<DistrubValues>? _distrubValues;

  @override
  void initState() {
    _initializeData();
    _loadData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    _initializeWatchers();
    return FractionallySizedBox(
      heightFactor: widget.heightFactor,
      child: _sheetBody(),
    );
  }

  Widget _sheetBody() {
    return StatefulBuilder(builder: (context, build) {
      return Container(
        height: MediaQuery.of(context).size.height * .41 /*6*/,
        margin: EdgeInsets.only(
            top: MediaQuery.of(context).viewPadding.top,
            bottom: MediaQuery.of(context).viewInsets.bottom),
        color: ThemeColors.white,
        child: Column(
          children: <Widget>[
            _header(),
            const Divider(
              height: 1,
              thickness: 1,
              color: ThemeColors.grayE5E5EA,
            ),
            //if (widget.showSearchBar) _searchBar(),
            _itemsList(),
            //_submitButton(),
          ],
        ),
      );
    });
  }

  Widget _header() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 12.sp,
      ),
      decoration: const BoxDecoration(
        color: ThemeColors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomTextWidget(
                title: widget.title,
                color: ThemeColors.color1C1C1E,
                size: 16.sp,
                fontWeight: FontWeight.bold,
              ),
              GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: SvgPicture.asset(Images.closeIconSheet)),
            ],
          ),
          // CustomTextWidget(
          //   title: widget.subTitle,
          //   color: ThemeColors.color1C1C1E,
          //   size: 14.sp,
          //   fontWeight: FontWeight.w500,
          // ),
        ],
      ),
    );
  }

  Widget _itemsList() {
    return Expanded(child: buildExpansionInfo());
  }

  Widget buildSheetInfoTile(String? key, Function() onTap, bool visible) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 40,
        padding: const EdgeInsets.all(10),
        width: double.maxFinite,
        decoration: BoxDecoration(
            color: visible
                ? ThemeColors.color26467A.withOpacity(.05)
                : ThemeColors.white,
            border: Border.all(
                width: .5,
                color: visible
                    ? ThemeColors.color26467A
                    : ThemeColors.colorD1D1D6)),
        child: Row(
          children: [
            CustomTextWidget(
              title: key,
              size: 12.sp,
              fontWeight: visible ? FontWeight.bold : FontWeights.medium,
              color: ThemeColors.color3A3A3C,
            ),
            Spacer(),
            if (visible) SvgPicture.asset(Images.check),
          ],
        ),
      ),
    );
  }

  buildExpansionInfo() {
    if (_distrubValues == null)
      return const Center(child: CircularProgressIndicator());
    return Column(
      children: _distrubValues!
          .map((e) => buildSheetInfoTile(
              e.label ?? '',
              () => _updateDistrub(context, e.value ?? 1),
              widget.selectHour == e.value))
          .toList(),
    );
  }

  Future<void> _updateDistrub(BuildContext context, int select) async {
    try {
      await _emergencyProvider.updateDistrubValues(
          authenticationProvider.accessToken, select);
      setState(() {
        widget.selectHour = select;
      });
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (error) {
      final errorException = ErrorParser().parseError(error);
      Snackbars.danger(context, errorException.message);
    }
  }

  void _initializeData() {
    _emergencyProvider = context.read<EmergencySettingsProvider>();
    authenticationProvider = context.read<AuthenticationProvider>();
  }

  Future<void> _loadData() async {
    await _emergencyProvider.getEmergencySettings();

    DateTime dateTime = DateTime.parse(
            '${_emergencyProvider.myEmergency?.emergencyHelpDontDistrubToDate ?? DateTime.now().toUtc()}${_emergencyProvider.myEmergency?.emergencyHelpDontDistrubToDate != null ? 'Z' : ''}')
        .toUtc();
    Duration difference = dateTime.difference(DateTime.now().toUtc());
    final hours = difference.inHours;
    final minutes = difference.inMinutes;
    setState(() {
      widget.selectHour = hours;
    });
    if (minutes > 0 && hours == 0) {
      setState(() {
        widget.selectHour = 1;
      });
    }
    await _emergencyProvider.getDistrubValues(
      authenticationProvider.accessToken,
      widget.languageId,
    );
  }

  void _initializeWatchers() async {
    _distrubValues = context.watch<EmergencySettingsProvider>().distrubValues;
  }
}
