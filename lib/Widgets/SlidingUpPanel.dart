import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modarby/Models/InstructorProfile/Instructor.dart';
import 'package:modarby/Models/Messages/MessageUserInfoModel.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/SearchProvider.dart';
import 'package:modarby/Providers/StudentDashboardProvider.dart';
import 'package:modarby/Providers/StudentProfileProvider.dart';
import 'package:modarby/Widgets/custom_text_widget.dart';
import 'package:modarby/core/Utilities/ProgressIndicators.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/navigation_service.dart';
import 'package:modarby/core/utilities/routing_user.dart';
import 'package:modarby/core/utilities/storage.dart';
import 'package:modarby/features/available_options/available_options_arguments.dart';
import 'package:modarby/features/available_options/available_options_page.dart';
import 'package:modarby/features/booking_details/view/BookDetails.dart';
import 'package:modarby/features/booking_page/ModarbBooking.dart';
import 'package:modarby/features/messages/domain/enum/conversation_place.dart';
import 'package:modarby/features/messages/presentation/arguments/conversation_argument.dart';
import 'package:modarby/features/messages/presentation/views/conversation_screen.dart';
import 'package:modarby/features/selected_fee/arguments/selected_fee_arguments.dart';
import 'package:provider/provider.dart';

import '../Enums/Roles.dart';
import '../Models/Messages/ConversationModel.dart';
import '../Providers/AuthenticationProvider.dart';
import '../Providers/BookingProvider.dart';
import '../Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import '../Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import '../Providers/DependencyManager/TextProviders/TextProvider.dart';
import '../Providers/MessagesProvider.dart';
import '../Widgets/ConfirmationDialog.dart';

class SlidingUpPanelBody extends StatefulWidget {
  final Instructor? instructor;

  SlidingUpPanelBody({this.instructor});

  @override
  _SlidingUpPanelBodyState createState() => _SlidingUpPanelBodyState();
}

class _SlidingUpPanelBodyState extends State<SlidingUpPanelBody> {
  late TextProvider textProvider;
  late IconsProvider iconsProvider;

  late LocalizationProvider localizationProvider;

  late AuthenticationProvider authenticationProvider;
  SearchProvider? instructorProvider;
  BookingProvider? bookingProvider;

  String? requestButtonLabel;
  String? instantBookingLabel;
  String? cancelRequestButtonLabel;
  String? sendMessageLabel;
  String? notStudentDialogTitle;
  String? notStudentDialogButton;
  final panelBodyHeight = 100.0;
  final screenPadding = 16.sp;
  final bookButtonHeight = 52.sp;
  final sectionsHeightMargin = 15.0;
  final iconFlex = 1;
  final textFlex = 20;

  bool get isAr =>
      StaticVar.context
          .read<DependencyManager>()
          .localization
          .locals
          .language ==
      'ar';

  @override
  Widget build(BuildContext context) {
    initializeProviders(context);
    loadScreenLabels();
    return _buildContent(context);
  }

  Widget _buildContent(BuildContext context) {
    return Row(
      children: [
        Flexible(
          flex: 3,
          child: buildBookNowButton(context),
        ),
        SizedBox(width: 8.w),
        Flexible(
          flex: 3,
          child: buildSendMessageButton(context),
        ),
      ],
    );
  }

  void initializeProviders(BuildContext context) {
    textProvider = context.watch<DependencyManager>().text;
    iconsProvider = context.watch<DependencyManager>().icons;

    instructorProvider = context.watch<SearchProvider>();
    localizationProvider = context.watch<DependencyManager>().localization;
    authenticationProvider = context.watch<AuthenticationProvider>();
    bookingProvider = context.watch<BookingProvider>();
  }

  TextButton buildBookNowButton(BuildContext context) {
    return TextButton(
      style: TextButton.styleFrom(padding: EdgeInsets.zero),
      onPressed:
          _isBookingDisabled ? () {} : () => _handleBookingClick(context),
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsetsDirectional.only(
          start: 14.5.w,
          end: 14.5.w,
          top: 14.sp,
          bottom: 14.sp,
        ),
        decoration: BoxDecoration(
          color: ThemeColors.color26467A,
          borderRadius: BorderRadius.circular(screenPadding),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            iconsProvider.parametrizedIcon(
              iconsProvider.bookmarkSolid,
              color: ThemeColors.primaryColor,
              width: 24.w,
              height: 24.w,
            ),
            SizedBox(width: 5.w),
            CustomTextWidget(
              title: _getBookButtonLabel(),
              fontWeight: FontWeight.w700,
              size: 12,
              textAlign: TextAlign.center,
              color: ThemeColors.white,
            ),
          ],
        ),
      ),
    );
  }

  String? _getBookButtonLabel() {
    if (widget.instructor?.availabilityMode == 2) return instantBookingLabel;
    return requestButtonLabel;
  }

  void _handleMessageClick(BuildContext context) {
    setState(() {
      _isButtonDisabled = true; // Disable the button
    });

    // Perform the action initiated by the button click
    _sendMessage(context);

    // Enable the button after a short delay (you can adjust the delay as needed)
    Future.delayed(const Duration(milliseconds: 500), () {
      setState(() {
        _isButtonDisabled = false;
      });
    });
  }

  bool _isButtonDisabled = false;

  void _handleBookingClick(BuildContext context) {
    setState(() {
      _isBookingDisabled = true; // Disable the button
    });

    // Perform the action initiated by the button click
    _showBookingTypes(context);

    // Enable the button after a short delay (you can adjust the delay as needed)
    Future.delayed(const Duration(milliseconds: 500), () {
      setState(() {
        _isBookingDisabled = false;
      });
    });
  }

  bool _isBookingDisabled = false;

  TextButton buildSendMessageButton(BuildContext context) {
    return TextButton(
      style: TextButton.styleFrom(padding: EdgeInsets.zero),
      onPressed: _isButtonDisabled ? () {} : () => _handleMessageClick(context),
      child: Container(
        height: bookButtonHeight,
        decoration: BoxDecoration(
          color: ThemeColors.colorF2F2F7,
          borderRadius: BorderRadius.circular(screenPadding),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            iconsProvider.parametrizedIcon(
              iconsProvider.messageMatsapp,
              fit: BoxFit.contain,
              width: 24.w,
              height: 24.w,
              color: ThemeColors.color26467A,
            ),
            SizedBox(width: 5.w),
            CustomTextWidget(
              title: sendMessageLabel,
              fontWeight: FontWeight.w700,
              color: ThemeColors.black1A1818,
              size: 12,
            ),
          ],
        ),
      ),
    );
  }

  _showBookingTypes(BuildContext context) async {
    if (getIt<Storage>().role == Roles.INSTRUCTOR ||
        getIt<Storage>().role == Roles.REFERRAL) {
      return _showDialog(context);
    }
    final routing = RoutingUser().checkUserComeFromOtherPlace(
      routingScreen: StudentModarbBookingScreen.routeName,
      context: context,
    );
    if (routing.inverted) {
      return;
    }

    ProgressIndicators.loadingDialog(context);
    final dashboardProvider = context.read<StudentDashboardProvider>();
    await dashboardProvider.getPackages(
        context.read<AuthenticationProvider>().accessToken, 1);
    final packages = context.read<StudentDashboardProvider>().packages ?? [];
    Navigator.of(context).pop();

    for (final package in packages) {
      if (package.instructorGuid == widget.instructor?.userId &&
          (package.canBookLesson ?? false)) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => BookDetailsScreen(
              isBookingFromPackage: true,
              packageRemainingTime: package.remaining,
              packageGuid: package.guid,
            ),
          ),
        );
        return;
      }
    }
    if (StaticVar.displaySingleBooking.inverted) {
      return NavigationService.instance.navigateTo(
        AvailableOptionsPage.routeName,
        args: AvailableOptionsArguments(
            selectedFeesArguments: SelectedPackageArguments(
          instructor: widget.instructor,
          selectedIndexPackage: -1,
        )),
      );
    }
    final isNew =
        (context.read<StudentProfileProvider>().student?.isNew ?? true);
    final hasPakcages = (widget.instructor?.hasPackages ?? false);
    if ((!isNew) && hasPakcages) {
      NavigationService.instance.navigateTo(
        AvailableOptionsPage.routeName,
        args: AvailableOptionsArguments(
            selectedFeesArguments: SelectedPackageArguments(
          instructor: widget.instructor,
          selectedIndexPackage: -1,
        )),
      );
    } else {
      NavigationService.instance.navigateTo(
        BookDetailsScreen.routeName,
      );
    }
  }

  _sendMessage(BuildContext context) async {
    if (getIt<Storage>().role == Roles.INSTRUCTOR ||
        getIt<Storage>().role == Roles.REFERRAL) {
      return _showDialog(context);
    }
    final routing = RoutingUser().checkUserComeFromOtherPlace(
      routingScreen: StudentModarbBookingScreen.routeName,
      context: context,
    );
    if (routing.inverted) {
      return;
    }
    final data = await StaticVar.context.read<MessagesProvider>().getUserInfo(
          widget.instructor?.userId ?? '',
          showLoading: true,
        );
    final conversation = _buildConversation(data);
    context.read<MessagesProvider>().conversation = conversation;
    context.read<MessagesProvider>().haveMessageInstructorReply = false;
    if (conversation.userId != null) {
      final argument = ConversationArgument(
        conversationType: ConversationType.anyPlace,
        userId: conversation.userId ?? '',
        userName: conversation.userName ?? '',
        userNameAr: conversation.userNameAr ??
            '${widget.instructor?.firstNameAr ?? ''} ${widget.instructor?.lastNameAr ?? ''}',
      );
      NavigationService.instance
          .navigateToIfNotCurrent(ConversationScreen.routeName, args: argument);
    }
  }

  bool get isStudent =>
      getIt<Storage>().role == Roles.STUDENT ||
      getIt<Storage>().role == Roles.PARENT;
  // GetUserInfo
  Conversation _buildConversation(MessageUserInfoModel? data) {
    final photo = '${data?.picture}';
    return Conversation(
      usersid: widget.instructor?.userId,
      userslist: data?.name,
      // userslistAr: data?.namear,
      userspic: photo,
      usersflag: data?.flag ?? 'SA',
      counts: null,
      usersemail: null,
      usersprofile: null,
      lastMessage: null,
      lastMessageDate: null,
    );
  }

  Future _showDialog(BuildContext context) {
    return showDialog(
      context: context,
      builder: (_) => ConfirmationDialog(
        hasEx: false,
        title: notStudentDialogTitle,
        subtitle: '',
        submitLabel: notStudentDialogButton,
        icon: iconsProvider.infoSolid,
      ),
    );
  }

  void loadScreenLabels() {
    instantBookingLabel =
        localizationProvider.resources.getWithKey(BOOKING_BOOKTRIALLESSON);
    requestButtonLabel =
        localizationProvider.resources.getWithKey(BOOKING_REQUESTLESSON);
    cancelRequestButtonLabel =
        localizationProvider.resources.getWithKey(BOOKING_CANCELREQUEST);
    sendMessageLabel =
        localizationProvider.resources.getWithKey(COMMON_SENDMESSAGE);
    notStudentDialogTitle = localizationProvider.resources
        .getWithKey(ACTIONS_NEEDSTUDENTACCOUNTFORTHISACTION_TITLE);
    notStudentDialogButton =
        localizationProvider.resources.getWithKey(BUTTONS_CANCEL);
  }
}
