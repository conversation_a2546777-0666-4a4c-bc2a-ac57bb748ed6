import 'package:flutter/material.dart';
import 'package:modarby/Enums/FontWeights.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/TextProviders/TextProvider.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:provider/provider.dart';

class SendRequestButton extends StatelessWidget {
  const SendRequestButton({
    Key? key,
    this.label,
    this.onPressed,
  }) : super(key: key);
  final String? label;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    TextProvider textProvider;

    textProvider = context.watch<DependencyManager>().text;
    const buttonRadius = 10.0;
    const buttonHeight = 50.0;
    const textFlex = 20;
    const iconFlex = 1;
    return TextButton(
      onPressed: onPressed,
      child: Container(
        padding: const EdgeInsets.all(buttonRadius),
        margin: const EdgeInsets.only(bottom: buttonRadius / 2),
        height: buttonHeight,
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          color: ThemeColors.accentColor,
          borderRadius: BorderRadius.circular(buttonRadius),
        ),
        child: Row(
          children: [
            const Expanded(
              flex: iconFlex,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Icon(
                  Icons.bookmark_border,
                  color: ThemeColors.primaryColor,
                ),
              ),
            ),
            Expanded(
              flex: textFlex,
              child: Align(
                  alignment: Alignment.center,
                  child: textProvider.buildNormalText2(label,
                      weight: FontWeights.semiBold,
                      color: ThemeColors.primaryColor)),
            ),
          ],
        ),
      ),
    );
  }
}
