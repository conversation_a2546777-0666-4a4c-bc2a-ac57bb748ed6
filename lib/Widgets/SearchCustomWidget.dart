// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modarby/Enums/FontWeights.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/DependencyManager/TextProviders/TextProvider.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:provider/provider.dart';

import '../Providers/DependencyManager/DependencyManager.dart';
import '../Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import '../UIDTOs/Search/SearchSubject.dart';

typedef QueryListItemBuilder<T> = Widget Function(T item);
typedef OnItemSelected<T> = void Function(T item);
typedef SelectedItemBuilder<T> = Widget Function(
  T item,
  VoidCallback deleteSelectedItem,
);
typedef QueryBuilder<T> = List<T> Function(
  String query,
  List<T>? list,
);
typedef TextFieldBuilder = Widget Function(
  TextEditingController controller,
  FocusNode? focus,
);

class SearchCustomWidget<T> extends StatefulWidget {
  SearchCustomWidget(
      {required this.dataList,
      required this.popupListItemBuilder,
      required this.selectedItemBuilder,
      required this.queryBuilder,
      Key? key,
      this.onItemSelected,
      this.hideSearchBoxWhenItemSelected = false,
      this.listContainerHeight,
      this.noItemsFoundWidget,
      this.textFieldBuilder,
      this.hintLabel})
      : super(key: key);
  String? hintLabel;
  final List<T>? dataList;
  final QueryListItemBuilder<T?> popupListItemBuilder;
  final SelectedItemBuilder<T?> selectedItemBuilder;
  final bool hideSearchBoxWhenItemSelected;
  final double? listContainerHeight;
  final QueryBuilder<T?> queryBuilder;
  final TextFieldBuilder? textFieldBuilder;
  final Widget? noItemsFoundWidget;
  final OnItemSelected<SearchSubject?>? onItemSelected;

  @override
  MySingleChoiceSearchState<SearchSubject> createState() =>
      MySingleChoiceSearchState<SearchSubject>();
}

class MySingleChoiceSearchState<SearchSubject>
    extends State<SearchCustomWidget<SearchSubject?>> {
  final _controller = TextEditingController();
  late List<SearchSubject> _list;
  List<SearchSubject?>? _tempList;
  bool? isFocused;
  FocusNode? _focusNode;
  late ValueNotifier<SearchSubject?> notifier;
  bool? isRequired;
  Widget? textField;
  OverlayEntry? overlayEntry;
  bool showTextBox = false;
  double? listContainerHeight;
  final LayerLink _layerLink = LayerLink();
  final double textBoxHeight = 48;
  final TextEditingController textController = TextEditingController();

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() {
    textController.text = widget.hintLabel ?? '';
    _controller.text = widget.hintLabel ?? '';
    _tempList = <SearchSubject>[];
    notifier = ValueNotifier(null);
    _focusNode = FocusNode();
    isFocused = false;
    _list = List<SearchSubject>.from(widget.dataList!);
    _tempList?.addAll(_list);
    _focusNode?.addListener(() {
      if (!(_focusNode?.hasFocus ?? false)) {
        _controller.clear();
        if (overlayEntry != null) {
          overlayEntry?.remove();
        }
        overlayEntry = null;
      } else {
        _tempList
          ?..clear()
          ..addAll(_list);
        if (overlayEntry == null) {
          onTap();
        } else {
          overlayEntry?.markNeedsBuild();
        }
      }
    });
    _controller.addListener(() {
      final text = _controller.text;
      if (text.trim().isNotEmpty) {
        _tempList?.clear();
        final List<SearchSubject?> filterList =
            widget.queryBuilder(text, widget.dataList);

        _tempList?.addAll(filterList);
        if (overlayEntry == null) {
          onTap();
        } else {
          overlayEntry?.markNeedsBuild();
        }
      } else {
        _tempList
          ?..clear()
          ..addAll(_list);
        if (overlayEntry == null) {
          onTap();
        } else {
          overlayEntry?.markNeedsBuild();
        }
      }
    });
    //
    KeyboardVisibilityController().onChange.listen((bool visible) {
      if (!visible) {
        _focusNode?.unfocus();
      }
    });
  }

  @override
  void didUpdateWidget(SearchCustomWidget oldWidget) {
    if (oldWidget.dataList != widget.dataList) {
      init();
    }
    super.didUpdateWidget(oldWidget as SearchCustomWidget<SearchSubject?>);
  }

  String action = 'search';
  String searchOption = 'search';
  final cancelLabel = 'cancel';
  final searchHeight = 40.0;
  final customSearchRadius = 22.0;
  final widgetMargin = 10.0;
  final gapPadding = 0.0;
  final fontSize = 14.sp;
  final minimumLength = 1;
  late IconsProvider iconsProvider;
  late TextProvider textProvider;
  late LocalizationProvider localizationProvider;
  String? searchHint;

  bool get isAr => localizationProvider.locals.language == 'ar';

  @override
  Widget build(BuildContext context) {
    iconsProvider = context.watch<DependencyManager>().icons;

    textProvider = context.watch<DependencyManager>().text;
    localizationProvider = context.watch<DependencyManager>().localization;
    listContainerHeight =
        widget.listContainerHeight ?? MediaQuery.of(context).size.height / 4;
    searchHint = widget.hintLabel ??
        localizationProvider.resources.getWithKey(COMINGSOON_SEARCHOPTION);
    textField = widget.textFieldBuilder != null
        ? widget.textFieldBuilder!(_controller, _focusNode)
        : Container(
            decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.all(Radius.circular(customSearchRadius))),
            height: searchHeight,
            width: MediaQuery.of(context).size.width,
            margin: EdgeInsets.only(top: widgetMargin),
            child: TextField(
                controller: _controller,
                focusNode: _focusNode,
                textInputAction: TextInputAction.unspecified,
                // onSubmitted: (value) {
                // Navigator.pushNamed(context, StudentSearchScreen.routeName);
                // textController.clear();
                // },
                style: textProvider.buildStyle(fontSize, FontWeights.semiBold,
                    ThemeColors.black1A1818, null, null),
                decoration: InputDecoration(
                  hintText: searchHint,
                  hintStyle: textProvider.buildStyle(
                      fontSize,
                      FontWeights.semiBold,
                      ThemeColors.color8E8E93,
                      null,
                      null),
                  filled: true,
                  contentPadding: EdgeInsets.zero,
                  fillColor: ThemeColors.colorF2F2F7,
                  enabledBorder: OutlineInputBorder(
                    borderSide:
                        const BorderSide(color: ThemeColors.primaryColor),
                    borderRadius: BorderRadius.circular(customSearchRadius),
                    gapPadding: gapPadding,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide:
                        const BorderSide(color: ThemeColors.primaryColor),
                    borderRadius: BorderRadius.circular(customSearchRadius),
                    gapPadding: gapPadding,
                  ),
                  prefixIcon: GestureDetector(
                    onTap: () {
                      setState(() {
                        action != searchOption ? removeText() : print('');

                        _controller.text = '';
                        textController.text = '';
                        // Navigator.pop(context);
                      });
                    },
                    child: textController.text.length >= minimumLength
                        ? iconsProvider.cancelButton
                        : iconsProvider.parametrizedIcon(
                            iconsProvider.search,
                            width: 24.w,
                            height: 24.w,
                          ),
                  ),
                )),
          );

    final column = Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        if (widget.hideSearchBoxWhenItemSelected && notifier.value != null)
          const SizedBox(height: 0)
        else
          CompositedTransformTarget(
            link: _layerLink,
            child: textField,
          ),
      ],
    );
    return column;
  }

  void onDropDownItemTap(item) {
    if (overlayEntry != null) {
      overlayEntry?.remove();
    }
    overlayEntry = null;
    _focusNode?.unfocus();
    _controller.clear();
    setState(() {
      isFocused = false;
      isRequired = false;
    });
    if (widget.onItemSelected != null) {
      widget.onItemSelected!(item);
      searchHint = isAr ? item.nameAr : item.name;
      widget.hintLabel = isAr ? item.nameAr : item.name;
    }
  }

  void onTap() {
    final RenderBox textFieldRenderBox =
        context.findRenderObject() as RenderBox;
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    final width = textFieldRenderBox.size.width;
    final ScrollController _controllerOne = ScrollController();
    final position = RelativeRect.fromRect(
      Rect.fromPoints(
        textFieldRenderBox.localToGlobal(
          textFieldRenderBox.size.topLeft(Offset.zero),
          ancestor: overlay,
        ),
        textFieldRenderBox.localToGlobal(
          textFieldRenderBox.size.topRight(Offset.zero),
          ancestor: overlay,
        ),
      ),
      Offset.zero & overlay.size,
    );
    overlayEntry = OverlayEntry(
      builder: (context) {
        final height = MediaQuery.of(context).size.height;
        return Positioned(
          left: position.left,
          width: width,
          child: CompositedTransformFollower(
            offset: Offset(
              0,
              height - position.bottom < listContainerHeight!
                  ? (textBoxHeight + 6.0)
                  : -(listContainerHeight! - 8.0),
            ),
            showWhenUnlinked: false,
            link: _layerLink,
            child: Container(
              height: listContainerHeight,
              margin: const EdgeInsets.symmetric(horizontal: 12),
              child: Card(
                color: ThemeColors.primaryColor,
                elevation: 5,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(4)),
                ),
                child: (_tempList ?? []).isNotEmpty
                    ? Scrollbar(
                        thumbVisibility: true,
                        controller: _controllerOne,
                        child: ListView.separated(
                          controller: _controllerOne,
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          separatorBuilder: (context, index) => const Divider(
                            height: 1,
                          ),
                          itemBuilder: (context, index) => Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: () => {
                                onDropDownItemTap(_tempList?[index]),
                              },
                              child: widget.popupListItemBuilder(
                                _tempList?.elementAt(index),
                              ),
                            ),
                          ),
                          itemCount: (_tempList ?? []).length,
                        ),
                      )
                    : widget.noItemsFoundWidget != null
                        ? Center(
                            child: widget.noItemsFoundWidget,
                          )
                        : NoItemFound(),
              ),
            ),
          ),
        );
      },
    );
    Overlay.of(context).insert(overlayEntry!);
  }

  @override
  void dispose() {
    if (overlayEntry != null) {
      overlayEntry?.remove();
    }
    overlayEntry = null;
    super.dispose();
  }

  removeText() {
    textController.clear();
    // searchHint = '';
    // _controller.clear();
    action = searchOption;
  }
}

class NoItemFound extends StatelessWidget {
  String? title;
  dynamic icon;
  late LocalizationProvider localizationProvider;
  late IconsProvider iconsProvider;
  late TextProvider textProvider;
  final marginWidth = 10.0;

  NoItemFound({
    Key? key,
    this.title,
    this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    localizationProvider = context.watch<DependencyManager>().localization;
    iconsProvider = context.watch<DependencyManager>().icons;

    textProvider = context.watch<DependencyManager>().text;

    title =
        localizationProvider.resources.getWithKey(HOME_SEARCH_NORESULTSFOUND);
    return Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          iconsProvider.subject,
          SizedBox(width: marginWidth),
          textProvider.buildNormalText3(title,
              weight: FontWeights.regular, color: ThemeColors.darkGrey)
        ],
      ),
    );
  }
}
