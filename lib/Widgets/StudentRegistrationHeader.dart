// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:provider/provider.dart';

import '../Enums/FontWeights.dart';
import '../Providers/DependencyManager/DependencyManager.dart';
import '../Providers/DependencyManager/TextProviders/TextProvider.dart';

class StudentRegisterationHeader extends StatelessWidget {
  final int? value;
  final String? title;
  final String? subtitle;

  StudentRegisterationHeader({
    required this.value,
    required this.title,
    required this.subtitle,
  });

  late TextProvider textProvider;

  final radius = 30.0;
  final lineWidth = 3.5;
  final totalValue = 5;
  final contentMargin = 20.0;
  final textMargin = 5.0;

  @override
  Widget build(BuildContext context) {
    _initializeData(context);
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(width: contentMargin),
        _buildText(),
      ],
    );
  }

  Widget _buildProgressIndicator() {
    return CircularPercentIndicator(
      radius: radius,
      lineWidth: lineWidth,
      percent: value! / totalValue,
      progressColor: ThemeColors.accentColor,
      backgroundColor: ThemeColors.secondaryColor,
      center: Directionality(
        textDirection: TextDirection.ltr,
        child: textProvider.buildNormalText1(
          '$value / $totalValue',
          weight: FontWeights.semiBold,
        ),
      ),
    );
  }

  Widget _buildText() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          textProvider.buildTitle2(title, weight: FontWeights.semiBold),
          SizedBox(height: textMargin),
          textProvider.buildNormalText3(
            subtitle,
            weight: FontWeights.light,
            color: ThemeColors.darkGrey,
          ),
        ],
      ),
    );
  }

  void _initializeData(BuildContext context) {
    textProvider = context.watch<DependencyManager>().text;
  }
}
