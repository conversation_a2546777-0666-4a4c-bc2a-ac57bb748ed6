import 'dart:developer';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:modarby/Enums/AttachmentType.dart';
import 'package:modarby/Enums/Roles.dart';
import 'package:modarby/Models/Messages/MessageUserInfoModel.dart';
import 'package:modarby/Models/Search/SearchProfileDTO.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/InstructorProfileProvider.dart';
import 'package:modarby/Providers/SearchProvider.dart';
import 'package:modarby/Providers/StudentProfileProvider.dart';
import 'package:modarby/Services/service/MessagesRepository.dart';
import 'package:modarby/Widgets/customDialog.dart';
import 'package:modarby/core/Utilities/Snackbars.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/page_loading_dialog/page_loading_dialog.dart';
import 'package:modarby/core/utilities/ProgressIndicators.dart';
import 'package:modarby/core/utilities/error_parser.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';
import 'package:modarby/core/utilities/navigation_service.dart';
import 'package:modarby/core/utilities/shard.dart';
import 'package:modarby/core/utilities/storage.dart';
import 'package:modarby/features/messages/domain/entity/delete_message_input_entity.dart';
import 'package:modarby/features/voice_call/data/call_provider.dart';
import 'package:provider/provider.dart';
import 'package:vibration/vibration.dart';

import '../Models/Messages/Attachment.dart';
import '../Models/Messages/ConversationMessage.dart';
import '../Models/Messages/ConversationModel.dart';
import '../Models/Messages/Messages.dart';

class MessagesProvider with ChangeNotifier {
  static const messageIdKey = 'M';
  static const _CONVERSATION_PATH = 'chat';
  static const _SENDER_KEY = 'sid';
  static const _MESSAGE_ID = 'id';

  static const _RECEIVER_KEY = 'rid';
  static const _MESSAGE_KEY = 'text';
  static const _IS_ATTACHMENT_KEY = 'isattachment';
  static const _ATTACHMENT_TYPE_KEY = 'attchmenttypeid';
  static const ATTACHMENT_FILE_NAME_KEY = 'AttachmentFileName';

  late MessagesRepository _messagesRepository;
  Messages? _messages;
  int? _unread;
  Conversation? conversation;
  bool isLoading = false;
  bool isFirstLoading = false;
  bool canLoadMore = true;
  List<ConversationMessage>? conversationMessages = [];
  Messages? get messages => _messages;
  int? get unread => _unread;
  bool haveMessageInstructorReply = false;
  bool haveMessageFromMe = false;

  List<ConversationMessage> listSelectedMessages = [];

  late DatabaseReference _pusher;

  MessagesProvider() {
    _messagesRepository = MessagesRepository();
  }

  Future<Messages?> getUserChat(String? accessToken, int pageIndex) async {
    try {
      final messages =
          await _messagesRepository.getMessages(accessToken, pageIndex);
      if (pageIndex == 1) {
        _messages = messages;
      } else if (pageIndex > 1) {
        (_messages?.conversations ?? []).addAll(messages.conversations ?? []);
      }
      _calculateUnreadMessagesNumber();
    } catch (error) {
      _messages = null;
    }
    notifyListeners();
    return _messages;
  }

  void _calculateUnreadMessagesNumber() {
    int unread = 0;
    for (Conversation conversation in (_messages?.conversations ?? [])) {
      unread += conversation.unread ?? 0;
    }
    _unread = unread;
    notifyListeners();
  }

  Future<List<ConversationMessage>?> getConversationMessages(
    String? accessToken,
    String? userId,
    AuthenticationProvider authenticationProvider, {
    bool? isForward = false,
    List<ConversationMessage>? list,
    required int pageIndex,
    required int pageSize,
  }) async {
    try {
      if (pageIndex == 0) {
        isFirstLoading = true;
      }
      isLoading = true;
      notifyListeners();
      final messages = await _messagesRepository.getConversationDetailsMessages(
        accessToken,
        userId,
        pageIndex,
        pageSize,
      );
      conversationMessages?.addAll(messages);
      notifyListeners();
      (conversationMessages ?? []).sort((a, b) {
        return (a.id!).compareTo(b.id!);
      });
      canLoadMore = messages.isNotEmpty;
      isLoading = false;
      if (pageIndex == 0) {
        isFirstLoading = false;
      }
      notifyListeners();
      checkIfMessageInstructor(conversationMessages, userId);
      checkIfMessageMe(conversationMessages);
      notifyListeners();
      await Future.delayed(const Duration(milliseconds: 500));
      notifyListeners();
    } catch (error) {
      isLoading = false;
      canLoadMore = false;
      isFirstLoading = false;
      notifyListeners();
      log(error.toString());
    }
    return conversationMessages;
  }

  List<ConversationMessage> sortMessagesByDateTime(
      List<ConversationMessage>? messages) {
    DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
    try {
      messages?.sort((a, b) {
        DateTime aDateTime = dateFormat.parse('${a.date}');
        DateTime bDateTime = dateFormat.parse("${b.date}");
        return aDateTime.compareTo(bDateTime);
      });
    } catch (e) {
      log(e.toString());
    }
    return messages ?? [];
  }

  String getDateFormat() {
    DateTime now = DateTime.now().toUtc();
    String formattedDate =
        DateFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS\'Z\'').format(now);

    return formattedDate;
  }

  Future<List<ConversationMessage>?> appendSocketMessage(Map message) async {
    try {
      final newMessage = ConversationMessage(
          userId: message[_SENDER_KEY],
          message: message[_MESSAGE_KEY],
          date: getDateFormat(),
          isAttachment: message[_IS_ATTACHMENT_KEY],
          attchmentType: message[_ATTACHMENT_TYPE_KEY],
          timezone: null,
          id: message[_MESSAGE_ID],
          attachFileName: message[ATTACHMENT_FILE_NAME_KEY]);
      (conversationMessages ?? []).add(newMessage);
      final typeAttachment = newMessage.attchmentType;
      final isMessageFromCurrentUser =
          conversation?.userId != newMessage.userId;
      if ((typeAttachment == AttachmentType.acceptRequestCall ||
              typeAttachment == AttachmentType.rejectRequestCall) &&
          isMessageFromCurrentUser.inverted) {
        getIt<CallProvider>().updateIsAllowedMeToCall(
            typeAttachment == AttachmentType.acceptRequestCall);
      }
      notifyListeners();
      return conversationMessages;
    } catch (error) {
      print(error);
      return conversationMessages;
    }
  }

  Future<bool> _sendMessageToServer(
    String? accessToken,
    Map<String, dynamic> message,
    String userId,
  ) async {
    try {
      final addModel = await _messagesRepository.sendMessage(
          accessToken,
          message[_SENDER_KEY],
          message[_RECEIVER_KEY],
          message[_MESSAGE_KEY],
          message[_IS_ATTACHMENT_KEY],
          message[_ATTACHMENT_TYPE_KEY],
          message[ATTACHMENT_FILE_NAME_KEY]);

      message[_MESSAGE_ID] = addModel.messageId;
      if (addModel.messageId != null) {
        // remove await from here
        _sendMessageToFirebaseAndRemove(message, userId);
        appendSocketMessage(message);
        notifyListeners();
      }
      return Future.value(true);
    } catch (error) {
      if (message[_ATTACHMENT_TYPE_KEY] == AttachmentType.requestCall) {
        rethrow;
      }
      final errorException = ErrorParser().parseError(error);
      Snackbars.danger(StaticVar.context, errorException.message);
      debugPrint(errorException.message);
      return Future.value(false);
    }
  }

  Future<void> deleteMessage(String? accessToken, String? userId) async {
    ProgressIndicators.loadingDialog(NavigationService.instance.appContext!);
    try {
      for (var item in listSelectedMessages) {
        if (item.id != null && _isCurrentUser(item.userId, userId)) {
          final input = DeleteMessageInputEntity(messageId: item.id ?? 0);
          await _messagesRepository.deleteMessage(
            accessToken,
            input,
          );
          pushDeleteMessage(userId, item);
          if (item.id != null) {
            deleteMessageFromConversation(accessToken, item.id ?? 0);
          }
        }
      }
      listSelectedMessages = [];
      notifyListeners();
      Navigator.pop(NavigationService.instance.appContext!);
    } catch (error) {
      Navigator.pop(NavigationService.instance.appContext!);
      final errorException = ErrorParser().parseError(error);
      debugPrint(errorException.message);
    }
  }

  Future<StudentAdditionalInfo?> getStudentInfoMessage(String? userId) async {
    final accessToken =
        StaticVar.context.read<AuthenticationProvider>().accessToken;
    if (userId == null || accessToken == null) {
      return null;
    }
    try {
      final result = await _messagesRepository.getStudentInfoMessage(
        userId,
        accessToken,
      );
      notifyListeners();
      return result;
    } catch (error) {
      final errorException = ErrorParser().parseError(error);
      debugPrint(errorException.message);
      return null;
    }
  }

  Future<MessageUserInfoModel?> getUserInfo(String? userId,
      {bool showLoading = false}) async {
    final accessToken =
        StaticVar.context.read<AuthenticationProvider>().accessToken;
    if (userId == null || accessToken == null) return null;
    PageLoadingDialogStatus? loader;
    if (showLoading) {
      loader = getIt<IPageLoadingDialog>().showLoadingDialog();
    }

    try {
      final result = await _messagesRepository.getUserInfo(
        userId,
        ((getIt<Storage>().role == Roles.REFERRAL ||
                    getIt<Storage>().role == Roles.INSTRUCTOR)
                .inverted)
            ? 0
            : 1,
        accessToken,
      );
      if (showLoading) {
        loader?.hide();
      }

      notifyListeners();
      return result;
    } catch (error) {
      if (showLoading) {
        loader?.hide();
      }

      final errorException = ErrorParser().parseError(error);
      debugPrint(errorException.message);
      return null;
    }
  }

  Future<Attachment> addAttachment(
      String? accessToken, String filePath, int type) async {
    try {
      final file =
          await _messagesRepository.addAttachment(accessToken, filePath, type);
      return file;
    } catch (error) {
      rethrow;
    }
  }

  Future<bool?> checkContent(String? accessToken, String content) async {
    try {
      final isValid =
          await _messagesRepository.checkContent(accessToken, content);
      return isValid;
    } catch (error) {
      final errorException = ErrorParser().parseError(error);
      debugPrint(errorException.message);

      return false;
    }
  }

  void clear() {
    clearSelectedMessages();
    isFirstLoading = true;
    conversationMessages = [];
    isLoading = false;
    canLoadMore = true;
    notifyListeners();
  }

  void logout() {
    _messages = null;
    conversationMessages = [];
    _unread = null;
    conversation = null;
    clear();
    notifyListeners();
  }

  Future<void> deleteMessageFromConversation(
      String? accessToken, int messageId) async {
    int index = (conversationMessages ?? [])
        .indexWhere((element) => element.id.toString() == messageId.toString());
    if (index != -1) {
      conversationMessages?.remove(conversationMessages?[index]);
    }
    notifyListeners();
    getUserChat(accessToken, 1);
  }

  void doSelectionMessage(ConversationMessage conversationMessage) {
    if (conversationMessages != null &&
        checkVoiceCall(conversationMessage).inverted) {
      if (isSelected(conversationMessage)) {
        listSelectedMessages.remove(conversationMessage);
      } else {
        _applyVibration();
        listSelectedMessages.add(conversationMessage);
      }
      notifyListeners();
    }
  }

  bool isSelected(ConversationMessage conversationMessage) {
    return listSelectedMessages.contains(conversationMessage);
  }

  void clearSelectedMessages() {
    listSelectedMessages = List.empty(growable: true);
    notifyListeners();
  }

  bool _isCurrentUser(String? userId, String? messageUserId) {
    return (userId ?? '') != (messageUserId ?? '');
  }

  void copyMessagesToMemory() {
    StaticVar.listMessagesCopy = listSelectedMessages;

    Clipboard.setData(
      ClipboardData(
        text: listSelectedMessages
            .map((e) => e.message ?? '')
            .toList()
            .join('\n'),
      ),
    );
    listSelectedMessages = [];
    notifyListeners();
  }

  Future<void> _applyVibration() async {
    final hasCustomVibrationsSupport =
        await Vibration.hasCustomVibrationsSupport();
    if (hasCustomVibrationsSupport ?? false) {
      Vibration.vibrate(duration: 50);
    } else {
      Vibration.vibrate(duration: 50);
    }
  }

  Future<void> openConfirmDeleteDialog(
      String? accessToken, String? userId) async {
    final isSelected = await _openConfirmDeleteDialog();
    if (isSelected) {
      deleteMessage(accessToken, userId);
    }
  }

  Future<bool> _openConfirmDeleteDialog() async {
    final isSelected = await showDialog(
      context: StaticVar.context,
      builder: (_) => CustomDialog(
        submitColor: ThemeColors.color26467A,
        title: areYouSureWantDeleteMessages.translate(),
      ),
    );
    return isSelected ?? false;
  }

  Future<void> pushDeleteMessage(
      String? conversationUserId, ConversationMessage item) async {
    _pusher =
        FirebaseDatabase.instance.ref().child('chatDelete/$conversationUserId');
    final ref = _pusher.push();
    await ref.set(item.toMap());
    await Future.delayed(const Duration(milliseconds: 300));
    await _pusher.child(ref.key!).remove();
  }

  /// send messages

  Future<void> sendListMessage(List<ConversationMessage>? list,
      AuthenticationProvider authenticationProvider, String? userId) async {
    if (userId != null) {
      for (var message in list ?? []) {
        final msg = {
          _SENDER_KEY: authenticationProvider.userId,
          _RECEIVER_KEY: userId,
          _MESSAGE_KEY: message.message,
          _IS_ATTACHMENT_KEY: message.isAttachment ?? false,
          _ATTACHMENT_TYPE_KEY: message.attchmentType,
          ATTACHMENT_FILE_NAME_KEY: message.attachFileName ?? '',
        };
        await _sendMessageToServer(
          authenticationProvider.accessToken,
          msg,
          userId,
        );
        await Future.delayed(const Duration(microseconds: 500));
      }
      notifyListeners();
    }
  }

  Future<void> sendMessageLocal(
      String? userId,
      AuthenticationProvider authenticationProvider,
      String? message,
      bool isAttachment,
      String attachFileName,
      [int? type]) async {
    if (userId != null) {
      final msg = {
        _SENDER_KEY: authenticationProvider.userId,
        _RECEIVER_KEY: userId,
        _MESSAGE_KEY: message,
        _IS_ATTACHMENT_KEY: isAttachment,
        _ATTACHMENT_TYPE_KEY: type,
        ATTACHMENT_FILE_NAME_KEY: attachFileName,
      };
      await _sendMessageToServer(
        authenticationProvider.accessToken,
        msg,
        userId,
      );
    }
  }

  Future<bool> _sendMessageToFirebaseAndRemove(
      Map<String, Object?> msg, String userId) async {
    _pusher =
        FirebaseDatabase.instance.ref().child('$_CONVERSATION_PATH/$userId');
    final ref = _pusher.push();
    await ref.set(msg);
    await _pusher.child(ref.key!).remove();
    return Future.value(true);
  }

  void checkIfMessageInstructor(
      List<ConversationMessage>? messages, String? userId) {
    for (ConversationMessage item in messages ?? []) {
      if (userId != null && item.userId == userId) {
        haveMessageInstructorReply = true;
        notifyListeners();
        break;
      }
    }
  }

  void checkIfMessageMe(
    List<ConversationMessage>? messages,
  ) {
    String? userId;
    if (Shard().isStudent) {
      userId = StaticVar.context.read<StudentProfileProvider>().student?.userId;
    } else {
      userId = StaticVar.context
          .read<InstructorProfileProvider>()
          .instructor
          ?.userId;
    }
    if (userId == null) return;
    for (ConversationMessage item in messages ?? []) {
      if (item.userId == userId) {
        haveMessageFromMe = true;
        notifyListeners();
        break;
      }
    }
  }

  int? _getTimeZone() {
    final code = getIt<Storage>().countryCode;
    final timeZoneID = Shard().getTimezoneWithIsoCountryCode(code);
    return timeZoneID;
  }

  Future<void> getInstructor() async {
    final instructorProfile =
        await StaticVar.context.read<SearchProvider>().searchInstructorProfile(
              SearchProfileDTO(
                languageId: Shard().languageId,
                currencyId: Shard().currencyId,
                guid: conversation?.userId,
                timezone: _getTimeZone(),
              ),
            );
    conversation?.setInstructorInformation = InstructorInformation(
      totalNumberOfLessons: instructorProfile?.totalLessons ?? 0,
      totalNumberOfStudent: instructorProfile?.totalStudents ?? 0,
      totalReviews: instructorProfile?.totalReviews ?? 0,
      ratingPercent: instructorProfile?.totalRating ?? 0,
      isVerified: instructorProfile?.isVerified ?? false,
    );
    notifyListeners();
  }

  bool checkVoiceCall(ConversationMessage message) {
    final type = message.attchmentType;
    return type == AttachmentType.callReceiverInAnotherCall ||
        type == AttachmentType.requestCall ||
        type == AttachmentType.acceptRequestCall ||
        type == AttachmentType.rejectRequestCall ||
        type == AttachmentType.callFinished ||
        type == AttachmentType.callNoAnswer ||
        type == AttachmentType.callBusy ||
        type == AttachmentType.callReceiverInAnotherCall;
  }

  Future<void> getStudentDataAndUpdate() async {
    final userId = conversation?.userId;
    final result = await getStudentInfoMessage(userId);
    if (result != null) {
      conversation?.setStudentAdditionalInfo = result;
    }
  }
}
