import 'package:flutter/foundation.dart';

import '../Models/Payments/CountryBanks.dart';
import '../Models/Payments/CountryOffices.dart';
import '../Services/Payment/PaymentRepository.dart';

class PaymentProvider with ChangeNotifier {
  late PaymentRepository _paymentRepository;
  List<CountryOffices>? _countriesOffices;
  List<CountryBanks>? _countriesBanks;

  List<CountryOffices>? get countriesOffices => _countriesOffices;
  List<CountryBanks>? get countriesBanks => _countriesBanks;

  PaymentProvider() {
    _paymentRepository = PaymentRepository();
  }

  Future<List<CountryOffices>?> getCountriesOffices(
      String? accessToken, int? languageId) async {
    try {
      final countriesOffices =
          await _paymentRepository.getOffices(accessToken, languageId);
      _countriesOffices = countriesOffices;
    } catch (error) {
      print(error);
      _countriesOffices = null;
    }
    notifyListeners();
    return _countriesOffices;
  }

  Future<List<CountryBanks>?> getCountriesBanks(
      String? accessToken, int? languageId) async {
    try {
      final countriesBanks =
          await _paymentRepository.getBanks(accessToken, languageId);
      _countriesBanks = countriesBanks;
    } catch (error) {
      print(error);
      _countriesBanks = null;
    }
    notifyListeners();
    return _countriesBanks;
  }

  Future<bool> makeBankTransfer(
      String? accessToken,
      String imagePath,
      String? bookingGuid,
      String name,
      String? country,
      String date,
      double amount,
      int? currency,
      int? language) async {
    try {
      await _paymentRepository.makeBankTransfer(accessToken, imagePath,
          bookingGuid, name, country, date, amount, currency, language);
      return true;
    } catch (error) {
      print(error);
      rethrow;
    }
  }
}
