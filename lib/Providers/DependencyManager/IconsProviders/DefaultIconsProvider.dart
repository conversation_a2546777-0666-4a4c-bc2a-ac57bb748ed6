import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'IconsProvider.dart';

@override
class DefaultIconsProvider implements IconsProvider {
  @override
  Widget buildIcon(String fileName,
      {BoxFit fit = BoxFit.scaleDown,
      double? width,
      double? height,
      Color? color,
      double? scale}) {
    if (_isSvg(fileName)) {
      return SvgPicture.asset('assets/icons/$fileName',
          fit: fit, width: width, height: height, color: color);
    }
    return Image.asset('assets/icons/$fileName',
        fit: fit, width: width, height: height, color: color, scale: scale);
  }

  @override
  Widget buildImage(String fileName, {BoxFit fit = BoxFit.scaleDown}) {
    if (_isSvg(fileName)) {
      return SvgPicture.asset('assets/images/$fileName', fit: fit);
    }
    return Image.asset(
      'assets/images/$fileName',
      fit: fit,
    );
  }

  @override
  Widget parametrizedIcon(Widget icon,
      {BoxFit fit = BoxFit.scaleDown,
      double? width,
      double? height,
      Color? color,
      double? scale}) {
    String fileName = _iconName(icon as SvgPicture);
    return buildIcon(fileName,
        fit: fit, width: width, height: height, color: color, scale: scale);
  }

  String _iconName(SvgPicture icon) {
    String fullPath = icon.pictureProvider.toString();
    int firstIdx = fullPath.indexOf('"');
    int secondIdx = fullPath.indexOf('"', firstIdx + 1);
    String path = fullPath.substring(firstIdx + 1, secondIdx);
    List pathList = path.split('/');
    return pathList.last;
  }

  bool _isSvg(String fileName) {
    List nameList = fileName.split('.');
    return nameList.last == 'svg' ? true : false;
  }

  @override
  get searchIconBottomBar => buildIcon('search_icon_bottom_bar.svg');

  @override
  get searchIconSolidBottomBar => buildIcon('search_icon_solid_bottom_bar.svg');

  @override
  get playVideo => buildIcon('play_video.svg');

  @override
  get squareMenu => buildIcon('about_us_icon.svg');

  @override
  get circleMenu => buildIcon('menu_icon.svg');

  @override
  get personCheck => buildIcon('a-check.svg');

  @override
  get checkRight => buildIcon('checkRight.svg');

  @override
  get goArrow => buildIcon('goArrow.svg');

  @override
  get rightSwipe => buildIcon('right_swipe.svg');

  @override
  get leftSwipe => buildIcon('left_swipe.svg');

  @override
  get editComment => buildIcon('edit-comment.svg');

  @override
  get fastTime => buildIcon('fast-time.svg');

  @override
  get popOverLiveRequest => buildIcon('popOverLiveRequest.svg');

  @override
  get popOverLiveRequestAR => buildIcon('popOverLiveRequestAR.svg');

  @override
  get leftArrow => buildIcon('arrow-left.svg');
  @override
  get nextLeftArrow => buildIcon('nextLeftArrow.svg');
  @override
  get nextRightArrow => buildIcon('nextRightArrow.svg');
  @override
  get marketingSvg => buildIcon('marketing.svg');
  @override
  get closeIconWithBorder => buildIcon('close_with_border.svg');

  @override
  get profileAvatar => buildIcon('profile-avatar.svg');

  @override
  get rightArrow => buildIcon('arrow-right.svg');

  @override
  get deactivate => buildIcon('a-tag-remove.svg');

  @override
  get award => buildIcon('award_icon.svg');

  @override
  get backButton => buildIcon('back_icon.svg');

  @override
  get bank => buildIcon('bank_icon.svg');

  @override
  get share => buildIcon('share.svg');

  @override
  get visa => buildIcon('visa.svg');

  @override
  get visaCard => buildIcon('visa_card_icon.svg');

  @override
  get paypal => buildIcon('paypal.svg');

  // @override
  // get bell => buildIcon('bell.svg');

  @override
  get blueBell => buildIcon('blue_outlined_bell.svg');

  @override
  get bin => buildIcon('bin.svg');

  @override
  get referralStudentAddedSuccessfully =>
      buildIcon('referral_student_added_successfully.svg');

  @override
  get referralWarning => buildIcon('referral_warning.svg');

  @override
  get referralStudent => buildIcon('referral_student.svg');

  @override
  get referralStudentAction => buildIcon('referral_student_edit.svg');

  @override
  get noReferralStudents => buildIcon('no_referral_students.svg');

  @override
  get transactionArrow => buildIcon('transaction_arrow.svg');

  @override
  get transactionArArrow => buildIcon('transactions_arrow_ar.svg');

  @override
  get balanceArrow => buildIcon('balanceArrow.svg');

  @override
  get balanceArArrow => buildIcon('pay_ar.svg');

  @override
  get referralUrl => buildIcon('referral_url.svg');

  @override
  get referralCopyUrl => buildIcon('referral_url_copy.svg');

  @override
  get addReferral => buildIcon('add_referral.svg');

  @override
  get addReferralSolid => buildIcon('add_referral_solid.svg');

  @override
  get referralTransactions => buildIcon('referral_transactions.svg');

  @override
  get referralTransactionsSolid => buildIcon('referral_transactions_solid.svg');

  @override
  get bill => buildIcon('bill.svg');

  @override
  get noNotifications => buildIcon('no_notifications.svg');

  @override
  get notificationsType1 => buildIcon('type_1.svg');

  @override
  get chevron => buildIcon('chevron_right.svg');

  @override
  get chevronAr => buildIcon('chevron_left.svg');

  @override
  get notificationsType2 => buildIcon('type_2.svg');

  @override
  get notificationsType3 => buildIcon('type_3.svg');

  @override
  get blueMail => buildIcon('blue_email_icon.svg');

  @override
  get blueFlag => buildIcon('blue-flag.svg');

  @override
  get bookmark => buildIcon('bookmark_icon.svg');

  @override
  get blackBookmark => buildIcon('bookmark_black_icon.svg');

  @override
  get sendMessage => buildIcon('send-message.svg');

  @override
  get calculator => buildIcon('calculator_icon.svg');

  @override
  get calendar => buildIcon('calender_icon.svg');

  @override
  get calendarOutlined => buildIcon('calendar_date_icon.svg');

  @override
  get camera => buildIcon('camera_icon.png');

  @override
  get cancelButton => buildIcon('cancel_icon.svg');

  @override
  get money => buildIcon('cash_icon.svg');

  @override
  get currencyImage => buildIcon('choose_currency_icon.svg');

  // @override
  // get languageImage => buildIcon('choose_language_icon.svg');

  @override
  get personRounded => buildIcon('circle-profile.svg');

  @override
  get clock => buildIcon('clock.svg');

  @override
  get clockSolid => buildIcon('clock-solid.svg');

  @override
  get clockWithHours => buildIcon('clock_icon.svg');

  @override
  get signals => buildIcon('contactless.svg');

  @override
  get greenCircleCheck => buildIcon('green-circle-check.svg');

  @override
  get crown => buildIcon('crown_icon.png');

  @override
  get plus => buildIcon('e-add.svg');

  @override
  get minus => buildIcon('e-minus.svg');

  @override
  get close => buildIcon('e-remove.svg');

  @override
  get mail => buildIcon('email_icon.svg');

  @override
  get whatsappButton => buildIcon('fab_whatsapp_icon.svg');

  @override
  get film => buildIcon('film_icon.svg');

  @override
  get deliver => buildIcon('deliver_icon.svg');

  @override
  get stopWatch => buildIcon('filter_timer_icon.svg');

  @override
  get male => buildIcon('gender_icon.svg');

  @override
  get home => buildIcon('home_icon.svg');

  @override
  get homeSolid => buildIcon('home_solid_icon.svg');

  @override
  get aim => buildIcon('hotspot_icon.svg');

  @override
  get apps => buildIcon('Icon_apps.png');

  @override
  get world => buildIcon('ielts_icon.svg');

  @override
  get starLogo => buildIcon('instant_booking_icon.svg');

  @override
  get starLogoGradient => buildIcon('instant_booking_icon_big.svg');

  @override
  get language => buildIcon('language_icon.svg');

  @override
  get speak => buildIcon('speak_icon.svg');

  @override
  get speakL => buildIcon('large_speaks_icon.svg');

  @override
  get leftBlueArrowButton => buildIcon('left_blue_arrow_icon.svg');

  @override
  get letter => buildIcon('letter.svg');

  @override
  get whatsappLogo => buildIcon('logo-whatsapp.svg');

  @override
  get mailOutlined => buildIcon('mail.svg');

  @override
  get bookmarkOutlined => buildIcon('mark_book_icon.svg');

  @override
  get message => buildIcon('messages_icon.svg');

  @override
  get messageNew => buildIcon('message_icon_new.svg');

  @override
  get messageNewSelected => buildIcon('message_icon_new_selected.svg');

  @override
  get messageSolid => buildIcon('messages_solid_icon.svg');

  @override
  get emptyMessage => buildIcon('message_icon.svg');

  @override
  get people => buildIcon('multi_persons_icon.svg');
  @override
  get similarTutors => buildIcon('similar_tutors.svg');

  @override
  get notifications => buildIcon('notification_icon.svg');

  @override
  get notificationPermission => buildIcon('notification_permission.svg');

  @override
  get window => buildIcon('window-add.svg');

  @override
  get cameraButton => buildIcon('online_lessons_icon.svg');

  @override
  get video => buildIcon('video.svg');

  @override
  get person => buildIcon('person.svg');

  @override
  get mobilePhone => buildIcon('phone-button.svg');

  @override
  get phone => buildIcon('phone_icon.svg');

  @override
  get redPhone => buildIcon('phone-call.svg');

  @override
  get physics => buildIcon('physics_icon.svg');

  @override
  get points => buildIcon('pin-2.svg');

  @override
  get location => buildIcon('pin-3.svg');

  @override
  get presentation => buildIcon('presentation.svg');

  @override
  get presentationSolid => buildIcon('presentation_solid.svg');

  @override
  get presentationSolidSelected => buildIcon('presentation_solid_selected.svg');

  @override
  get myLessons => buildIcon('empty_my_lessons_icon.svg');

  @override
  get presentationSmall => buildIcon('presentation_icon.svg');

  @override
  get range => buildIcon('price_range_icon.svg');

  @override
  get privacyLock => buildIcon('privacy.svg');

  @override
  get profileRounded => buildIcon('profile_small_icon.svg');

  @override
  get protection => buildIcon('protection_icon.svg');

  @override
  get refresh => buildIcon('refresh.svg');

  @override
  get refund => buildIcon('refund.svg');

  @override
  get reset => buildIcon('reset_filter_icon.svg');

  @override
  get dollarRounded => buildIcon('round-dollar.svg');
  @override
  get myTutors => buildIcon('my_tutors.svg');

  @override
  get filter => buildIcon('search_filter_icon.svg');

  @override
  get search => buildIcon('search_icon.svg');

  @override
  get security => buildIcon('security.svg');

  @override
  get send => buildIcon('send.svg');

  @override
  get shield => buildIcon('shield.svg');

  @override
  get right => buildIcon('small_right_arrow_icon.svg');

  @override
  get whatsappLogoSmall => buildIcon('small_whatsapp_icon.svg');

  @override
  get subject => buildIcon('subject_icon.svg');

  @override
  get callSupport => buildIcon('support-fab.png');

  @override
  get survey => buildIcon('survey_icon.svg');

  @override
  get balance => buildIcon('balance_profile_icon.svg');
  @override
  get balanceSolid => buildIcon('balance_solid_profile_icon.svg');
  @override
  get packageProfileInstructor => buildIcon('package_profile_icon.svg');
  @override
  get packageSolidProfileInstructor =>
      buildIcon('package_solid_profile_icon.svg');

  @override
  get thumbLogo => buildIcon('thumb_banner_icon.svg');

  @override
  get timer => buildIcon('timer_icon.png');

  @override
  get translate => buildIcon('translation_icon.svg');

  @override
  get warning => buildIcon('t-warning-e.svg');

  @override
  get unlocked => buildIcon('unlocked.svg');

  @override
  get profile => buildIcon('user_profile_icon.svg');

  @override
  get profileSolid => buildIcon('user_profile_solid_icon.svg');

  @override
  get verified => buildIcon('verified_icon.svg');

  @override
  get stopVideo => buildIcon('video-off.svg');

  @override
  get creditCard => buildIcon('credit-card.svg');

  @override
  get voiceSignals => buildIcon('voice-recognition.svg');

  @override
  get wallet => buildIcon('wallet.svg');

  @override
  get walletOutlined => buildIcon('wallet-outlined.svg');

  @override
  get worldSolid => buildIcon('world_icon.svg');

  @override
  get whatsapp => buildIcon('whatsapp_icon.svg');

  @override
  get cancelledStatus => buildIcon('cancelled_status.svg');

  @override
  get paidStatus => buildIcon('paid_status.svg');

  @override
  get refundingStatus => buildIcon('refunding_status.svg');

  @override
  get redStatus => buildIcon('red-status.svg');

  @override
  get yellowStatus => buildIcon('yellow-status.svg');

  @override
  get greenStatus => buildIcon('green-status.svg');

  @override
  get mexico => buildIcon('mexico.svg');

  @override
  get saudi => buildIcon('saudi-arabia.svg');

  @override
  get jordan => buildIcon('jordan.svg');

  @override
  get jordanSmall => buildIcon('jordan_flag_icon.svg');

  @override
  get kuwait => buildIcon('kuwait.svg');

  @override
  get uae => buildIcon('united-arab-emirates.svg');

  @override
  get trash => buildIcon('trash-can.svg');

  @override
  get add => buildIcon('c-add.svg');

  @override
  get call => buildIcon('call.svg');

  @override
  get securedField => buildIcon('secured-field.svg');

  @override
  get unsecuredField => buildIcon('unsecured-field.svg');

  @override
  get sortBy => buildIcon('sort_by.svg');

  @override
  get info => buildIcon('info.svg');
  @override
  get infoSolid => buildIcon('icon_info_solid.svg');

  @override
  get deviceConnection => buildIcon('device-connection.svg');

  @override
  get check => buildIcon('check.svg');

  @override
  get onlinePayment => buildIcon('online_payment.svg');

  @override
  get tamaraAr => buildIcon('tamara-ar.svg');

  @override
  get tamaraEn => buildIcon('tamara-en.svg');

  @override
  get upload => buildIcon('upload.svg');

  @override
  get percentage => buildIcon('percentage.svg');

  @override
  get checkAll => buildIcon('check-all.svg');

  @override
  get questionnaire => buildIcon('questionnaire.svg');

  @override
  get greenShield => buildIcon('green_shield.svg');

  @override
  get smallInfo => buildIcon('small_info.svg');

  @override
  get cycle => buildIcon('cycle.svg');

  @override
  get pending => buildIcon('survey.svg');

  @override
  get chosen => buildIcon('chosen.svg');

  @override
  get failed => buildIcon('failed.svg');

  @override
  get printer => buildIcon('printer.svg');

  @override
  get bookmarkSolid => buildIcon('bookmark_solid.svg');

  @override
  get messageMatsapp => buildIcon('message_matsapp.svg');

  @override
  get success => buildIcon('success.svg');

  @override
  get error => buildIcon('error.svg');

  @override
  get galleryPermission => buildIcon('gallery_icon.svg');

  @override
  get galleryManageSettings => buildIcon('manage_setting.svg');
  @override
  get callManageSettings => buildIcon('image_setting_call_profile.svg');

  @override
  get emailYellow => buildIcon('yellow_email.svg');

  @override
  get remove => buildIcon('remove.svg');

  @override
  get pinCode => buildIcon('pin_code.svg');

  @override
  get confirmationCode => buildIcon('confirmation_code.svg');

  @override
  get worldMarker => buildIcon('world_marker.svg');

  @override
  get swearHand => buildIcon('swear_hand.svg');

  @override
  get mobileChat => buildIcon('mobile_chat.svg');

  get file => buildIcon('file.svg');

  @override
  get attachment => buildIcon('attachment.svg');

  @override
  get download => buildIcon('download.svg');

  @override
  get cameraOutlined => buildIcon('cameraOutlined.svg');

  @override
  get gallery => buildIcon('gallery.svg');

  @override
  get files => buildIcon('files.svg');

  @override
  get inverseProcess => buildIcon('invert-process.svg');

  @override
  get greenCheck => buildIcon('green_check.svg');

  @override
  get splashLogo => buildIcon('splash_logo.svg');

  @override
  get applePayIcon => buildIcon('apple_pay_icon.svg');

  @override
  get appBarLogo => buildIcon('appbar_logo.svg');

  @override
  get missing => buildIcon('missing.svg');

  @override
  get blueCircle => buildIcon('small_circle.svg');

  @override
  get missingLogo => buildIcon('missing_logo.svg');

  @override
  get packagesIndicator => buildIcon('packages-indicator.png');

  @override
  get bubble => buildIcon('bubble.svg');

  @override
  get packagesEmoji => buildIcon('packages-emoji.png');

  @override
  get lessonsEmoji => buildIcon('lessons-emoji.png');
  //
  // @override
  // get bookingPackage => buildIcon('booking-package.png');

  @override
  get bookingMultiLessons => buildIcon('booking-multi-lessons.png');

  @override
  get package => buildIcon('package.svg');

  @override
  get lesson => buildIcon('lesson.svg');

  @override
  get originalPrice => buildIcon('original-price.svg');

  @override
  get discount => buildIcon('discount.svg');

  @override
  get discountedPrice => buildIcon('discounted-price.svg');

  @override
  get myPackages => buildIcon('my-packages.svg');

  @override
  get editPackages => buildIcon('edit-packages.svg');

  @override
  get singleLesson => buildIcon('single-lesson.svg');

  @override
  get packageLesson => buildIcon('package-lesson.svg');

  @override
  get lessonTypes => buildIcon('lesson-types.png');

  @override
  get lessonTypeOnline => buildIcon('lesson-type-online.svg');

  @override
  get lessonTypeInstructor => buildIcon('lesson-type-inperson.svg');

  @override
  get lessonTypeStudent => buildIcon('lesson-type-inperson.svg');

  @override
  get reviews => buildIcon('reviews.svg');

  @override
  get stars => buildIcon('stars.svg');

  @override
  get genderMen => buildIcon('gender-men.svg');

  @override
  get genderWomen => buildIcon('gender-women.svg');

  @override
  get genderBoth => buildIcon('gender-both.svg');

  @override
  get level => buildIcon('level.svg');

  @override
  get searchNotFound => buildIcon('search-not-found.png');

  @override
  get maramAvailable => buildIcon('maram.png');

  @override
  get onlineIndicator => buildIcon('online_indicator.svg');

  @override
  get handDrawnAr => buildIcon('hand_drawn_ar.svg');

  @override
  get handDrawnEn => buildIcon('hand_drawn_en.svg');

  @override
  get notificationsPermission => buildIcon('notification-permission.svg');

  @override
  get locationPermission => buildIcon('location-permission.svg');

  @override
  get checkMark => buildIcon('check-mark.svg');

  @override
  get checkMarkDisabled => buildIcon('check-mark-disabled.svg');

  @override
  get mic => buildIcon('mic_button_icon.svg');

  @override
  get additionalInfo => buildIcon('additional-info.svg');

  @override
  get helpMe => buildIcon('help-me.svg');

  @override
  get helpMeForm => buildIcon('help-me-form.svg');
  //
  // @override
  // get helpMeLanding => buildIcon('help-me-landing.svg');

  @override
  get helpMeOnline => buildIcon('help-me-online.svg');

  @override
  get helpMeInPerson => buildIcon('help-me-inperson.svg');

  @override
  get helpMeBoth => buildIcon('help-me-both.svg');

  @override
  get packageRange => buildIcon('package-range.svg');

  @override
  get packageTop => buildIcon('package-top.svg');

  @override
  get viewStudentExperience => buildIcon('viewStudentExperience.svg');

  @override
  get completeRegistration => buildIcon('completeRegistration.svg');

  // * Images

  // @override
  // get sliderImage1 => buildImage('slider_image_1.png');

  // @override
  // get sliderImage2 => buildImage('slider_image_2.png');

  @override
  // get sliderImage3 => buildImage('slider_image_3.png');
  //
  // @override
  // get sliderImage4 => buildImage('slider_image_4.png');

  // @override
  // get sliderImage5 => buildImage('slider_image_5.png');

  // @override
  // get supportPerson => buildImage('haneen.png');

  @override
  get flagPlaceholder => buildImage('flag-placeholder.png');

  @override
  get checked => buildImage('checked.png');

  @override
  get persons => buildImage('persons.png');

  @override
  get verifyImage => buildImage('verify_image.png');

  @override
  get thanksBanner => buildImage('thanks_banner.png');

  @override
  get marketing => buildImage('affiliate-marketing.png');

  @override
  get personPlaceHolder => buildImage('personPlaceHolder.png');

  @override
  get helpMeSuccess => buildIcon('help-me-success.gif');

  @override
  get backRequests => buildImage('live_tutors/arrow_left.svg');

  @override
  get forwardRequests => buildImage('live_tutors/arrow_right.svg');

  @override
  get availability => buildImage('live_tutors/availability_icon.svg');

  @override
  get changeSettings => buildImage('live_tutors/change_settings_icon.svg');
}
