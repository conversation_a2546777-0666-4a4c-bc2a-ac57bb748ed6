import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:modarby/Models/Content/StudentExtrnalSource.dart';
import 'package:modarby/Models/Content/TraineeTypes.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/DefaultLocalizationDataProviders/LocalDataProvider.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../Enums/SharedPreferencesKeys.dart';
import '../../../../Models/Content/AppStoreUrl.dart';
import '../../../../Models/Content/BookingPaymentStatus.dart';
import '../../../../Models/Content/CancellationPolicy.dart';
import '../../../../Models/Content/Curriculum.dart';
import '../../../../Models/Content/Degree.dart';
import '../../../../Models/Content/Gender.dart';
import '../../../../Models/Content/InstructorHelpReason.dart';
import '../../../../Models/Content/Nationality.dart';
import '../../../../Models/Content/PackageCancelReason.dart';
import '../../../../Models/Content/PayMeMethod.dart';
import '../../../../Models/Content/PaymentMethod.dart';
import '../../../../Models/Content/PaymentReason.dart';
import '../../../../Models/Content/TeachingGender.dart';
import '../../../../Models/Content/TimeZone.dart';
import '../../../../core/utilities/language/LanguagesKeys.dart';
import '../../../../core/utilities/language/LocalizationProviderKeys.dart';

class LookupsProvider implements LocalDataProvider {
  static const ASSET_FILE_PATH = 'assets/content/lookups.json';
  static const FILE_PATH = 'lookups.json';
  late SharedPreferences _sharedPreferences;
  Map? _data;

  initialize() async {
    await _initSharedPreferences();
    await readFile();
  }

  _initSharedPreferences() async {
    _sharedPreferences = await SharedPreferences.getInstance();
  }

  @override
  readFile() async {
    final directory = await getApplicationDocumentsDirectory();
    final exists = await File('${directory.path}/$FILE_PATH').exists();
    final file = File('${directory.path}/$FILE_PATH');
    if (exists) {
      final dataAsString = await file.readAsString();
      _data = jsonDecode(dataAsString);
    } else {
      final dataAsString = await rootBundle.loadString(ASSET_FILE_PATH);
      await file.writeAsString(dataAsString);
      _data = jsonDecode(dataAsString);
    }
  }

  @override
  getAll() {
    return _data;
  }

  @override
  getWithKey(key) {
    final userLang =
        _sharedPreferences.containsKey(SharedPreferencesKeys.LANGUAGE)
            ? _sharedPreferences.getString(SharedPreferencesKeys.LANGUAGE)
            : LanguagesKeys.ARABIC;
    switch (key) {
      case GENDER:
        return _data![key].map((obj) => genderFromJson(obj, userLang)).toList();
      case NATIONALITY:
        return _data![key]
            .map((obj) => nationalityFromJson(obj, userLang))
            .toList();
      case TIMEZONE:
        return _data![key]
            .map((obj) => timeZoneFromJson(obj, userLang))
            .toList();
      case DEGREE:
        return _data![key].map((obj) => degreeFromJson(obj, userLang)).toList();

      case CANCELATIONPOLICY:
        return _data![key]
            .map((obj) => cancellationPolicyFromJson(obj, userLang))
            .toList();

      case BOOKINGPAYMENTSTATUS:
        return _data![key]
            .map((obj) => bookingPaymentStatusFromJson(obj, userLang))
            .toList();
      case studentExternalSource:
        return _data?[key] != null
            ? (_data?[key]
                .map((obj) => studentExtrnalSourceFromJson(obj, userLang))
                .toList())
            : <StudentExtrnalSource>[];

      case PAYMENTMETHODS:
        return _data![key]
            .map((obj) => paymentMethodFromJson(obj, userLang))
            .toList();

      case PAYMENTREASON:
        return _data![key]
            .map((obj) => paymentReasonFromJson(obj, userLang))
            .toList();

      case PAYMEMETHOD:
        return _data![key]
            .map((obj) => payMeMethodFromJson(obj, userLang))
            .toList();

      case APPSTOREURLS:
        return _data![key]
            .map((obj) => appStoreUrlFromJson(obj, userLang))
            .toList();

      case TEACHINGGENDER:
        return _data![key]
            .map((obj) => teachingGenderFromJson(obj, userLang))
            .toList();

      case TRAINEETYPE:
        return _data![key]
            .map((obj) => traineeTypesFromJson(obj, userLang))
            .toList();

      case CURRICULUMS:
        return _data![key]
            .map((obj) => curriculumsFromJson(obj, userLang))
            .toList();
      case PACKAGECANCELREASONS:
        return _data![key]
            .map((obj) => packageCancelReasonsFromJson(obj, userLang))
            .toList();
      case INSTRUCTORHELPREASONS:
        return _data![key]
            .map((obj) => instructorHelpReasonsFromJson(obj, userLang))
            .toList();
      default:
    }
  }
}
