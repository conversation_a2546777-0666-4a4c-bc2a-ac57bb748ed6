import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../Enums/SharedPreferencesKeys.dart';
import '../../../../Models/Content/Currency.dart';
import 'LocalDataProvider.dart';

class CurrenciesProvider implements LocalDataProvider {
  static const ASSET_FILE_PATH = 'assets/content/currencies.json';
  static const FILE_PATH = 'currencies.json';
  late SharedPreferences _sharedPreferences;
  List? _data;

  initialize() async {
    await _initSharedPreferences();
    await readFile();
  }

  _initSharedPreferences() async {
    _sharedPreferences = await SharedPreferences.getInstance();
  }

  @override
  readFile() async {
    final directory = await getApplicationDocumentsDirectory();
    final exists = await File('${directory.path}/$FILE_PATH').exists();
    final file = File('${directory.path}/$FILE_PATH');
    if (exists) {
      final dataAsString = await file.readAsString();
      _data = jsonDecode(dataAsString);
    } else {
      final dataAsString = await rootBundle.loadString(ASSET_FILE_PATH);
      await file.writeAsString(dataAsString);
      _data = jsonDecode(dataAsString);
    }
  }

  @override
  getAll() {
    final userLang =
        _sharedPreferences.getString(SharedPreferencesKeys.LANGUAGE);
    return _data!.map((obj) => currencyFromJson(obj, userLang)).toList();
  }

  @override
  getWithKey(key) {
    final userLang =
        _sharedPreferences.getString(SharedPreferencesKeys.LANGUAGE);
    for (var obj in _data ?? []) {
      if (obj['k'] == key) return currencyFromJson(obj, userLang);
    }
  }

  getJsonWithKey(key) {
    for (var obj in _data ?? []) {
      if (obj['k'] == key) return obj;
    }
  }

  getWithCountryCode(code) {
    final userLang =
        _sharedPreferences.getString(SharedPreferencesKeys.LANGUAGE);
    for (var obj in _data ?? []) {
      for (String country in obj['CountriesCode']) {
        if (country == code) return currencyFromJson(obj, userLang);
      }
    }
  }
}
