import 'dart:convert';
import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter_app_badge_control/flutter_app_badge_control.dart';
import 'package:modarby/Enums/NotificationTypes.dart';
import 'package:modarby/Models/InstructorProfile/GetProfileDTO.dart';
import 'package:modarby/Models/voice_call/VoiceCallSetting.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/InstructorProfileProvider.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:modarby/core/extentions/iterables/iterable_compact_map.dart';
import 'package:modarby/core/extentions/notifcation_type_extention.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/notifications/firebase_messaging_impl.dart';
import 'package:modarby/core/page_loading_dialog/page_loading_dialog.dart';
import 'package:modarby/core/utilities/error_parser.dart';
import 'package:modarby/core/utilities/shard.dart';
import 'package:modarby/core/utilities/storage.dart';
import 'package:provider/provider.dart';

import '../Models/InstructorDashboard/AddReferralStudent.dart';
import '../Models/InstructorDashboard/Balance.dart';
import '../Models/InstructorDashboard/BillingInfo.dart';
import '../Models/InstructorDashboard/Lesson.dart';
import '../Models/InstructorDashboard/Package.dart' as pakage;
import '../Models/InstructorDashboard/Payment.dart';
import '../Models/InstructorDashboard/ReferralStudents.dart';
import '../Models/InstructorDashboard/ReferralTransactions.dart';
import '../Models/InstructorDashboard/UpdateStudentInfoDTO.dart';
import '../Models/NotificationsHistoryModel.dart';
import '../Services/InstructorDashboard/InstructorDashboardRepository.dart';
import '../core/utilities/Snackbars.dart';

class InstructorDashboardProvider with ChangeNotifier {
  late InstructorDashboardRepository _dashboardRepository;

  /// filters
  DateTime? fromDateReferralTransaction;
  DateTime? toDateReferralTransaction;

  DateTime? fromDatePaymentHistory;
  DateTime? toDatePaymentHistory;

  DateTime? fromDateLessons;
  DateTime? toDateLessons;

  DateTime? fromDatePackages;
  DateTime? toDatePackages;

  DateTime? fromDateBalance;
  DateTime? toDateBalance;

  /// end filters
  List<InstructorLesson>? _lessons;
  List<InstructorLesson>? _comingLessons;
  InstructorBalance? _balance;
  List<InstructorPayment>? _payments;
  BillingInfo? _billingInfo;
  List<pakage.Package>? _packages;
  List<ReferralStudent>? _referralStudentsList;
  ReferralTransactions? _referralTransactions;
  List<Notifications> listNotification = List.empty(growable: true);
  VoiceCallSetting? _voiceCallSetting;
  bool isLoadingPagination = false;
  VoiceCallSetting? get voiceCallSetting => _voiceCallSetting;
  List<InstructorLesson>? get lessons => _lessons;
  List<InstructorLesson>? get comingLessons => _comingLessons;
  InstructorBalance? get balance => _balance;
  List<InstructorPayment>? get payments => _payments;
  BillingInfo? get billingInfo => _billingInfo;
  List<pakage.Package>? get packages => _packages;
  List<ReferralStudent>? get referralStudent => _referralStudentsList;
  ReferralTransactions? get referralTransactions => _referralTransactions;
  bool canLoadMore = true;

  InstructorDashboardProvider() {
    _dashboardRepository = InstructorDashboardRepository();
  }

  Future<List<InstructorLesson>?> getLessons(
    String? accessToken,
    int? languageId, {
    String? fromDate,
    String? toDate,
  }) async {
    try {
      final lessons = await _dashboardRepository.getLessons(
        accessToken,
        languageId,
        fromDate: fromDate ?? '',
        toDate: toDate ?? '',
      );
      _lessons = lessons;
    } catch (error) {
      _lessons = [];
    }
    notifyListeners();
    return _lessons;
  }

  Future<void> setVoiceSetting({
    VoiceCallSetting? voiceSetting,
  }) async {
    final accessToken = Shard().accessToken;
    if (accessToken == null) return;
    final role = getIt<Storage>().role;
    final isStudent = StaticVar.isStudent;
    final loader = getIt<IPageLoadingDialog>().showLoadingDialog();
    try {
      _voiceCallSetting = voiceSetting;
      notifyListeners();
      if (isStudent) {
        await _dashboardRepository.setStudentVoiceSetting(
          accessToken,
          voiceSetting?.toJson() ?? {},
        );
      } else {
        await _dashboardRepository.setInstructorVoiceSetting(
          accessToken,
          voiceSetting?.toJson() ?? {},
        );
      }
      loader.hide();
    } catch (error) {
      loader.hide();
      final errorException = ErrorParser().parseError(error);
      debugPrint(errorException.message);
    }

    notifyListeners();
  }

  Future<void> getVoiceSetting() async {
    final accessToken = Shard().accessToken;
    if (accessToken == null) return;
    final role = getIt<Storage>().role;
    final isStudent = StaticVar.isStudent;
    try {
      if (isStudent) {
        final setting = await _dashboardRepository.getStudentVoiceSetting(
          accessToken,
        );
        _voiceCallSetting = setting;
        notifyListeners();
      } else {
        final setting = await _dashboardRepository.getInstructorVoiceSetting(
          accessToken,
        );
        _voiceCallSetting = setting;
        notifyListeners();
      }
    } catch (error) {
      final errorException = ErrorParser().parseError(error);
      debugPrint(errorException.message);

      _voiceCallSetting = VoiceCallSetting(
        isVoiceCallAllowed: true,
        voiceCallAllowedFor: 1,
      );
      notifyListeners();
    }
  }

  Future<List<InstructorLesson>?> getComingLessons(
    String? accessToken,
    int? languageId, {
    String? fromDate,
    String? toDate,
  }) async {
    try {
      final lessons = await _dashboardRepository.getComingLessons(
        accessToken,
        languageId,
        fromDate: fromDate ?? '',
        toDate: toDate ?? '',
      );
      _comingLessons = lessons;
    } catch (error) {
      _comingLessons = [];
    }
    notifyListeners();
    return _comingLessons;
  }

  Future<InstructorBalance?> getMyBalance(
    String? accessToken,
    int? languageId,
    int? currencyId, {
    String? fromDate,
    String? toDate,
  }) async {
    try {
      final balance = await _dashboardRepository.getMyBalance(
        accessToken,
        languageId,
        currencyId,
        fromDate: fromDate ?? '',
        toDate: toDate ?? '',
      );
      _balance = balance;
      notifyListeners();
      return _balance;
    } catch (error) {
      return null;
    }
  }

  Future<List<InstructorPayment>?> getPaymentsHistory(
    String? accessToken,
    int? languageId, {
    String? fromDate,
    String? toDate,
  }) async {
    try {
      final payments = await _dashboardRepository.getPaymentsHistory(
        accessToken,
        languageId,
        fromDate: fromDate ?? '',
        toDate: toDate ?? '',
      );
      _payments = payments;
      notifyListeners();
      return _payments;
    } catch (error) {
      return [];
    }
  }

  Future<bool?> deactivateAccount(
      String? accessToken, int? mode, int? languageId) async {
    try {
      await _dashboardRepository.deactivateAccount(accessToken, mode);
      await getInstructor(accessToken);
      return true;
    } catch (error) {
      return null;
    }
  }

  Future<bool> cancelLesson(
      String? accessToken, String? bookingGuid, int? languageId) async {
    try {
      await _dashboardRepository.cancelLesson(
          accessToken, bookingGuid, languageId);
      await this._refreshData(accessToken, languageId);
      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> noShowLesson(
      String? accessToken, String? bookingGuid, int? languageId) async {
    try {
      await _dashboardRepository.noShowLesson(
          accessToken, bookingGuid, languageId);
      await this._refreshData(accessToken, languageId);
      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<bool?> deliveredLesson(
      String? accessToken, String? bookingGuid, int? languageId) async {
    try {
      final needUpdate = await _dashboardRepository.deliveredLesson(
          accessToken, bookingGuid, languageId);
      await this._refreshData(accessToken, languageId);
      return needUpdate;
    } catch (error) {
      rethrow;
    }
  }

  Future<void> _refreshData(String? accessToken, int? languageId) async {
    await this.getComingLessons(accessToken, languageId);
    await this.getLessons(accessToken, languageId);
  }

  Future<bool> rescheduleLesson(
      String accessToken,
      String bookingGuid,
      String userGuid,
      String date,
      int dayTimeId,
      int timeZoneId,
      String notes,
      int languageId) async {
    try {
      await _dashboardRepository.rescheduleLesson(accessToken, bookingGuid,
          userGuid, date, dayTimeId, timeZoneId, notes, languageId);
      await this.getComingLessons(accessToken, languageId);
      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> payMe(String? accessToken, int? currencyId, int? paymentMethodId,
      int? languageId) async {
    try {
      await _dashboardRepository.payMe(
          accessToken, currencyId, paymentMethodId, languageId);
      await this.getMyBalance(accessToken, languageId, currencyId);
      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<BillingInfo?> getBillingInfo(
      String? accessToken, int? languageId) async {
    try {
      final billingInfo =
          await _dashboardRepository.getBillingInfo(accessToken, languageId);
      _billingInfo = billingInfo;
      notifyListeners();
      return _billingInfo;
    } catch (error) {
      _billingInfo = null;
      return _billingInfo;
    }
  }

  Future<bool> updateBillingInfo(
      String? accessToken, BillingInfo billingInfo, int? languageId) async {
    try {
      await _dashboardRepository.updateBillingInfo(
          accessToken, billingInfo, languageId);
      await getBillingInfo(accessToken, languageId);
      await getInstructor(accessToken);
      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> updateCancellationPolicy(
      String? accessToken, int? policyId, int? languageId) async {
    try {
      await _dashboardRepository.updateCancellationPolicy(
          accessToken, policyId);
      await getInstructor(accessToken);
      notifyListeners();
      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<pakage.Package>?> getBookedPackages(
    String? accessToken,
    int? languageId, {
    String? fromDate,
    String? toDate,
  }) async {
    try {
      final packages = await _dashboardRepository.getBookedPackages(
        accessToken,
        languageId,
        fromDate: fromDate ?? '',
        toDate: toDate ?? '',
      );
      _packages = packages;
    } catch (error) {
      _packages = [];
    }
    notifyListeners();
    return _packages;
  }

  Future<bool> cancelPackage(String? accessToken, String? packageGuid,
      int? languageId, String reason) async {
    try {
      await _dashboardRepository.cancelPackage(
          accessToken, packageGuid, reason);
      await this.getBookedPackages(accessToken, languageId);
      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> updateStudentInfo(UpdateStudentInfoDTO dto) async {
    try {
      await _dashboardRepository.updateStudentInfo(dto);
      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<String> addReferralStudent(
      AddReferralStudent addReferralStudent) async {
    try {
      final res =
          await _dashboardRepository.addReferralStudent(addReferralStudent);
      return res;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<ReferralStudent>?> getReferralStudents(
      String? accessToken, int? languageId) async {
    try {
      final referralStudent = await _dashboardRepository.getReferralStudents(
          accessToken, languageId);
      _referralStudentsList = referralStudent;
      notifyListeners();
      return _referralStudentsList;
    } catch (error) {
      return [];
    }
  }

  Future<bool> removeReferralStudent(String? accessToken, int? id) async {
    try {
      await _dashboardRepository.removeReferralStudent(id, accessToken);
      return true;
    } catch (error) {
      rethrow;
    }
  }

  Future<ReferralTransactions?> getReferralTransactions(
    String? accessToken,
    int? languageId,
    int? currencyId, {
    String? fromDate,
    String? toDate,
  }) async {
    try {
      final refTransactions =
          await _dashboardRepository.getReferralTransactions(
        accessToken,
        languageId,
        currencyId,
        fromDate: fromDate ?? '',
        toDate: toDate ?? '',
      );
      _referralTransactions = refTransactions;
      notifyListeners();
      return _referralTransactions;
    } catch (error) {
      _referralTransactions = null;
      return _referralTransactions;
    }
  }

  int numberUnreadCount = 0;
  bool isLoadingRequest = false;

  Future<void> getUnReadNotification() async {
    final accessToken =
        StaticVar.context.read<AuthenticationProvider>().accessToken;
    if (accessToken == null) {
      return;
    }
    try {
      final myNotifications = await _dashboardRepository.getMyNotifications(
        accessToken,
        0,
        5,
      );
      numberUnreadCount = myNotifications.numberOfUnread ?? 0;
      _updateBadge(numberUnreadCount);
      notifyListeners();
    } catch (error) {
      log(error.toString());
    }
  }

  Future<void> getMyNotifications(
    String? accessToken,
    int? pageIndex,
    int pageSize, {
    bool firstRequest = false,
  }) async {
    try {
      if (firstRequest) {
        isLoadingRequest = true;
      }
      if (pageIndex == 0) {
        canLoadMore = true;
        listNotification = List.empty(growable: true);
      } else {
        isLoadingPagination = true;
      }
      notifyListeners();
      final myNotifications = await _dashboardRepository.getMyNotifications(
        accessToken,
        pageIndex,
        pageSize,
      );
      numberUnreadCount = myNotifications.numberOfUnread ?? 0;
      _updateBadge(numberUnreadCount);
      canLoadMore = myNotifications.notifications.isNotEmpty;
      notifyListeners();
      if (pageIndex == 0) {
        listNotification = myNotifications.notifications.noneNullList();
      } else {
        listNotification.addAll(myNotifications.notifications);
      }
      isLoadingRequest = false;
      notifyListeners();
      isLoadingPagination = false;

      notifyListeners();
      await Future.delayed(const Duration(milliseconds: 300));
      notifyListeners();
    } catch (error) {
      log(error.toString());
    }
  }

  Future<void> readAllNotifications() async {
    final accessToken =
        StaticVar.context.read<AuthenticationProvider>().accessToken;
    final loader = getIt<IPageLoadingDialog>().showLoadingDialog();
    try {
      await _dashboardRepository.readAllNotifications(
        accessToken,
      );
      updateReadAllNotificationLocal();
      loader.hide();
    } catch (error) {
      loader.hide();
      final errorException = ErrorParser().parseError(error);
      Snackbars.danger(StaticVar.context, errorException.message);
    }
  }

  Future<bool> displayEmergencyAnnouncment(String? accessToken) async {
    try {
      await _dashboardRepository.displayEmergencyAnnouncment(accessToken);
      return true;
    } catch (error) {
      rethrow;
    }
  }

  void logout() {
    _lessons = null;
    _comingLessons = null;
    _balance = null;
    _payments = null;
    _billingInfo = null;
    _packages = null;
    _referralStudentsList = null;
    _referralTransactions = null;
    listNotification = [];
    notifyListeners();
  }

  void updateReadAllNotificationLocal() {
    listNotification.map((e) => e.seen = true).toList();
    numberUnreadCount = 0;
    _removeAllBadge();
    notifyListeners();
  }

  Future<void> readSingleNotification(
      BuildContext context, Notifications? notificationsHistoryModel) async {
    final accessToken =
        StaticVar.context.read<AuthenticationProvider>().accessToken;
    if ((notificationsHistoryModel?.seen ?? false).inverted) {
      final loader = getIt<IPageLoadingDialog>().showLoadingDialog();
      try {
        await _dashboardRepository.readNotifications(
          [notificationsHistoryModel?.id ?? 0],
          accessToken,
        );
        loader.hide();
        _handelSeen(notificationsHistoryModel);
        notifyListeners();
        _redirectionUser(notificationsHistoryModel);
      } catch (error) {
        loader.hide();
        final errorException = ErrorParser().parseError(error);
        Snackbars.danger(context, errorException.message);
      }
    } else {
      _handelSeen(notificationsHistoryModel);
      _redirectionUser(notificationsHistoryModel);
    }
  }

  void _redirectionUser(Notifications? notificationsHistoryModel) {
    if (notificationsHistoryModel?.additionalData != null &&
        notificationsHistoryModel?.additionalData.isNotEmpty) {
      FireBaseMessagingImpl.fireBaseMessagingImpl
          .handleNotificationTypesFromLogs(
              json.decode(notificationsHistoryModel?.additionalData),
              type: notificationsHistoryModel?.typeId?.type ??
                  NotificationTypes.none);
    } else {
      FireBaseMessagingImpl.fireBaseMessagingImpl
          .handleNotificationTypesFromLogs(null,
              type: notificationsHistoryModel?.typeId?.type ??
                  NotificationTypes.none);
    }
  }

  void readNotifcationLocal(int? id) {
    final index = listNotification.indexWhere((element) => element.id == id);
    if (index != -1) {
      listNotification[index].seen = true;
    }
    notifyListeners();
  }

  void clear() {
    listNotification = [];
    notifyListeners();
  }

  void _handelSeen(Notifications? notificationsHistoryModel) {
    final index = listNotification
        .indexWhere((element) => element.id == notificationsHistoryModel?.id);
    if (index != -1) {
      listNotification[index].seen = true;
      notifyListeners();
      getUnReadNotification();
      notifyListeners();
    }
  }

  void clearNotification() {
    numberUnreadCount = 0;
    listNotification = [];
  }

  void _removeAllBadge() {
    FlutterAppBadgeControl.isAppBadgeSupported().then((value) {
      FlutterAppBadgeControl.updateBadgeCount(0);
    });
  }

  void _updateBadge(int numberUnreadCount) {
    FlutterAppBadgeControl.updateBadgeCount(numberUnreadCount);
  }

  Future<void> getInstructor(String? accessToken) async {
    await StaticVar.context.read<InstructorProfileProvider>().getProfile(
          GetProfileDTO(
              accessToken: accessToken,
              languageId: Shard().languageId,
              currencyId: Shard().currencyId),
        );
  }
}
