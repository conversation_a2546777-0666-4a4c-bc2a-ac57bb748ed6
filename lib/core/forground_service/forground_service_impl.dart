import 'dart:async';
import 'dart:io';

import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';

class ForegroundServiceImpl {
  static String incrementTimer(String currentTime) {
    // Split the time string into minutes and seconds
    List<String> parts = currentTime.split(':');
    int minutes = int.parse(parts[0]);
    int seconds = int.parse(parts[1]);

    // Increment seconds
    seconds++;

    // Handle minute rollover
    if (seconds >= 60) {
      seconds = 0;
      minutes++;
    }

    // Format the time with leading zeros
    String formattedMinutes = minutes.toString().padLeft(2, '0');
    String formattedSeconds = seconds.toString().padLeft(2, '0');

    return '$formattedMinutes:$formattedSeconds';
  }

  Future<void> startBackgroundService() async {
    if (Platform.isAndroid.inverted) return;
    final service = FlutterBackgroundService();
    final isRunning = await service.isRunning();
    if (isRunning) {
      service.invoke('stopService');
      service.startService();
    } else {
      service.startService();
    }
  }

  Future<void> stopBackgroundService() async {
    if (Platform.isAndroid.inverted) return;
    final service = FlutterBackgroundService();
    service.invoke("stopService");
  }
}
