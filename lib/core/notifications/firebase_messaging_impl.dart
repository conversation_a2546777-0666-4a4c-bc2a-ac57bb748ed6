import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io' as io;
import 'dart:io';

import 'package:app_settings/app_settings.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_callkit_incoming/entities/entities.dart';
import 'package:flutter_callkit_incoming/flutter_callkit_incoming.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:modarby/Enums/NotificationTypes.dart';
import 'package:modarby/Enums/Roles.dart';
import 'package:modarby/Instructor/Screens/Instructor_home.dart';
import 'package:modarby/Models/Messages/ConversationModel.dart';
import 'package:modarby/Models/Search/SearchProfileDTO.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/CollectDataProvider.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/InstructorDashboardProvider.dart';
import 'package:modarby/Providers/SearchProvider.dart';
import 'package:modarby/Student/Screens/Home.dart';
import 'package:modarby/Student/Screens/MyPackages.dart';
import 'package:modarby/Widgets/ConfirmationDialog.dart';
import 'package:modarby/Widgets/NotificationFullScreen.dart';
import 'package:modarby/Widgets/NotificationPopUp.dart';
import 'package:modarby/core/Utilities/ProgressIndicators.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/notifications/i_firebase_messaging.dart';
import 'package:modarby/core/page_loading_dialog/page_loading_dialog.dart';
import 'package:modarby/core/utilities/DeepLinking.dart';
import 'package:modarby/core/utilities/Snackbars.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';
import 'package:modarby/core/utilities/navigation_service.dart';
import 'package:modarby/core/utilities/shard.dart';
import 'package:modarby/core/utilities/storage.dart';
import 'package:modarby/features/live_tutors/tutors/presentation/screens/live_notifications_tutor_screen.dart';
import 'package:modarby/features/messages/domain/enum/conversation_place.dart';
import 'package:modarby/features/messages/presentation/arguments/conversation_argument.dart';
import 'package:modarby/features/messages/presentation/views/conversation_screen.dart';
import 'package:modarby/features/voice_call/data/call_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:modarby/core/utilities/permition_handler.dart';

import '../../Providers/InstructorProfileProvider.dart';
import '../../Providers/MessagesProvider.dart';
import '../../Providers/emergency_settings_provider.dart';
import '../../features/booking_page/ModarbBooking.dart';
import '../../features/booking_page/instrucor_arguments.dart';

class FireBaseMessagingImpl extends IFirebaseMessaging {
  // * Data Keys
  static const String _TYPE_KEY = 'type';

  // * Notification Types
  static const String _MESSAGE_NOTIFICATION = 'Message';
  static const String _BOOKING_NOTIFICATION = 'Booking';
  static const String _PACKAGE_NOTIFICATION = 'Package';
  static const String instructorAvailable = 'EmergencyHelp';
  static const String voiceCall = 'VoiceCall';

  static const String emergencyHelpReminder = 'EmergencyHelpReminder';

  static const String _INSTRUCTOR_REVIEW_NOTIFICATION = 'InstructorReview';
  static const String _BROWSER_NOTIFICATION = 'Browser';
  static const String _URL_NOTIFICATION = 'NotificationWithTargetUrl';

  // * New Message Keys
  static const String _USER_ID_KEY = 'userId';
  static const String _USER_PIC_KEY = 'userPic';
  static const String _USER_NAME_KEY = 'userName';

  // * Browser Keys
  static const String _BROWSER_URL_KEY = 'url';

  // * URL Keys
  static const String _URL_TARGET_KEY = 'targetUrl';
  static const String _URL_IS_POP_UP_KEY = 'isPopup';
  static const String _requestId = 'requestid';

  static List<String> visited = [];

  // ================

  FireBaseMessagingImpl._();

  static FireBaseMessagingImpl fireBaseMessagingImpl =
      FireBaseMessagingImpl._();
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  @override
  Future<void> configureHuaweiMessaging() async {
    await Future.wait([
      requestIOSPermissions(),
      setUpFlutterLocalNotification(),
      setUpForGroundNotification(),
      getTokenFirebase(),
      handleBackGroundNotifications(),
      handleForGroundNotifications(),
      onChangedToken(),
    ]);
  }

  @override
  Future<String> getTokenFirebase() async {
    final token = await _firebaseMessaging.getToken() ?? '';
    final apnstoken = await _firebaseMessaging.getAPNSToken() ?? '';
    print('apnstoken:$apnstoken');
    saveTokenFirebase(token);
    return token;
  }

  @override
  Future<void> handleBackGroundNotifications() async {
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
      // redirect user to specific page
      _handleNotificationTypes(message.data);
    });
  }

  String removeFileExtension(String filePath) {
    int dotIndex = filePath.lastIndexOf('.');
    if (dotIndex != -1) {
      return filePath.substring(0, dotIndex);
    }
    return filePath; // Return the original path if no dot is found
  }

  @override
  Future<void> handleForGroundNotifications() async {
    String channelName = '';
    FirebaseMessaging.onMessage.listen((RemoteMessage? message) async {
      final RemoteNotification? notification = message?.notification;
      final AndroidNotification? android = message?.notification?.android;
      final data = message?.data ?? {};
      if (data.containsKey(_TYPE_KEY) &&
          data[_TYPE_KEY] == instructorAvailable) {
        _handleInstructorAvailable(data[_requestId], false);
      } else if (data.containsKey(_TYPE_KEY) && data[_TYPE_KEY] == voiceCall) {
        final channelNameNew = data['ChannelName'] ?? '';
        if (channelNameNew != channelName) {
          channelName = channelNameNew;
          final provider = getIt<CallProvider>();
          final callExists = await provider.checkIfCallExists(channelName);
          if (channelName != provider.callArgument?.channelName && callExists) {
            showCallkitIncoming(Map<String, dynamic>.from(data));
          }
        }
      }

      // If `onMessage` is triggered with a notification, construct our own
      // local notification to show to users using the created channel.
      if (notification != null &&
          android != null &&
          ((data[_TYPE_KEY] == voiceCall).inverted)) {
        final channel = getAndroidChannel();
        flutterLocalNotificationsPlugin.show(
          notification.hashCode,
          notification.title,
          notification.body,
          NotificationDetails(
            android: AndroidNotificationDetails(
              channel.id,
              channel.name,
              playSound: true,
              icon: '@mipmap/launcher_icon',
              priority: Priority.high,
              importance: Importance.max,
              sound: const RawResourceAndroidNotificationSound(
                  'announcement_sound'),
            ),
          ),
          payload: json.encode(message?.data),
        );
      }
      _handleGetDataRequests(message?.data ?? {});
    });
  }

  @override
  Future<void> onChangedToken() async {
    _firebaseMessaging.onTokenRefresh.listen(sendFcmToken);
  }

  @override
  Future<void> saveTokenFirebase(String? token) async {
    if ((token ?? '').isNotEmpty) {
      StaticVar.firebaseToken = token ?? '';
    }
  }

  @override
  AndroidNotificationChannel getAndroidChannel({String? sound}) {
    return const AndroidNotificationChannel(
      'high_importance_channel',
      'modarby High Importance Notifications',
      description: 'This channel is used for important notifications. 1',
      importance: Importance.high,
      playSound: true,
      sound: RawResourceAndroidNotificationSound('announcement_sound'),
    );
  }

  @override
  Future<void> setUpFlutterLocalNotification() async {
    final initializationSettingsIOS = DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
      onDidReceiveLocalNotification: (id, title, body, payload) async {
        handleLocalNotification(payload);
      },
    );

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/launcher_icon');

    final initializationSettings = InitializationSettings(
      iOS: initializationSettingsIOS,
      android: initializationSettingsAndroid,
    );
    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (notificationResponse) async {
        log('onDidReceiveNotificationResponse${notificationResponse.payload}');
        handleLocalNotification(notificationResponse.payload);
        _handleGetDataRequests(notificationResponse.payload);
      },
      onDidReceiveBackgroundNotificationResponse: (notificationResponse) async {
        log('onDidReceiveBackgroundNotificationResponse${notificationResponse.payload}');
        handleLocalNotification(notificationResponse.payload);
        _handleGetDataRequests(notificationResponse.payload);
      },
    );
  }

  @override
  Future<void> setUpForGroundNotification() async {
    await _firebaseMessaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
  }

  @override
  Future<void> requestIOSPermissions() async {
    if (io.Platform.isAndroid) {
      // Create multiple channels
      const AndroidNotificationChannel channel1 = AndroidNotificationChannel(
        'high_importance_channel',
        'modarby High Importance Notifications',
        description: 'This channel is used for important notifications.',
        importance: Importance.high,
        playSound: true,
        sound: RawResourceAndroidNotificationSound('announcement_sound'),
      );
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel1);
    } else {
      flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: false,
            badge: false,
            sound: false,
          );
    }
  }

  void handleLocalNotification(dynamic payload) {
    if (payload != null && payload.isNotEmpty) {
      try {
        final data = json.decode(payload);
        _handleNotificationTypes(data);
      } catch (e) {
        log(e.toString());
      }
    }
  }

  @override
  Future<void> sendFcmToken(String? token) async {
    try {
      if (token != null && token.isNotEmpty) {
        _checkUserLogInOrNotAndSendTokenToServer(token);
      }
    } catch (e) {
      log(e.toString());
    }
  }

  static _handleGetDataRequests(dynamic payload) async {
    final accessToken =
        StaticVar.context.read<AuthenticationProvider>().accessToken;
    final instructorDashboardProvider =
        StaticVar.context.read<InstructorDashboardProvider>();
    instructorDashboardProvider.getMyNotifications(
      accessToken,
      0,
      15,
    );
    if (payload != null &&
        payload is Map<String, dynamic> &&
        payload.containsKey(_TYPE_KEY)) {
      if (payload[_TYPE_KEY] == _MESSAGE_NOTIFICATION) {
        final messagesProvider = StaticVar.context.read<MessagesProvider>();
        messagesProvider.getUserChat(accessToken, 1);

        if (StaticVar.routeScreen == ConversationScreen.routeName) {
          messagesProvider.getConversationMessages(
            accessToken,
            payload[_USER_ID_KEY],
            StaticVar.context.read<AuthenticationProvider>(),
            pageSize: 15,
            pageIndex: 1,
          );
        }
      }
    }
  }

  void handleNotificationTypesFromLogs(
    Map<dynamic, dynamic>? data, {
    required NotificationTypes type,
  }) {
    log('_handleNotificationTypes$data');
    final role = getIt<Storage>().role;

    switch (type) {
      case NotificationTypes.message:
        _handleNewMessageSelect(
            data?[_USER_ID_KEY], data?[_USER_NAME_KEY], data?[_USER_PIC_KEY]);
        break;
      case NotificationTypes.booking:
        _handleBookingSelect();
        break;
      case NotificationTypes.package:
        _handlePackageSelect();
        break;
      case NotificationTypes.browser:
        _handleBrowserSelect(data?[_BROWSER_URL_KEY]);
        break;
      case NotificationTypes.popUp:
        _handleURLSelect(data?[_URL_TARGET_KEY], data?[_URL_IS_POP_UP_KEY]);
        break;
      case NotificationTypes.instructorReview:
        if (role == Roles.INSTRUCTOR) _handleInstructorReview(data ?? {});
        break;
      default:
    }
  }

  void _handleNotificationTypes(Map<dynamic, dynamic> data) {
    log('_handleNotificationTypes$data');
    final logInUser =
        StaticVar.context.read<AuthenticationProvider>().isLoggedIn;
    switch (data[_TYPE_KEY]) {
      case _MESSAGE_NOTIFICATION || NotificationTypes.message:
        if (logInUser) {
          _handleNewMessageSelect(
              data[_USER_ID_KEY], data[_USER_NAME_KEY], data[_USER_PIC_KEY]);
        }
        break;
      case _BOOKING_NOTIFICATION:
        _handleBookingSelect();
        break;
      case _PACKAGE_NOTIFICATION:
        _handlePackageSelect();
        break;
      case voiceCall:
        _checkCallExistsThenShowIncomingCall(Map<String, dynamic>.from(data));
        break;
      case instructorAvailable:
        _handleInstructorAvailable(
          data[_requestId],
          true,
        );
        break;
      case emergencyHelpReminder:
        _handleInstructorAvailable(
          data[_requestId],
          true,
        );
        break;
      case _INSTRUCTOR_REVIEW_NOTIFICATION:
        _handleInstructorReview(data);
        break;
      case _BROWSER_NOTIFICATION:
        _handleBrowserSelect(data[_BROWSER_URL_KEY]);
        break;
      case _URL_NOTIFICATION:
        _handleURLSelect(
            data[_URL_TARGET_KEY], data[_URL_IS_POP_UP_KEY] == 'true');
        break;
      default:
    }
  }

  static void _handleNewMessageSelect(
      String? userId, String? userName, String? userPhoto) async {
    final logInUser =
        StaticVar.context.read<AuthenticationProvider>().isLoggedIn;
    final logInUserIsTutor = (getIt<Storage>().role == Roles.INSTRUCTOR ||
        getIt<Storage>().role == Roles.REFERRAL);
    final instructor =
        StaticVar.context.read<InstructorProfileProvider>().instructor;

    if (logInUser) {
      final loader = getIt<IPageLoadingDialog>().showLoadingDialog();
      try {
        final conversation = _buildConversation(userId, userName, userPhoto);
        final messagesProvider = StaticVar.context.read<MessagesProvider>();
        messagesProvider.conversation = conversation;
        if (userId != null) {
          final argument = ConversationArgument(
            conversationType: ConversationType.anyPlace,
            callConversationMessages: false,
            userId: userId,
            userName: userName ?? '',
            userNameAr: conversation.userNameAr ?? '',
          );
          await Future.delayed(
            const Duration(seconds: 2),
          );
          loader.hide();
          if (logInUserIsTutor) {
            if (instructor?.isVerified ?? false) {
              NavigationService.instance.navigateToIfNotCurrent(
                  ConversationScreen.routeName,
                  args: argument);
            } else {
              return Navigator.pushNamedAndRemoveUntil<void>(
                StaticVar.context,
                InstructorHomeScreen.routeName,
                (route) => false,
                arguments: {'PAGE_INDEX': 4},
              );
            }
          } else {
            NavigationService.instance.navigateToIfNotCurrent(
                ConversationScreen.routeName,
                args: argument);
          }
        }
      } catch (error) {
        loader.hide();
        print(error);
      }
    }
  }

  static Conversation _buildConversation(
      String? userId, String? userName, String? userPhoto) {
    return Conversation(
      usersid: userId,
      userslist: userName,
      userspic: userPhoto,
      usersflag: null,
      counts: null,
      usersemail: null,
      usersprofile: null,
      lastMessage: null,
      lastMessageDate: null,
    );
  }

  static Future<void> _handleBookingSelect() async {
    try {
      final role = getIt<Storage>().role;
      if (StaticVar.isStudent) {
        return Navigator.pushNamedAndRemoveUntil<void>(
          StaticVar.context,
          StudentHomeScreen.routeName,
          (route) => false,
          arguments: {'PAGE_INDEX': 1},
        );
      }
      if (role == Roles.INSTRUCTOR || role == Roles.REFERRAL) {
        return Navigator.pushNamedAndRemoveUntil<void>(
          StaticVar.context,
          InstructorHomeScreen.routeName,
          (route) => false,
          arguments: {'PAGE_INDEX': 3},
        );
      }
    } catch (error) {
      print(error);
    }
  }

  static Future<void> _handlePackageSelect() async {
    try {
      final role = getIt<Storage>().role;
      if (StaticVar.isStudent) {
        return Navigator.pushNamed<void>(
          StaticVar.context,
          StudentMyPackagesScreen.routeName,
        );
      }
      if (role == Roles.INSTRUCTOR || role == Roles.REFERRAL) {
        return Navigator.pushNamedAndRemoveUntil<void>(
          StaticVar.context,
          InstructorHomeScreen.routeName,
          (route) => false,
          arguments: {'PAGE_INDEX': 2},
        );
      }
    } catch (error) {
      print(error);
    }
  }

  void _handleBrowserSelect(String? url) async {
    if (url == null || url.isEmpty) return;
    final uri = Uri.parse(url);
    DeepLinking.deepLinking.handleURI(uri);
  }

  static Future<void> _handleURLSelect(String? url, bool isPopUp) async {
    if (url == null || url.isEmpty) return;
    if (isPopUp) {
      return await showDialog(
        context: StaticVar.context,
        builder: (_) => NotificationPopUp(url: url),
      );
    }

    if (!visited.contains('NotificationFullScreen')) {
      visited.add('NotificationFullScreen');
      Navigator.push<void>(
        StaticVar.context,
        MaterialPageRoute(
            builder: (context) => NotificationFullScreen(url: url)),
      ).then((value) {
        visited = [];
      });
    }
  }

  @override
  Future<void> handleNotificationsWhenAppClosed() async {
    final message = await FirebaseMessaging.instance.getInitialMessage();
    log('handleNotificationsWhenAppClosed: ${message?.data}');
    await Future.delayed(const Duration(seconds: 3));
    if (message != null) {
      // Handle the message when the app is closed
      final data = message.data;
      log('handleNotificationsWhenAppClosed$data');

      _handleNotificationTypes(data);
    }
  }

  void _checkUserLogInOrNotAndSendTokenToServer(String token) {
    final collectDataProvider = StaticVar.context.read<CollectDataProvider>();
    collectDataProvider.checkUserLogInOrNotAndSendTokenToServer(token);
  }

  Future<void> _handleInstructorAvailable(
      String requestId, bool isFromClicked) async {
    if ((StaticVar.accessToken ?? '').isEmpty) return;
    final provider = StaticVar.context.read<EmergencySettingsProvider>();
    if (isFromClicked) {
      final result = await provider.checkStatusRequest(
          requestId: int.tryParse(requestId) ?? 0);
      if (result.success ?? false) {
        _checkRedirectionToLiveTutorsOrNot();
      } else {
        // showPopup live tutors
        _showConfirmationDialogForAnotherTutorAccepted(
            result.reason == "RequestExpired"
                ? ANOTHER_TUTOR_ACCEPTED_TITLE.translate()
                : result.reason == 'RequestMaxInstructorAccepted'
                    ? dialogTitleMaxInstructor.translate()
                    : ANOTHER_TUTOR_ACCEPTED_TITLE_CANCELED.translate(),
            result.reason == "RequestExpired"
                ? ANOTHER_TUTOR_ACCEPTED_BODY.translate()
                : result.reason == 'RequestMaxInstructorAccepted'
                    ? dialogBodyMaxInstructor.translate()
                    : ANOTHER_TUTOR_ACCEPTED_BODY_CANCELED.translate());
      }
    } else {
      _checkRedirectionToLiveTutorsOrNot();
    }
  }

  Future<bool> _showConfirmationDialogForAnotherTutorAccepted(
      String? title, String? body) async {
    final iconsProvider = StaticVar.context.read<DependencyManager>().icons;
    final isConfirmed = await showDialog(
        context: StaticVar.context,
        builder: (_) => FractionallySizedBox(
              widthFactor: 1.1,
              //heightFactor: 0.5,
              child: ConfirmationDialog(
                icon: iconsProvider.fastTime,
                title: title,
                height: 45,
                subtitle: body,
                hasEx: false,
                referral: true,
                hasCancel: false,
                confirmColor: ThemeColors.gray48484A,
                submitLabelColor: ThemeColors.gray48484A,
                submitColor: ThemeColors.grayE5E5EA,
                submitLabel: ANNOUNCEMENT_SKIP.translate(),
              ),
            ));
    return isConfirmed ?? false;
  }

  @override
  Future<void> deleteToken() async {
    StaticVar.firebaseToken = '';
    await fireBaseMessagingImpl._firebaseMessaging.deleteToken();
  }

  void _checkRedirectionToLiveTutorsOrNot() {
    final provider = StaticVar.context.read<EmergencySettingsProvider>();

    if (StaticVar.routeScreen != LiveNotificationsTutorScreen.routeName) {
      provider.getPendingRequestToStartTimer();
      Navigator.pushNamed<void>(
        StaticVar.context,
        LiveNotificationsTutorScreen.routeName,
        // arguments: true
      );
    } else {
      provider.getPendingRequest();
    }
  }

  void _handleInstructorReview(Map<dynamic, dynamic> data) {
    final isInstructor = data['ToUserType'] == 'Instructor';
    final userId = data['userId'];
    if (isInstructor) {
      _openProfile();
    } else {
      Shard().showRatingInstructor(userId);
    }
  }

  Future<void> _openProfile() async {
    try {
      ProgressIndicators.loadingDialog(StaticVar.context);
      final dto = await _generateProfileDTO();
      await StaticVar.context
          .read<SearchProvider>()
          .searchInstructorProfile(dto);
      Navigator.pop(StaticVar.context);
      Navigator.pushNamed(
        StaticVar.context,
        StudentModarbBookingScreen.routeName,
        arguments: InstructorArguments(
          doScrollToReview: true,
        ),
      );
    } catch (e) {
      Navigator.pop(StaticVar.context);
      log(e.toString());
    }
  }

  Future<SearchProfileDTO> _generateProfileDTO() async {
    final token = await Shard().getToken();
    return SearchProfileDTO(
      languageId: Shard().languageId,
      currencyId: Shard().currencyId,
      guid: StaticVar.context
          .read<InstructorProfileProvider>()
          .instructor
          ?.userId,
      timezone: StaticVar.context
          .read<InstructorProfileProvider>()
          .instructor
          ?.timezone,
      deviceToken: token,
    );
  }

  Future<void> showCallkitIncoming(
    Map<String, dynamic> data,
  ) async {
    if (data.containsKey('ChannelName').inverted) return;
    final shard = await SharedPreferences.getInstance();
    final id = shard.getString(
      'system_id',
    );
    if (id != null && id.isNotEmpty && Platform.isAndroid) {
      await FlutterCallkitIncoming.endCall(id);
    }
    final timeRing = shard.getInt('_timeRingCall') ?? 30;
    final senderName = data['SenderName'];
    final pictureUrl = data['PictureUrl'];
    final uuid = const Uuid().v4();
    await shard.setString('system_id', uuid);
    final params = CallKitParams(
      id: uuid,
      nameCaller: senderName,
      appName: 'Modarby',
      handle: '',
      type: 0,
      avatar: pictureUrl ?? '',
      duration: timeRing * 1000,
      missedCallNotification: const NotificationParams(
        showNotification: false,
        isShowCallback: false,
        subtitle: 'Missed call',
        callbackText: 'Call back',
      ),
      extra: data,
      headers: <String, dynamic>{'apiKey': 'Abc@123!', 'platform': 'flutter'},
      android: const AndroidParams(
        isCustomNotification: false,
        isShowLogo: true,
        ringtonePath: 'system_ringtone_default',
        backgroundColor: '#0955fa',
        actionColor: '#4CAF50',
        textColor: '#000000',
        isShowFullLockedScreen: true,
      ),
      ios: const IOSParams(
        iconName: 'CallKitLogo',
        handleType: '',
        supportsVideo: false,
        maximumCallGroups: 2,
        maximumCallsPerCallGroup: 1,
        audioSessionMode: 'default',
        audioSessionActive: true,
        audioSessionPreferredSampleRate: 44100.0,
        audioSessionPreferredIOBufferDuration: 0.005,
        supportsDTMF: false,
        supportsHolding: false,
        supportsGrouping: false,
        supportsUngrouping: false,
        ringtonePath: 'system_ringtone_default',
      ),
    );
    await FlutterCallkitIncoming.showCallkitIncoming(params);
  }

  Future<void> _checkCallExistsThenShowIncomingCall(
      Map<String, dynamic> data) async {
    if (data.containsKey('ChannelName').inverted) return;
    final providerCall = getIt<CallProvider>();
    final channelName = data['ChannelName'];
    final callExists = await providerCall.checkIfCallExists(channelName);
    if (callExists) {
      if (channelName != providerCall.callArgument?.channelName) {
        showCallkitIncoming(data);
      }
    } else {
      Snackbars.dangerBottom(
          StaticVar.context, callNotAvailableRightNow.translate().orDefault);
    }
  }

  @override
  Future<PermissionStatus> getPermissionsStatus() {
    return Permission.notification.status;
  }

  @override
  Future<PermissionStatus> requestPermissions() async {
    // For Android 13+ (API 33+), we need to request POST_NOTIFICATIONS permission
    if (Platform.isAndroid) {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      final isAndroid13OrHigher = androidInfo.version.sdkInt >= 33;

      if (isAndroid13OrHigher) {
        // Check if permission is already granted using native method
        final isGranted = await PermissionWrapper.checkNotificationPermission();
        if (isGranted) {
          return PermissionStatus.granted;
        }

        // Request POST_NOTIFICATIONS permission for Android 13+ using native method
        await PermissionWrapper.requestNotificationPermission();

        // Check again after request
        final isGrantedAfterRequest = await PermissionWrapper.checkNotificationPermission();
        if (isGrantedAfterRequest) {
          return PermissionStatus.granted;
        }

        // Fallback to permission_handler if native method doesn't work
        PermissionStatus status = await Permission.notification.request();

        // If permanently denied, open settings
        if (status.isPermanentlyDenied) {
          await AppSettings.openAppSettings(
            type: AppSettingsType.notification,
          );
          // Recheck permission after returning from settings
          return await Permission.notification.status;
        }

        return status;
      }
    }

    // Check current permission status for older Android versions and iOS
    PermissionStatus status = await Permission.notification.status;

    // If already granted, return status
    if (status.isGranted) {
      return status;
    }

    // Request permission
    status = await Permission.notification.request();

    // If permanently denied, open settings
    if (status.isPermanentlyDenied) {
      if (Platform.isAndroid) {
        // On Android, we can use app_settings package
        await AppSettings.openAppSettings(
          type: AppSettingsType.notification,
        );
      } else if (Platform.isIOS) {
        // On iOS, this will open the app's settings page
        await openAppSettings();
      }
      // Recheck permission after returning from settings
      return await Permission.notification.status;
    }

    return status;
  }
}
