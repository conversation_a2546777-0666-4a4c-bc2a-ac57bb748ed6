import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;

class ProxyService {
  static const platform = MethodChannel('com.yesatlas.modarby/proxy');

  // static Future<String?> getSystemProxy() async {
  //   try {
  //     final String? proxy = await platform.invokeMethod('getSystemProxy');
  //     if (getIt<Storage>().baseUrl.contains('test')) {
  //       StaticVar.proxy = proxy ?? '';
  //     }
  //     return null;
  //   } on PlatformException catch (e) {
  //     print("Failed to get proxy: '${e.message}'.");
  //     return null;
  //   }
  // }

  static HttpClient? getHttpClient() {
    HttpClient? httpClient;

    // if (StaticVar.proxy.isNotEmpty) {
    //   // Retrieve the system proxy settings
    //   final proxy = StaticVar.proxy;
    //   print("System proxy: $proxy");
    //
    //   if (proxy.isNotEmpty) {
    //     httpClient = HttpClient()
    //       ..findProxy = (uri) {
    //         return "PROXY $proxy";
    //       }
    //       ..badCertificateCallback =
    //           (X509Certificate cert, String host, int port) {
    //         // Allow self-signed certificates for debugging (e.g., Charles)
    //         return true;
    //       };
    //   }
    // }
    return httpClient;
  }
}

// Custom BaseClient to wrap HttpClient with proxy settings
class ProxyHttpClient extends http.BaseClient {
  final HttpClient _httpClient;

  ProxyHttpClient(this._httpClient);

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    final req = await _httpClient.openUrl(request.method, request.url);
    request.headers.forEach((name, value) {
      req.headers.add(name, value);
    });
    if (request is http.Request && request.body.isNotEmpty) {
      req.write(utf8.encode(request.body));
    }
    final response = await req.close();
    final headers = <String, String>{};
    response.headers.forEach((name, values) {
      headers[name] = values.join(',');
    });
    return http.StreamedResponse(
      response.handleError(
          (e) => throw http.ClientException(e.toString(), request.url)),
      response.statusCode,
      contentLength: response.contentLength,
      request: request,
      headers: headers,
      isRedirect: response.isRedirect,
      persistentConnection: response.persistentConnection,
      reasonPhrase: response.reasonPhrase,
    );
  }

  @override
  void close() {
    _httpClient.close();
  }
}
