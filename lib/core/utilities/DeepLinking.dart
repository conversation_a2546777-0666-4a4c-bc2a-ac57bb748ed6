import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geocoder2/geocoder2.dart';
import 'package:modarby/Enums/Roles.dart';
import 'package:modarby/Enums/Settings.dart';
import 'package:modarby/Instructor/Screens/DeactivateAccount.dart';
import 'package:modarby/Models/Content/Curriculum.dart';
import 'package:modarby/Models/Content/Gender.dart';
import 'package:modarby/Models/Content/SubSubject.dart';
import 'package:modarby/Models/Content/Subject.dart';
import 'package:modarby/Models/Content/TraineeTypes.dart';
import 'package:modarby/Models/InstructorProfile/Instructor.dart';
import 'package:modarby/Models/Search/SearchListDTO.dart';
import 'package:modarby/Models/Search/SearchProfileDTO.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/InstructorProfileProvider.dart';
import 'package:modarby/Providers/SearchProvider.dart';
import 'package:modarby/Providers/StudentProfileProvider.dart';
import 'package:modarby/Student/Screens/DeactivateAccount.dart';
import 'package:modarby/Student/Screens/Home.dart';
import 'package:modarby/Widgets/NotificationFullScreen.dart';
import 'package:modarby/core/config/static_var.dart';
import 'package:modarby/core/extentions/iterables/iterable_first.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/injection/di.dart';
import 'package:modarby/core/utilities/ProgressIndicators.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/shard.dart';
import 'package:modarby/core/utilities/storage.dart';
import 'package:modarby/features/booking_page/ModarbBooking.dart';
import 'package:provider/provider.dart';

class DeepLinking {
  DeepLinking._();
  static DeepLinking deepLinking = DeepLinking._();
  final _INSTRUCTOR_PROFILE_PATH = 'instructorprofile';
  final _deleteAccountPath = 'delete-account';
  final _PUBLIC_SEARCH_PATH = 'list';
  final _path_donlaod = 'app-download';
  final _LANDING_PATH = 'www.modarby.com';
  final _EN_LANGUGAE_PATH = 'en';
  final _AR_LANGUAGE_PATH = 'ar';
  final _SAUDIA_TIMEZONE_ID = 1780;

  late LocalizationProvider _localizationProvider;
  late AuthenticationProvider _authProvider;
  late StudentProfileProvider _studentProvider;
  late InstructorProfileProvider _instructorProvider;
  late SearchProvider _searchProvider;

  StreamSubscription<Uri> listenDynamicLinks() {
    _initializeData();
    final stream = AppLinks().uriLinkStream.listen((data) {
      handleURI(data);
    });

    return stream;
  }

  void handleURI(Uri? uri, {bool removeBack = false}) {
    if (StaticVar.isStudent.inverted &&
        StaticVar.context.read<AuthenticationProvider>().isLoggedIn) {
      getIt<Storage>().instructorComeFromViewSimilarTutors = true;
    }
    if (uri != null) {
      try {
        if ((uri.pathSegments).contains(_deleteAccountPath)) {
          return _handleDeleteAccount(uri, removeBack: removeBack);
        }
        if ((uri.pathSegments).contains(_INSTRUCTOR_PROFILE_PATH)) {
          return _handleInstructorProfile(uri, removeBack: removeBack);
        }
        if ((uri.pathSegments).contains(_PUBLIC_SEARCH_PATH)) {
          return _handlePublicSearch(uri.queryParameters);
        }
        if ((uri.pathSegments.contains(_path_donlaod))) {
          return;
        }
        if (uri.toString().isNotEmpty) {
          return _handleNotFound(uri);
        }
      } on PlatformException {
        rethrow;
      }
    }
  }

  void _handleInstructorProfile(Uri uri, {bool removeBack = false}) async {
    final idx = (uri.pathSegments.indexOf(_INSTRUCTOR_PROFILE_PATH)) + 1;
    if (idx < (uri.pathSegments).length) {
      ProgressIndicators.loadingDialog(StaticVar.context);
      final guid = uri.pathSegments[idx];
      final instructor = await _loadInstructor(guid);
      bool isOnline = false;
      for (TeachingMode item in instructor?.teachingModes ?? []) {
        if (item.isOnline ?? false) {
          isOnline = true;
        }
      }
      if (!isOnline) {
        Navigator.pop(StaticVar.context);
        return;
      }
      if (Navigator.canPop(StaticVar.context)) {
        Navigator.pop(StaticVar.context);
      }
      if (removeBack) {
        Navigator.pushNamedAndRemoveUntil(
          StaticVar.context,
          StudentModarbBookingScreen.routeName,
          (route) => false,
        );
      } else {
        Navigator.pushNamed(
          StaticVar.context,
          StudentModarbBookingScreen.routeName,
        );
      }
    }
  }

  Future<Instructor?> _loadInstructor(String guid) async {
    final dto = await _generateProfileDTO(guid);
    return await _searchProvider.searchInstructorProfile(dto);
  }

  Future<SearchProfileDTO> _generateProfileDTO(String? guid) async {
    return SearchProfileDTO(
      languageId: _localizationProvider.locals.languageId,
      currencyId: _localizationProvider.locals.currency?.id,
      guid: guid,
      timezone: _parseTimeZone(),
      deviceToken: await Shard().getToken(),
    );
  }

  void _handlePublicSearch(Map<String, dynamic> params) async {
    _loadSearchResults(params);
    Navigator.pushNamedAndRemoveUntil(
      StaticVar.context,
      StudentHomeScreen.routeName,
      (route) => false,
    );
  }

  Map<String, String>? mapSubSubjectTags(
      List<SubSubject> ssids, List<String> tags) {
    Map<String, String> result = {};

    for (var subSubject in ssids) {
      // Collect matching tags for this SubSubject
      List<String> matchingTags = subSubject.tags
              ?.where((tag) => tags.contains(tag.id.toString()))
              .map((tag) => tag.id.toString())
              .toList() ??
          [];

      if (matchingTags.isNotEmpty) {
        result[subSubject.id.toString()] = matchingTags.join('-');
      }
    }

    return result;
  }

  Future _loadSearchResults(Map<String, dynamic> params) async {
    List<SubSubject> ssids = _parseSubjects(params[SearchListDTO.majorIdKey],
            params[SearchListDTO.subjectsIdsKey]) ??
        [];
    List<String> tags = params[SearchListDTO.tagsIdsKey].toString().split('-');
    final currencyId = params[SearchListDTO.currencyIdKey];
    final dto = SearchListDTO.fromQueryParams(params,
        languageId: _localizationProvider.locals.languageId,
        currencyId: currencyId ?? _localizationProvider.locals.currency?.id,
        major: _parseMajor(params[SearchListDTO.majorIdKey]),
        subjects: _parseSubjects(params[SearchListDTO.majorIdKey],
            params[SearchListDTO.subjectsIdsKey]),
        // tags: {"366": "1"},
        tags: mapSubSubjectTags(ssids, tags) ?? {},
        selectFromSearchTags: params[SearchListDTO.tagsIdsKey].toString(),
        level: _parseLevel(params[SearchListDTO.levelIdKey]),
        curriculum: _parseCurriculum(params[SearchListDTO.curriculumIdKey]),
        city: await _parseCity(params[SearchListDTO.locationKey]),
        gender: _parseGender(params[SearchListDTO.genderKey]));
    _searchProvider.setCurrentSearchQuery = dto;
  }

  Subject? _parseMajor(String? param) {
    if (param == null) return null;
    final id = int.tryParse(param);
    List<Subject> majors = _localizationProvider.subjects.getAll();
    return majors.firstWhereOrNull(
      (major) => major.id == id,
    );
  }

  List<SubSubject>? _parseSubjects(String? majorParam, String? subjectsParam) {
    if (majorParam == null || subjectsParam == null) return [];
    final majorId = int.tryParse(majorParam);
    final params = subjectsParam.split('-');
    final subjectsIds = params.map((p) => int.tryParse(p));
    final subjects = _localizationProvider.subSubjects
        .getWithKey('$majorId')
        .cast<SubSubject>();
    return subjects
        .where((subject) => subjectsIds.contains(subject.id))
        .toList();
  }

  TraineeTypes? _parseLevel(String? param) {
    if (param == null) return null;
    final id = int.tryParse(param);
    final List<TraineeTypes> levels = _localizationProvider.lookups
        .getWithKey(TRAINEETYPE)
        .cast<TraineeTypes>();
    final level = levels.firstWhereOrNull(
      (level) => level.id == id,
    );
    StaticVar.levelId = level?.id;
    return level;
  }

  Curriculum? _parseCurriculum(String? param) {
    if (param == null) return null;
    final id = int.tryParse(param);
    final List<Curriculum> curriculums = _localizationProvider.lookups
        .getWithKey(CURRICULUMS)
        .cast<Curriculum>();
    return curriculums.firstWhereOrNull(
      (curriculum) => curriculum.id == id,
    );
  }

  int _parseTimeZone() {
    if (StaticVar.isStudent) {
      return _studentProvider.student?.timezone ?? _SAUDIA_TIMEZONE_ID;
    }
    if (getIt<Storage>().role == Roles.INSTRUCTOR ||
        getIt<Storage>().role == Roles.REFERRAL) {
      return _instructorProvider.instructor?.timezone ?? _SAUDIA_TIMEZONE_ID;
    }

    final code = getIt<Storage>().countryCode;
    final timeZoneID = Shard().getTimezoneWithIsoCountryCode(code);
    return timeZoneID;
  }

  int? _parseGender(String? param) {
    if (param == null) return null;
    final id = int.tryParse(param);
    final List<Gender> genders =
        _localizationProvider.lookups.getWithKey(GENDER).cast<Gender>();
    return genders
        .firstWhereOrNull(
          (gender) => gender.id == id,
        )
        ?.id;
  }

  Future<String> _parseCity(String? location) async {
    if (location == null) return '';
    final splitted =
        location.split(',').reversed.map((value) => double.tryParse(value));
    final geoData = await Geocoder2.getDataFromCoordinates(
        latitude: splitted.first!,
        longitude: splitted.last!,
        googleMapApiKey: Settings.GOOGLE_PLACES_API_KEY);

    return geoData.city;
  }

  void _handleNotFound(Uri? uri) async {
    ProgressIndicators.loadingDialog(StaticVar.context);
    await Future.delayed(Duration(seconds: 2));
    if (Navigator.canPop(StaticVar.context)) {
      Navigator.pop(StaticVar.context);
    }
    if (_checkIfLandingPage(uri)) return;
    Navigator.push(
      StaticVar.context,
      MaterialPageRoute(
          builder: (context) => NotificationFullScreen(url: uri.toString())),
    );
  }

  bool _checkIfLandingPage(Uri? uri) {
    if (uri != null) {
      if (uri.host == _LANDING_PATH && uri.pathSegments.isEmpty) return true;
      final segments = uri.pathSegments.where((seg) => seg.isNotEmpty).toList();
      if (uri.host == _LANDING_PATH && segments.length == 1) {
        if (segments.first == _AR_LANGUAGE_PATH) return true;
        if (segments.first == _EN_LANGUGAE_PATH) return true;
      }
    }
    return false;
  }

  void _initializeData() {
    _localizationProvider =
        StaticVar.context.read<DependencyManager>().localization;
    _authProvider = StaticVar.context.read<AuthenticationProvider>();
    _studentProvider = StaticVar.context.read<StudentProfileProvider>();
    _instructorProvider = StaticVar.context.read<InstructorProfileProvider>();
    _searchProvider = StaticVar.context.read<SearchProvider>();
  }

  void _handleDeleteAccount(Uri uri, {required bool removeBack}) {
    if (_authProvider.isLoggedIn &&
        _authProvider.accessToken != null &&
        (_authProvider.accessToken ?? '').isNotEmpty) {
      if (getIt<Storage>().role == Roles.STUDENT ||
          getIt<Storage>().role == Roles.PARENT) {
        Navigator.pushNamed(
            StaticVar.context, StudentDeactivateAccountScreen.routeName);
      } else {
        Navigator.pushNamed(
            StaticVar.context, InstructorDeactivateAccountScreen.routeName);
      }
    }
  }
}
