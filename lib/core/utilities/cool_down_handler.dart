import 'dart:async';

class CoolDownHandler {
  bool _isInCooldown = false;
  Timer? _cooldownTimer;

  Future<void> executeWithCooldown(
      Future<void> Function() action, Duration time) async {
    // If currently in cooldown, ignore the action
    if (_isInCooldown) {
      print('Action ignored: Still in cooldown');
      return;
    }

    // Execute the action
    await action();

    // Start the cooldown
    _isInCooldown = true;
    _cooldownTimer?.cancel(); // Cancel any existing timer
    _cooldownTimer = Timer(time, () {
      _isInCooldown = false;
    });
  }

  // Optional: method to manually cancel the cooldown if needed
  void cancelCooldown() {
    _cooldownTimer?.cancel();
    _isInCooldown = false;
  }

  // Dispose method to clean up resources
  void dispose() {
    _cooldownTimer?.cancel();
  }
}
