import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:modarby/Models/InstructorProfile/Instructor.dart';
import 'package:modarby/Providers/AuthenticationProvider.dart';
import 'package:modarby/Providers/DependencyManager/DependencyManager.dart';
import 'package:modarby/Providers/DependencyManager/IconsProviders/IconsProvider.dart';
import 'package:modarby/Providers/DependencyManager/LocalizationProviders/LocalizationProvider.dart';
import 'package:modarby/Providers/DependencyManager/TextProviders/TextProvider.dart';
import 'package:modarby/Providers/InstructorDashboardProvider.dart';
import 'package:modarby/Providers/InstructorProfileProvider.dart';
import 'package:modarby/Referral/Screens/AddReferral.dart';
import 'package:modarby/SharedScreens/custom_calender_widget.dart';
import 'package:modarby/Widgets/ReferralTransactionsAppBar.dart';
import 'package:modarby/core/config/themes/colors.dart';
import 'package:modarby/core/extentions/optional_mapper.dart';
import 'package:modarby/core/utilities/language/LocalizationProviderKeys.dart';
import 'package:modarby/core/utilities/language/trans.dart';
import 'package:provider/provider.dart';

import '../../../Enums/FontWeights.dart';
import '../../../core/Utilities/ProgressIndicators.dart';
import '../../Models/InstructorDashboard/ReferralTransactions.dart';
import '../../Models/InstructorDashboard/ReferralTransactionsItem.dart';
import '../../Models/Referral/ReferralStatuses.dart';
import '../../Widgets/ReferralTransactionCard.dart';
import '../../core/injection/di.dart';
import '../../core/utilities/language/LanguagesKeys.dart';
import '../../core/utilities/shard.dart';
import '../../core/utilities/storage.dart';

class ReferralTransactionsScreen extends StatefulWidget {
  static const routeName = '/ReferralTransactions';
  final AddReferralArguments? arguments;
  ReferralTransactionsScreen({
    this.arguments,
  });
  @override
  State<ReferralTransactionsScreen> createState() =>
      _ReferralTransactionsScreenState();
}

class _ReferralTransactionsScreenState
    extends State<ReferralTransactionsScreen> {
  // static const CONFIRMED_TRANSACTIONS_INDEX = 1;
  // static const PENDING_TRANSACTIONS_INDEX = 2;
  // static const TRANSFERRED_TRANSACTIONS_INDEX = 3;
  // static const ALL_TRANSACTIONS_INDEX = 4;

  late TextProvider textProvider;

  late IconsProvider iconsProvider;

  late LocalizationProvider localizationProvider;

  late AuthenticationProvider authenticationProvider;

  late InstructorDashboardProvider dashboardProvider;

  Instructor? instructor;

  String? appBarTitle;
  String? _totalAmount;

  final noTransactionsMargin = 20.0;

  final _bodyHorizontalMargin = 15.0;

  final cardHeight = 60.0;
  final cardCornerRadius = 15.0;
  final _bodyVerticalMargin = 5.0;
  final cardsVerticalMargin = 25.0;

  final cardHorizontalPadding = 10.0;
  final cardVerticalPadding = 6.0;
  final borderWidth = 1.0;
  final appBarPadding = 15.0;
  final studentSortHeight = 60.0;
  final radius = 5.0;
  final buttonHeight = 54.0;
  final padding = 10.0;
  final termsContainerCornerRadius = 10.0;
  final iconSize = 15.0;
  final height = 36.0;
  final buttonsMargin = 15.0;
  final paddingCount = 8.0;
  final sizedBoxHeight = 50.0;
  final buttonCornersRadius = 30.0;
  bool isLoading = false;

  int selectedStatusId = 2165;

  List<ReferralStatusesModel> _statuses = [];
  String? _sortBy;
  String? _statusLabel;

  String? confirmedTransactionsLabel;
  String? pendingTransactionsLabel;
  String? transferredTransactionsLabel;
  String? cancelledTransactionsLabel;
  String? allTransactionsLabel;
  String? noTransactionsLabel;
  String? _transactionsList;

  String? confirmedAmountLabel;
  String? payMeLabel;
  String? noBillingMethodDialogTitle;
  String? payMeDialogActionLabel;
  String? payMeDialogTitle;
  String? payMeDialogSubTitle;

  ReferralTransactions? _transactions;
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    _initializeData();
    _loadData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    _initializeWatchers();
    return Scaffold(
      backgroundColor: ThemeColors.primaryColor,
      appBar: (widget.arguments?.showAppBar ?? false)
          ? PreferredSize(
              preferredSize: const Size.fromHeight(80.0),
              child: ReferralTransactionsAppBar(
                appBarColor: ThemeColors.grayE5E5EA.withOpacity(.6),
                title: _transactions != null
                    ? _buildAppbarTitle()
                    : buildLoading(),
                backable: widget.arguments?.showBack,
              ),
            )
          : null,
      body: RefreshIndicator(
        key: _refreshIndicatorKey,
        onRefresh: () => _loadData(),
        child: _transactions != null ? _buildTransactions() : buildLoading(),
      ),
    );
  }

  Widget _buildAppbarTitle() {
    return Container(
      alignment: Alignment.center,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            height: 30.h,
          ),
          _buildTotalLabel(),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              textProvider.buildText(
                '${_transactions!.totalAmountWithCurrency}',
                20.sp,
                FontWeights.bold,
                ThemeColors.color1C1C1E,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildCardsPattern() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: _bodyHorizontalMargin,
        vertical: _bodyVerticalMargin,
      ),
      child: Row(
        children: [
          buildCard(
              iconsProvider.parametrizedIcon(iconsProvider.dollarRounded,
                  color: ThemeColors.black1A1818, width: 25),
              _totalAmount,
              '${_transactions!.totalAmountWithCurrency}',
              ThemeColors.secondaryColor),
          // buildPayMeButton()
        ],
      ),
    );
  }

  Widget buildCard(Widget icon, String? label, String amount, Color color) {
    return Expanded(
      child: Container(
        height: cardHeight,
        padding: EdgeInsets.all(_bodyVerticalMargin),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(cardCornerRadius),
        ),
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              icon,
              SizedBox(width: cardHorizontalPadding),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  textProvider.buildNormalText4(label),
                  SizedBox(height: cardVerticalPadding),
                  textProvider.buildNormalText1(amount,
                      weight: FontWeights.semiBold)
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _filerFromTo() {
    return FilterFromToDate(
      margin: const EdgeInsetsDirectional.only(
        start: 16,
        end: 16,
        top: 16,
        bottom: 16,
      ),
      title: titleFilterReferralBalance.translate().orDefault,
      haveFilter: dashboardProvider.fromDateReferralTransaction != null &&
          dashboardProvider.toDateReferralTransaction != null,
      startDate: dashboardProvider.fromDateReferralTransaction,
      endDate: dashboardProvider.toDateReferralTransaction,
      onClickConfirm: (DateTime? startDate, DateTime? endDate) {
        dashboardProvider.fromDateReferralTransaction = startDate;
        dashboardProvider.toDateReferralTransaction = endDate;
        setState(() {});
        _loadData();
      },
    );
  }

  Widget _buildTransactions() {
    //if (_transactions == null) return buildLoading();
    return Column(
      children: [
        // buildCardsPattern(),
        // _buildFilters(),
        buildTransactionsSort(),
        _filerFromTo(),
        buildTransactionsItems()
        //_bottom(context)
      ],
    );
  }

  Widget _buildTotalLabel() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30.0),
      child: textProvider.buildText(
        _totalAmount.toString(),
        12.sp,
        FontWeights.regular,
        ThemeColors.color1C1C1E,
      ),
    );
  }

  Widget buildArrow() {
    if (localizationProvider.locals.language == LanguagesKeys.ARABIC)
      return iconsProvider.rightArrow;
    return iconsProvider.leftArrow;
  }

  Widget buildTransactionsItems() {
    if (isLoading) return buildLoading();
    if (_transactions!.items.isEmpty) return buildNoTransactions();

    final filteredItems = _transactions!.items.where((e) {
      if (e.statusId != selectedStatusId &&
          _statusLabel != null &&
          selectedStatusId != 0) {
        return false;
      }
      return true;
    }).toList();

    if (filteredItems.isEmpty) return buildNoTransactions();

    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          children: [
            Column(
              children: filteredItems.map((e) => _buildItem(e)).toList(),
              // children: filteredItems
              //     .map((e)
              // {
              //   // if (e.statusId != selectedStatusId && _statusLabel != null && selectedStatusId != 0) {
              //   //   return const SizedBox();
              //   // }
              //   return _buildItem(e);
              // }).toList(),
            ),
            SizedBox(
              height: sizedBoxHeight,
            )
          ],
        ),
      ),
    );
  }

  Widget buildNoTransactions() {
    return Container(
      height: MediaQuery.of(context).size.height / 2.5,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            iconsProvider.bill,
            SizedBox(height: noTransactionsMargin),
            textProvider.buildTitle1(
              noTransactionsLabel,
              weight: FontWeights.semiBold,
            ),
          ],
        ),
      ),
    );
  }

  Widget buildTransactionsSort() {
    return Container(
      height: studentSortHeight,
      decoration: const BoxDecoration(
          border: Border.symmetric(
              horizontal: BorderSide(color: ThemeColors.colorF2F2F7))),
      padding: EdgeInsets.symmetric(horizontal: buttonsMargin),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          textProvider.buildNormalText2(_transactionsList,
              weight: FontWeights.semiBold, color: ThemeColors.color1C1C1E),
          Container(
            height: height,
            //width: 150,
            alignment: Alignment.center,
            padding: EdgeInsets.all(paddingCount),
            //margin: EdgeInsets.symmetric(vertical: padding),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(termsContainerCornerRadius),
                color: ThemeColors.grayE5E5EA.withOpacity(.6),
                border: Border.all(color: ThemeColors.colorD1D1D6)),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<ReferralStatusesModel>(
                selectedItemBuilder: (context) {
                  return _statuses.map((value) {
                    return DropdownMenuItem<ReferralStatusesModel>(
                        value: value,
                        child: SizedBox(
                          //width: 130,
                          child: textProvider.buildText(
                              '$_sortBy | $_statusLabel',
                              13,
                              FontWeights.light,
                              ThemeColors.color1C1C1E,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis),
                        ));
                  }).toList();
                },
                padding: EdgeInsets.zero,
                borderRadius: BorderRadius.circular(radius),
                value: _statuses.first,
                items: _statuses.map((value) {
                  return DropdownMenuItem<ReferralStatusesModel>(
                    value: value,
                    child: textProvider.buildText(
                      value.status.toString(),
                      13,
                      FontWeights.regular,
                      ThemeColors.color1C1C1E,
                    ),
                  );
                }).toList(),
                icon: Icon(
                  Icons.expand_more,
                  size: iconSize,
                  color: ThemeColors.color1C1C1E,
                ),
                onChanged: (ReferralStatusesModel? selectedItem) async {
                  setState(() {
                    _statusLabel = selectedItem?.status;
                    selectedStatusId = selectedItem!.id;
                    //_statuses = _statuses.where((status) => status['id'] == selectedStatusId).toList();
                  });
                  // setState(() {
                  //   _statusLabel = selectedItem;
                  // });
                  _transactions?.items.sort((a, b) {
                    if (a.statusId == selectedStatusId &&
                        b.statusId != selectedStatusId) {
                      return -1;
                    } else if (a.statusId != selectedStatusId &&
                        b.statusId == selectedStatusId) {
                      return 1;
                    }
                    return 0;
                  });
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildLoading() {
    return Column(
      children: [
        SizedBox(height: 40.sp),
        ProgressIndicators.loadingIndicator(),
      ],
    );
  }

  Widget _buildItem(Items transaction) {
    return Padding(
        padding: EdgeInsets.symmetric(
            horizontal: _bodyHorizontalMargin, vertical: cardHorizontalPadding),
        child: ReferralTransactionCard(
          context: context,
          transaction: transaction,
          isTransactions: true,
        ));
  }

  void _initializeData() {
    dashboardProvider = context.read<InstructorDashboardProvider>();
    dashboardProvider.fromDateReferralTransaction = null;
    dashboardProvider.toDateReferralTransaction = null;
    textProvider = context.read<DependencyManager>().text;
    localizationProvider = context.read<DependencyManager>().localization;
    authenticationProvider = context.read<AuthenticationProvider>();
    localizationProvider = context.read<DependencyManager>().localization;
    localizationProvider = context.read<DependencyManager>().localization;
    noTransactionsLabel = localizationProvider.resources
        .getWithKey(INSTRUCTOR_PAYMENTHISTORY_NODATAAVAILIABLE);
    confirmedTransactionsLabel = localizationProvider.resources
        .getWithKey(REFERRAL_TRANSACTIONS_CONFIRMED_LABEL);
    pendingTransactionsLabel = localizationProvider.resources
        .getWithKey(REFERRAL_TRANSACTIONS_PENDING_LABEL);
    allTransactionsLabel = localizationProvider.resources
        .getWithKey(REFERRAL_TRANSACTIONS_ALL_LABEL);
    transferredTransactionsLabel = localizationProvider.resources
        .getWithKey(REFERRAL_TRANSACTIONS_TRANSFERRED_LABEL);
    cancelledTransactionsLabel = localizationProvider.resources
        .getWithKey(REFERRAL_TRANSACTIONS_CANCELLED_LABEL);
    _sortBy =
        localizationProvider.resources.getWithKey(FORMS_TRANSACTIONS_SORT_BY);
    _statuses = [
      ReferralStatusesModel(id: 0, status: allTransactionsLabel.toString()),
      ReferralStatusesModel(
          id: 2166, status: pendingTransactionsLabel.toString()),
      ReferralStatusesModel(
          id: 2165, status: confirmedTransactionsLabel.toString()),
      ReferralStatusesModel(
          id: 2167, status: transferredTransactionsLabel.toString()),
      ReferralStatusesModel(
          id: 2169, status: cancelledTransactionsLabel.toString()),
    ];
    _statusLabel = _statuses
        .where((element) => element.id == 2165)
        .first
        .status
        .toString();
    _transactionsList = localizationProvider.resources
        .getWithKey(FORMS_REFERRAL_TRANSACTIONS_LIST_LABEL);

    payMeDialogTitle = localizationProvider.resources
        .getWithKey(INSTRUCTOR_MYBALANCE_SURETRANSFERED);
    payMeDialogSubTitle = localizationProvider.resources
        .getWithKey(INSTRUCTOR_MYBALANCE_PAYCONFIRMTEXT);
    payMeDialogActionLabel =
        localizationProvider.resources.getWithKey(FORMS_CONFIRM);

    noBillingMethodDialogTitle = localizationProvider.resources
        .getWithKey(INSTRUCTOR_MYBALANCE_INVALIDBILLINGOPTION);
    payMeLabel =
        localizationProvider.resources.getWithKey(INSTRUCTOR_MYBALANCE_PAYME);
    confirmedAmountLabel = localizationProvider.resources
        .getWithKey(INSTRUCTOR_MYBALANCE_CONFIRMEDAMOUNT);
    if (context.mounted) {
      authenticationProvider.clearControllerWithOutNotifier();
    }

    appBarTitle = localizationProvider.resources
        .getWithKey(FORMS_REFERRAL_LIST_OF_TRANSACTIONS);
    _totalAmount = localizationProvider.resources
        .getWithKey(FORMS_REFERRAL_TOTAL_AMOUNT_TRANSACTIONS);

    if (context.mounted) {
      authenticationProvider.clearControllerWithOutNotifier();
    }

    final whatsappNumber = getIt<Storage>().whatsappNumber;
    final countryCodeId = getIt<Storage>().countryCodeLogin;

    if (whatsappNumber != null && whatsappNumber != '') {
      authenticationProvider.whatsappNumberController.text = whatsappNumber;
    }
    if (countryCodeId != null && countryCodeId != 0) {
      authenticationProvider.whatsappCodeController.text =
          Shard().getCountryCodeNumberUsingId(countryCodeId);
    }
    dashboardProvider = context.read<InstructorDashboardProvider>();
  }

  void _initializeWatchers() async {
    textProvider = context.watch<DependencyManager>().text;
    iconsProvider = context.watch<DependencyManager>().icons;
    localizationProvider = context.watch<DependencyManager>().localization;
    dashboardProvider = context.read<InstructorDashboardProvider>();
    instructor = context.watch<InstructorProfileProvider>().instructor;

    // _referralTransactions = await dashboardProvider.getReferralTransactions(
    //     authenticationProvider.accessToken, _languageId, currencyId);

    _transactions =
        context.watch<InstructorDashboardProvider>().referralTransactions;
    //_applyFilter(selectedFilter);

    // _referralTransactions =
    //     Provider.of<InstructorDashboardProvider>(context, listen: false)
    //         .referralTransactions;
  }

  Future<void> _loadData() async {
    final locals = context.read<DependencyManager>().localization.locals;
    final languageId = locals.languageId;
    final currencyId = locals.currency?.id;
    final dateFormat = DateFormat('MM/dd/yyyy');
    String? from;
    String? to;
    if (dashboardProvider.fromDateReferralTransaction != null) {
      from = dateFormat.format(
          dashboardProvider.fromDateReferralTransaction ?? DateTime.now());
    }
    if (dashboardProvider.toDateReferralTransaction != null) {
      to = dateFormat.format(
          dashboardProvider.toDateReferralTransaction ?? DateTime.now());
    }

    setState(() {
      isLoading = true;
    });
    await dashboardProvider.getReferralTransactions(
      authenticationProvider.accessToken,
      languageId,
      currencyId,
      fromDate: from,
      toDate: to,
    );
    setState(() {
      isLoading = false;
    });
  }
}
