// To parse this JSON data, do
//
//     final searchSuggestion = searchSuggestionFromJson(jsonString);

import 'dart:convert';

List<SearchSuggestionModel> searchSuggestionFromJson(String str) => List<SearchSuggestionModel>.from(json.decode(str).map((x) => SearchSuggestionModel.fromJson(x)));

String searchSuggestionToJson(List<SearchSuggestionModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class SearchSuggestionModel {
  SearchSuggestionModel({
    this.id,
    this.isSubSubject,
    this.title,
    this.isLanguages,
    this.detectedFromLanguage,
  });

  int? id;
  bool? isSubSubject;
  String? title;
  bool? isLanguages;
  int? detectedFromLanguage;

  factory SearchSuggestionModel.fromJson(Map<String, dynamic> json) => SearchSuggestionModel(
    id: json["Id"],
    isSubSubject: json["IsSubSubject"],
    title: json["Title"],
    isLanguages: json["IsLanguages"],
    detectedFromLanguage: json["DetectedFromLanguage"],
  );

  Map<String, dynamic> toJson() => {
    "Id": id,
    "IsSubSubject": isSubSubject,
    "Title": title,
    "IsLanguages": isLanguages,
    "DetectedFromLanguage": detectedFromLanguage,
  };
}
