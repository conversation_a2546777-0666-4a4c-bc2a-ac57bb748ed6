class Review {
  Review({
    required this.id,
    required this.studentGuid,
    required this.instructorGuid,
    required this.reviewText,
    required this.rating,
    required this.studentName,
    required this.studentPicture,
    required this.dateTime,
  });

  final int? id;
  final String? studentGuid;
  final String? instructorGuid;
  final String? reviewText;
  final int? rating;
  final String? studentName;
  final String? studentPicture;
  final DateTime dateTime;

  factory Review.fromJson(Map<String, dynamic> json) => Review(
        id: json["Id"],
        studentGuid: json["StudentGuid"],
        instructorGuid: json["InstructorGuid"],
        reviewText: json["ReviewText"],
        rating: json["Rating"],
        studentName: json["StudentName"],
        studentPicture: json["StudentPicture"],
        dateTime: DateTime.parse(json["CreatedOnUtc"]),
      );
}
