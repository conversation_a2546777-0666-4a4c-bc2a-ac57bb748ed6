import 'dart:convert';

/// Success : true
/// RequestId : 87

FindInstructors findInstructorsFromJson(String str) =>
    FindInstructors.fromJson(json.decode(str));
String findInstructorsToJson(FindInstructors data) =>
    json.encode(data.toJson());

class FindInstructors {
  FindInstructors({
    bool? success,
    int? requestId,
  }) {
    _success = success;
    _requestId = requestId;
  }

  FindInstructors.fromJson(dynamic json) {
    _success = json['Success'];
    _requestId = json['RequestId'];
  }
  bool? _success;
  int? _requestId;
  FindInstructors copyWith({
    bool? success,
    int? requestId,
  }) =>
      FindInstructors(
        success: success ?? _success,
        requestId: requestId ?? _requestId,
      );
  bool? get success => _success;
  int? get requestId => _requestId;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['Success'] = _success;
    map['RequestId'] = _requestId;
    return map;
  }
}
