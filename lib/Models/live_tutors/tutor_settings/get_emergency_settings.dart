import 'package:intl/intl.dart' as intl;

intl.DateFormat dateFormat = intl.DateFormat('dd/MM/yyyy');
intl.DateFormat timeFormat = intl.DateFormat.jm();

class EmergencySettings {
  bool? emergencyHelpAllowed;
  String? emergencyHelpDontDistrubToDate;
  int? emergencyHelpAvailabilityId;

  EmergencySettings(
      {this.emergencyHelpAllowed,
        this.emergencyHelpDontDistrubToDate,
        this.emergencyHelpAvailabilityId});

  EmergencySettings.fromJson(Map<String, dynamic> json) {
    emergencyHelpAllowed = json['EmergencyHelpAllowed'];
    emergencyHelpDontDistrubToDate = json['EmergencyHelpDontDistrubToDate'];
    emergencyHelpAvailabilityId = json['EmergencyHelpAvailabilityId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['EmergencyHelpAllowed'] = this.emergencyHelpAllowed;
    data['EmergencyHelpDontDistrubToDate'] =
        this.emergencyHelpDontDistrubToDate;
    data['EmergencyHelpAvailabilityId'] = this.emergencyHelpAvailabilityId;
    return data;
  }
}