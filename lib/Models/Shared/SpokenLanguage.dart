class SpokenLanguage {
  SpokenLanguage({
    this.id,
    required this.languageSpokenId,
    required this.languagesSpokenLevelId,
    required this.languageName,
  });

  final int? id;
  final int? languageSpokenId;
  final int? languagesSpokenLevelId;
  final String? languageName;

  factory SpokenLanguage.fromJson(Map<String, dynamic> json) => SpokenLanguage(
        id: json["Id"],
        languageSpokenId: json["LanguageSpokenId"],
        languagesSpokenLevelId: json["LanguagesSpokenLevelId"],
        languageName: json["LanguageName"],
      );

  Map<String, dynamic> toJson() => {
        "LanguageSpokenId": languageSpokenId,
        "LanguagesSpokenLevelId": languagesSpokenLevelId,
        "LanguageName": languageName,
      };
}
