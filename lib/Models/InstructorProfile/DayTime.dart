import 'dart:convert';

List<DayTime> dayTimesFromJson(List json) =>
    List<DayTime>.from(json.map((x) => DayTime.fromJson(x)));

String dayTimesToJson(List<DayTime> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class DayTime {
  DayTime({
    required this.text,
    required this.value,
  });

  final String? text;
  final String? value;

  factory DayTime.fromJson(Map<String, dynamic> json) => DayTime(
        text: json["Text"],
        value: json["Value"],
      );

  Map<String, dynamic> toJson() => {
        "Text": text,
        "Value": value,
      };
}
