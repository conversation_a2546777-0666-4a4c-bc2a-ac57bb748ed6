import 'dart:convert';

TimeZone timeZoneFromJson(Map json, String? language) =>
    TimeZone.fromJson(json as Map<String, dynamic>, language);

String timeZoneToJson(TimeZone data) => json.encode(data.toJson());

class TimeZone {
  TimeZone({
    required this.id,
    required this.name,
    required this.hours,
    required this.countryCode,
  });

  final int? id;
  final String? name;
  final String? hours;
  final String? countryCode;
  factory TimeZone.fromJson(Map<String, dynamic> json, String? language) =>
      TimeZone(
        id: json["k"],
        name: json["v"][language],
        hours: json["hours"],
        countryCode: json["Country_Code"],
      );

  Map<String, dynamic> toJson() => {
        "k": id,
        "v": name,
        "hours": hours,
        "Country_Code": countryCode,
      };
}
