import 'dart:convert';

Gender genderFromJson(Map json, String? language) =>
    Gender.fromJson(json as Map<String, dynamic>, language);

String genderToJson(Gender data) => json.encode(data.toJson());

class Gender {
  Gender({
    required this.id,
    required this.name,
  });

  final int? id;
  final String? name;

  factory Gender.fromJson(Map<String, dynamic> json, String? language) =>
      Gender(
        id: json["k"],
        name: json["v"][language],
      );

  Map<String, dynamic> toJson() => {
        "k": id,
        "v": name,
      };
}
