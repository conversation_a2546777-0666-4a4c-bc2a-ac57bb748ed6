import 'dart:convert';

Currency currencyFromJson(Map json, String? language) =>
    Currency.fromJson(json as Map<String, dynamic>, language);

String currencyToJson(Currency data) => json.encode(data.toJson());

class Currency {
  Currency({required this.id, required this.name, required this.countries});

  final int? id;
  final String? name;
  final List<String> countries;

  factory Currency.fromJson(Map<String, dynamic> json, String? language) =>
      Currency(
        id: json["k"],
        name: json["v"][language],
        countries: List<String>.from(json["CountriesCode"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "k": id,
        "v": name,
        "CountriesCode": List<dynamic>.from(countries.map((x) => x)),
      };
}
