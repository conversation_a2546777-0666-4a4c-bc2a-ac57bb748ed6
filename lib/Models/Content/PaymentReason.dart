import 'dart:convert';

PaymentReason paymentReasonFromJson(Map json, String? language) =>
    PaymentReason.fromJson(json as Map<String, dynamic>, language);

String paymentReasonToJson(PaymentReason data) => json.encode(data.toJson());

class PaymentReason {
  PaymentReason({
    required this.id,
    required this.name,
  });

  final int? id;
  final String? name;

  factory PaymentReason.fromJson(Map<String, dynamic> json, String? language) =>
      PaymentReason(
        id: json["k"],
        name: json["v"][language],
      );

  Map<String, dynamic> toJson() => {
        "k": id,
        "v": name,
      };
}
