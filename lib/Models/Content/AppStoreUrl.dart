import 'dart:convert';

AppStoreUrl appStoreUrlFromJson(Map json, String? language) =>
    AppStoreUrl.fromJson(json as Map<String, dynamic>, language);

String appStoreUrlToJson(AppStoreUrl data) => json.encode(data.toJson());

class AppStoreUrl {
  AppStoreUrl({
    required this.id,
    required this.name,
  });

  final int? id;
  final String? name;

  factory AppStoreUrl.fromJson(Map<String, dynamic> json, String? language) =>
      AppStoreUrl(
        id: json["k"],
        name: json["v"][language],
      );

  Map<String, dynamic> toJson() => {
        "k": id,
        "v": name,
      };
}
