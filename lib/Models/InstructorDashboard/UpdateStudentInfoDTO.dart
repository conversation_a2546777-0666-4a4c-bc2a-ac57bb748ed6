class UpdateStudentInfoDTO {
  final String? accessToken;
  final String? bookingGuid;
  final String major;
  final String school;

  UpdateStudentInfoDTO({
    required this.accessToken,
    required this.bookingGuid,
    required this.major,
    required this.school,
  });

  Map<String, dynamic> toJson() => {
        'BookingGuid': bookingGuid,
        'Major': major,
        'SchoolName': school,
      };
}
