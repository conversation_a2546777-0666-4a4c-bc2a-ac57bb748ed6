import 'dart:convert';

List<ReferralStudent> referralStudentsFromJson(List json) =>
    List<ReferralStudent>.from(json.map((x) => ReferralStudent.fromJson(x)));

String referralStudentsToJson(List<ReferralStudent> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ReferralStudent {
  ReferralStudent(
      {required this.name,
      required this.whatsappCountryCodeId,
      required this.whatsappNumber,
      required this.statusName,
      required this.statusId,
      required this.id});

  final String? name;
  final int? whatsappCountryCodeId;
  final String? whatsappNumber;
  final int? statusId;
  final String? statusName;
  final int? id;

  factory ReferralStudent.fromJson(Map<String, dynamic> json) =>
      ReferralStudent(
          name: json["Name"],
          whatsappCountryCodeId: json["WhatsappCountryCodeId"],
          whatsappNumber: json["WhatsappNumber"],
          statusName: json["StatusName"],
          id: json["Id"],
          statusId: json["StatusId"]);

  Map<String, dynamic> toJson() => {
        "Name": name,
        "WhatsappCountryCodeId": whatsappCountryCodeId,
        "WhatsappNumber": whatsappNumber,
        "StatusName": statusName,
        "StatusId": statusId,
        "Id": id
      };
}
