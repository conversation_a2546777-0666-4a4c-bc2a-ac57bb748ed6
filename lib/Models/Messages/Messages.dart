import 'package:modarby/Models/Messages/ConversationModel.dart';

/// result : [{"counts":10,"usersid":"fc95504f-eb18-43c8-a603-1866fdce9358","usersprofile":"","userspic":"/UploadedFiles/Student/Photos/7b4b277e-ebc3-4828-b817-5f94e7f42d17.jpg","usersemail":"","usersflag":"TR","userslist":"رغد","LastMessage":null,"LastMessageDate":"2023-04-19T02:42:41.41Z","StudentAdditionalInfo":{"UserTypeId":1,"Gender":13,"TraineeTypeId":5,"CurriculumLookupId":2200,"YearOfStudy":1}},{"counts":0,"usersid":"7ef517da-a002-447d-a493-b5cc76a15465","usersprofile":"","userspic":"/UploadedFiles/Student/Photos/348beb6c-b80b-4254-aea3-d899ffd466d6.jpg","usersemail":"","usersflag":"KE","userslist":"Mohammed abuhasna","LastMessage":"hi","LastMessageDate":"2023-04-18T19:20:08.08Z","StudentAdditionalInfo":{"UserTypeId":1,"Gender":12,"TraineeTypeId":1,"CurriculumLookupId":2201,"YearOfStudy":5}},{"counts":0,"usersid":"4069c6c5-2079-4aae-b6e3-46e71ed9260e","usersprofile":"","userspic":"/Content/images/instructor/default-profile-photo.svg","usersemail":"","usersflag":"SA","userslist":"فحص","LastMessage":"hi","LastMessageDate":"2023-04-18T11:27:12.12Z","StudentAdditionalInfo":{"UserTypeId":1,"Gender":12,"TraineeTypeId":4,"CurriculumLookupId":0,"YearOfStudy":2}},{"counts":0,"usersid":"e6782e46-6804-4284-a3df-a718e43d5701","usersprofile":"","userspic":"/Content/images/instructor/default-profile-photo.svg","usersemail":"","usersflag":"SA","userslist":"نيو","LastMessage":"https://www.modarby.com/UploadedFiles/chat/attachments/83dab53c-2c8e-467b-a165-59399ee9e322.m4a","LastMessageDate":"2023-04-06T09:42:55.55Z","StudentAdditionalInfo":{"UserTypeId":1,"Gender":13,"TraineeTypeId":6,"CurriculumLookupId":0,"YearOfStudy":0}},{"counts":0,"usersid":"9ee99c05-3a65-4ad7-b2f6-a58447e7272a","usersprofile":"","userspic":"/Content/images/instructor/default-profile-photo.svg","usersemail":"","usersflag":"AE","userslist":"رغد","LastMessage":"https://www.modarby.com/UploadedFiles/chat/attachments/666e667d-740c-4109-b32b-83e981f6df4f.m4a","LastMessageDate":"2023-04-18T09:06:50.50Z","StudentAdditionalInfo":{"UserTypeId":1,"Gender":12,"TraineeTypeId":6,"CurriculumLookupId":0,"YearOfStudy":0}},{"counts":0,"usersid":"6a369bed-2de2-441f-8973-9023fb363eb0","usersprofile":"","userspic":"/Content/images/instructor/default-profile-photo.svg","usersemail":"","usersflag":"TR","userslist":"نيو","LastMessage":"https://www.modarby.com/UploadedFiles/chat/attachments/81965434-1f58-4afe-bbe2-bb5f0e232fd7.m4a","LastMessageDate":"2023-04-10T14:21:30.30Z","StudentAdditionalInfo":{"UserTypeId":1,"Gender":12,"TraineeTypeId":6,"CurriculumLookupId":0,"YearOfStudy":0}},{"counts":0,"usersid":"fcdb2f1a-6822-40d1-b3b3-3d46272ad4ea","usersprofile":"","userspic":"/Content/images/instructor/default-profile-photo.svg","usersemail":"","usersflag":"JO","userslist":"رغد","LastMessage":"hi","LastMessageDate":"2023-04-05T09:36:08.08Z","StudentAdditionalInfo":{"UserTypeId":1,"Gender":12,"TraineeTypeId":6,"CurriculumLookupId":0,"YearOfStudy":0}},{"counts":0,"usersid":"0cd1346c-b779-4dd8-ba91-a25ad43941aa","usersprofile":"","userspic":"/Content/images/instructor/default-profile-photo.svg","usersemail":"","usersflag":"SA","userslist":"jood","LastMessage":"hi","LastMessageDate":"2023-04-05T09:22:44.44Z","StudentAdditionalInfo":{"UserTypeId":1,"Gender":13,"TraineeTypeId":6,"CurriculumLookupId":0,"YearOfStudy":0}},{"counts":0,"usersid":"5565623c-23c4-4640-b40b-c1bf18abe817","usersprofile":"","userspic":"/Content/images/instructor/default-profile-photo.svg","usersemail":"","usersflag":"KW","userslist":"احمد ","LastMessage":"اهاا","LastMessageDate":"2023-04-05T09:05:38.38Z","StudentAdditionalInfo":{"UserTypeId":1,"Gender":12,"TraineeTypeId":4,"CurriculumLookupId":0,"YearOfStudy":1}},{"counts":0,"usersid":"2215984f-2512-464d-ae19-e02f4031aa61","usersprofile":"","userspic":"/Content/images/instructor/default-profile-photo.svg","usersemail":"","usersflag":"EG","userslist":"تست","LastMessage":"مرحبا","LastMessageDate":"2023-04-05T08:24:01.01Z","StudentAdditionalInfo":{"UserTypeId":1,"Gender":12,"TraineeTypeId":5,"CurriculumLookupId":2199,"YearOfStudy":1}}]
/// TotalCount : 40

class Messages {
  Messages({
    List<Conversation>? result,
    num? totalCount,
  }) {
    _result = result;
    _totalCount = totalCount;
  }

  Messages.fromJson(dynamic json) {
    if (json['result'] != null) {
      _result = [];
      json['result'].forEach((v) {
        _result?.add(Conversation.fromJson(v));
      });
    }
    _totalCount = json['TotalCount'];
  }
  List<Conversation>? _result;
  num? _totalCount;
  Messages copyWith({
    List<Conversation>? result,
    num? totalCount,
  }) =>
      Messages(
        result: result ?? _result,
        totalCount: totalCount ?? _totalCount,
      );
  List<Conversation>? get conversations => _result;
  num? get totalCount => _totalCount;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (_result != null) {
      map['result'] = _result?.map((v) => v.toJson()).toList();
    }
    map['TotalCount'] = _totalCount;
    return map;
  }
}
