import 'dart:convert';

Attachment attachmentFromJson(Map json) =>
    Attachment.fromJson(json as Map<String, dynamic>);

String attachmentToJson(Attachment data) => json.encode(data.toJson());

class Attachment {
  Attachment({
    required this.success,
    required this.fileName,
  });

  final bool? success;
  final String? fileName;

  factory Attachment.fromJson(Map<String, dynamic> json) => Attachment(
        success: json["success"],
        fileName: json["fileName"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "fileName": fileName,
      };
}
