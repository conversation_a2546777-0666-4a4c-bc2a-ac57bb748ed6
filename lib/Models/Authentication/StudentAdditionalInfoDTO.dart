class StudentAdditionalInfoDTO {
  final int? userTypeId;
  final int? genderId;
  final int? levelId;
  final int? curriculumId;
  final int? year;
  final int? placeOrResidenceCountryId;
  final int? studentSourceId;
  final String? email;
  final int? nationality;
  StudentAdditionalInfoDTO({
    this.userTypeId,
    this.genderId,
    this.levelId,
    this.curriculumId,
    this.year,
    this.placeOrResidenceCountryId,
    this.studentSourceId,
    this.email,
    this.nationality,
  });

  Map<String, dynamic> toJson() => {
        'UserTypeId': userTypeId,
        'PreferedGenderLookupId': genderId,
        'TraineeTypeId': levelId,
        'CurriculumLookupId': curriculumId,
        'YearOfStudy': year,
        'PlaceOrResidenceCountryId': placeOrResidenceCountryId,
        'StudentSourceId': studentSourceId,
        'Email': email,
        'NationalityId': nationality,
      };
}
