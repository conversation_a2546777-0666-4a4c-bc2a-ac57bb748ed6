import 'dart:convert';

/// IsStudent : true
/// IsUser : true
/// IsAdditionlInfoSaved : true
/// AdditionalInfo : {"UserTypeId":1,"PreferedGenderLookupId":12,"SchoolName":null,"TraineeTypeId":1,"CurriculumLookupId":2202,"YearOfStudy":0,"ReasonOfStudyId":2226,"SelectedSubjectIds":[4],"SelectedSubSubjectIds":[16],"PlaceOrResidenceCountryId":0}

StudentDataWizardModel studentDataWizardModelFromJson(String str) =>
    StudentDataWizardModel.fromJson(json.decode(str));
String studentDataWizardModelToJson(StudentDataWizardModel data) =>
    json.encode(data.toJson());

class StudentDataWizardModel {
  StudentDataWizardModel({
    bool? isStudent,
    bool? isUser,
    int? studentSourceId,
    bool? isAdditionlInfoSaved,
    AdditionalInfo? additionalInfo,
  }) {
    _isStudent = isStudent;
    _isUser = isUser;
    _isAdditionlInfoSaved = isAdditionlInfoSaved;
    _additionalInfo = additionalInfo;
    _studentSourceId = studentSourceId;
  }

  StudentDataWizardModel.fromJson(dynamic json) {
    _isStudent = json['IsStudent'];
    _isUser = json['IsUser'];
    _studentSourceId = json['StudentSourceId'];
    _isAdditionlInfoSaved = json['IsAdditionlInfoSaved'];
    _additionalInfo = json['AdditionalInfo'] != null
        ? AdditionalInfo.fromJson(json['AdditionalInfo'])
        : null;
  }
  bool? _isStudent;
  bool? _isUser;
  bool? _isAdditionlInfoSaved;
  int? _studentSourceId;
  AdditionalInfo? _additionalInfo;
  StudentDataWizardModel copyWith({
    bool? isStudent,
    bool? isUser,
    bool? isAdditionlInfoSaved,
    int? studentSourceId,
    AdditionalInfo? additionalInfo,
  }) =>
      StudentDataWizardModel(
        isStudent: isStudent ?? _isStudent,
        isUser: isUser ?? _isUser,
        studentSourceId: studentSourceId ?? _studentSourceId,
        isAdditionlInfoSaved: isAdditionlInfoSaved ?? _isAdditionlInfoSaved,
        additionalInfo: additionalInfo ?? _additionalInfo,
      );
  bool? get isStudent => _isStudent;
  bool? get isUser => _isUser;
  int? get studentSourceId => _studentSourceId;
  bool? get isAdditionlInfoSaved => _isAdditionlInfoSaved;
  AdditionalInfo? get additionalInfo => _additionalInfo;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['IsStudent'] = _isStudent;
    map['IsUser'] = _isUser;
    map['StudentSourceId'] = _studentSourceId;
    map['IsAdditionlInfoSaved'] = _isAdditionlInfoSaved;
    if (_additionalInfo != null) {
      map['AdditionalInfo'] = _additionalInfo?.toJson();
    }
    return map;
  }
}

/// UserTypeId : 1
/// PreferedGenderLookupId : 12
/// SchoolName : null
/// TraineeTypeId : 1
/// CurriculumLookupId : 2202
/// YearOfStudy : 0
/// ReasonOfStudyId : 2226
/// SelectedSubjectIds : [4]
/// SelectedSubSubjectIds : [16]
/// PlaceOrResidenceCountryId : 0

AdditionalInfo additionalInfoFromJson(String str) =>
    AdditionalInfo.fromJson(json.decode(str));
String additionalInfoToJson(AdditionalInfo data) => json.encode(data.toJson());

class AdditionalInfo {
  AdditionalInfo({
    int? userTypeId,
    int? preferedGenderLookupId,
    dynamic schoolName,
    int? traineeTypeId,
    int? curriculumLookupId,
    int? yearOfStudy,
    int? reasonOfStudyId,
    List<int>? selectedSubjectIds,
    List<int>? selectedSubSubjectIds,
    Map<String, dynamic>? selectedTags,
    int? placeOrResidenceCountryId,
  }) {
    _userTypeId = userTypeId;
    _preferedGenderLookupId = preferedGenderLookupId;
    _schoolName = schoolName;
    _traineeTypeId = traineeTypeId;
    _curriculumLookupId = curriculumLookupId;
    _yearOfStudy = yearOfStudy;
    _reasonOfStudyId = reasonOfStudyId;
    _selectedSubjectIds = selectedSubjectIds;
    _selectedSubSubjectIds = selectedSubSubjectIds;
    selectedTags = selectedTags;
    _placeOrResidenceCountryId = placeOrResidenceCountryId;
  }

  AdditionalInfo.fromJson(dynamic json) {
    _userTypeId = json['UserTypeId'];
    _preferedGenderLookupId = json['PreferedGenderLookupId'];
    _schoolName = json['SchoolName'];
    _traineeTypeId = json['TraineeTypeId'];
    _curriculumLookupId = json['CurriculumLookupId'];
    _yearOfStudy = json['YearOfStudy'];
    _reasonOfStudyId = json['ReasonOfStudyId'];
    _selectedSubjectIds = json['SelectedSubjectIds'] != null
        ? json['SelectedSubjectIds'].cast<int>()
        : [];
    _selectedSubSubjectIds = json['SelectedSubSubjectIds'] != null
        ? json['SelectedSubSubjectIds'].cast<int>()
        : [];
    _selectedTags = json['SubSubjectsDictionary'] ?? {};
    _placeOrResidenceCountryId = json['PlaceOrResidenceCountryId'];
  }
  int? _userTypeId;
  int? _preferedGenderLookupId;
  dynamic _schoolName;
  int? _traineeTypeId;
  int? _curriculumLookupId;
  int? _yearOfStudy;
  int? _reasonOfStudyId;
  List<int>? _selectedSubjectIds;
  List<int>? _selectedSubSubjectIds;
  Map<String, dynamic>? _selectedTags;
  int? _placeOrResidenceCountryId;
  AdditionalInfo copyWith({
    int? userTypeId,
    int? preferedGenderLookupId,
    dynamic schoolName,
    int? traineeTypeId,
    int? curriculumLookupId,
    int? yearOfStudy,
    int? reasonOfStudyId,
    List<int>? selectedSubjectIds,
    List<int>? selectedSubSubjectIds,
    Map<String, dynamic>? selectedTags,
    int? placeOrResidenceCountryId,
  }) =>
      AdditionalInfo(
        userTypeId: userTypeId ?? _userTypeId,
        preferedGenderLookupId:
            preferedGenderLookupId ?? _preferedGenderLookupId,
        schoolName: schoolName ?? _schoolName,
        traineeTypeId: traineeTypeId ?? _traineeTypeId,
        curriculumLookupId: curriculumLookupId ?? _curriculumLookupId,
        yearOfStudy: yearOfStudy ?? _yearOfStudy,
        reasonOfStudyId: reasonOfStudyId ?? _reasonOfStudyId,
        selectedSubjectIds: selectedSubjectIds ?? _selectedSubjectIds,
        selectedSubSubjectIds: selectedSubSubjectIds ?? _selectedSubSubjectIds,
        selectedTags: selectedTags ?? _selectedTags,
        placeOrResidenceCountryId:
            placeOrResidenceCountryId ?? _placeOrResidenceCountryId,
      );
  int? get userTypeId => _userTypeId;
  int? get preferedGenderLookupId => _preferedGenderLookupId;
  dynamic get schoolName => _schoolName;
  int? get traineeTypeId => _traineeTypeId;
  int? get curriculumLookupId => _curriculumLookupId;
  int? get yearOfStudy => _yearOfStudy;
  int? get reasonOfStudyId => _reasonOfStudyId;
  List<int>? get selectedSubjectIds => _selectedSubjectIds;
  List<int>? get selectedSubSubjectIds => _selectedSubSubjectIds;
  Map<String, dynamic>? get selectedTags => _selectedTags;
  int? get placeOrResidenceCountryId => _placeOrResidenceCountryId;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['UserTypeId'] = _userTypeId;
    map['PreferedGenderLookupId'] = _preferedGenderLookupId;
    map['SchoolName'] = _schoolName;
    map['TraineeTypeId'] = _traineeTypeId;
    map['CurriculumLookupId'] = _curriculumLookupId;
    map['YearOfStudy'] = _yearOfStudy;
    map['ReasonOfStudyId'] = _reasonOfStudyId;
    map['SelectedSubjectIds'] = _selectedSubjectIds;
    map['SelectedSubSubjectIds'] = _selectedSubSubjectIds;
    map['SubSubjectsDictionary'] = _selectedTags;
    map['PlaceOrResidenceCountryId'] = _placeOrResidenceCountryId;
    return map;
  }
}
