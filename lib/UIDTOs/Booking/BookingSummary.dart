import 'LessonData.dart';
import 'PackageData.dart';

class BookingSummaryArgs {
  final bool isBookingThroughPackage;
  final String? lessonsBulkGuid;
  final List<LessonData?>? lessons;

  final bool isPackage;
  final String? packageGuid;
  final PackageData? package;
  final double? remainingTime;

  final bool isCompletePayment;
  final int? paymentMethodId;

  BookingSummaryArgs({
    this.isBookingThroughPackage = false,
    this.lessonsBulkGuid,
    this.lessons,
    this.isPackage = false,
    this.packageGuid,
    this.package,
    this.remainingTime,
    this.isCompletePayment = false,
    this.paymentMethodId,
  });
}
