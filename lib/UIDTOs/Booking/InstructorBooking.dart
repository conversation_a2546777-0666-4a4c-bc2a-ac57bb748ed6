import 'LessonData.dart';

class InstructorBookingArgs {
  final String? packageGuid;
  final double? packageRemainingTime;
  final String? studentName;
  final String? studentPhoto;
  final String? studentCountryCode;
  final String? studentWhatsappNumber;
  List<LessonData?>? lessons;

  InstructorBookingArgs({
    required this.packageGuid,
    required this.packageRemainingTime,
    required this.studentName,
    required this.studentPhoto,
    required this.studentCountryCode,
    required this.studentWhatsappNumber,
    this.lessons,
  });
}
