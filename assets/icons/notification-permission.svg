<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="282.303" height="240.506" viewBox="0 0 282.303 240.506">
  <defs>
    <linearGradient id="linear-gradient" x1="-0.014" y1="0.502" x2="1.002" y2="0.502" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#83a6ff"/>
      <stop offset="1" stop-color="#5a78ff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" y1="0.5" x2="1.001" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#d5dae8"/>
      <stop offset="1" stop-color="#9aa3ba"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="0.232" y1="0.091" x2="0.789" y2="0.925" xlink:href="#linear-gradient"/>
    <filter id="Path" x="189.798" y="147.761" width="92.505" height="92.745" filterUnits="userSpaceOnUse">
      <feOffset dy="5" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="4" result="blur"/>
      <feFlood flood-color="#1d4bff" flood-opacity="0.22"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-4" x1="0.247" y1="0.364" x2="0.626" y2="0.699" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#4f6aff"/>
      <stop offset="1" stop-color="#657eff" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="0.888" y1="1.094" x2="0.186" y2="0.395" gradientUnits="objectBoundingBox">
      <stop offset="0.011" stop-color="#c2d1ff"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="1.222" y1="1.398" x2="-1.459" y2="-2.539" xlink:href="#linear-gradient-5"/>
    <linearGradient id="linear-gradient-7" x1="0.151" y1="0.207" x2="0.743" y2="0.74" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e81778"/>
      <stop offset="1" stop-color="#e01f79" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="0.803" y1="0.93" x2="0.122" y2="0.253" gradientUnits="objectBoundingBox">
      <stop offset="0.011" stop-color="#ffc9e3"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
  </defs>
  <g id="_09" data-name="09" transform="translate(1 36.436)">
    <path id="Shape" d="M4.71,24.334l-.36-1.44a1.023,1.023,0,0,1,.717-1.258A7.307,7.307,0,0,1,6.324,15.88l1.614-2.159A.448.448,0,0,0,7.758,13L5.425,11.741A8.1,8.1,0,0,1,1.661,7.065,1.022,1.022,0,0,1,.406,6.344L.047,4.906A1.027,1.027,0,0,1,.766,3.644L14.391.047a1.018,1.018,0,0,1,1.254.721L16,2.206a1.423,1.423,0,0,1-.9,1.438,9.18,9.18,0,0,1-1.075,6.118L12.42,11.921a.447.447,0,0,0,.18.721L15.108,13.9a7.237,7.237,0,0,1,3.765,4.5,1.019,1.019,0,0,1,1.254.717l.36,1.442a1.024,1.024,0,0,1-.717,1.258l-13.625,3.6a.888.888,0,0,1-.281.045A1.252,1.252,0,0,1,4.71,24.334Z" transform="translate(7.662 92.927)" fill="url(#linear-gradient)"/>
    <path id="Path-2" data-name="Path" d="M11.409,11.884A6.826,6.826,0,0,0,2.217,1.79a6.826,6.826,0,0,0,9.192,10.094Z" transform="translate(223.209 47.809)" fill="#eaeef9"/>
    <path id="Path-3" data-name="Path" d="M5.869,8.776A4.5,4.5,0,1,0,.221,5.888,4.5,4.5,0,0,0,5.869,8.776Z" transform="translate(75.31 13.075)" fill="#eaeef9"/>
    <path id="Path-4" data-name="Path" d="M3.99,5.968A3.057,3.057,0,1,0,.15,4,3.059,3.059,0,0,0,3.99,5.968Z" transform="translate(86.971 1.209)" fill="#eaeef9"/>
    <path id="Path-5" data-name="Path" d="M4.8,5A2.874,2.874,0,0,0,.934.752,2.874,2.874,0,0,0,4.8,5Z" transform="translate(230.33 24.041)" fill="#eaeef9"/>
    <path id="Path-6" data-name="Path" d="M3.3,3.439A1.976,1.976,0,0,0,.641.517,1.976,1.976,0,0,0,3.3,3.439Z" transform="translate(161.057 4.146)" fill="#eaeef9"/>
    <path id="Shape-2" data-name="Shape" d="M.6,21.587a.931.931,0,0,1-.537-1.26L5.089,5.936a.934.934,0,0,1,.36-.719,2.651,2.651,0,0,1,2.868-.539c.18,0,.357.178.535.178L9.93,5.4a.175.175,0,0,0,.178.18c.357-.18.537-.541.9-.721.18-.178.537-.357.537-.537a.619.619,0,0,0-.357-.18,1.625,1.625,0,0,1-1.077-1.081,2.423,2.423,0,0,1,.9-1.62c.18-.18.537-.537.537-.719a.717.717,0,1,1,1.434,0,2.389,2.389,0,0,1-1.075,1.8l-.18.18a.175.175,0,0,0,.18.178c.537.362,1.434.721,1.254,1.8A3.2,3.2,0,0,1,11.9,6.116a.626.626,0,0,0-.36.182v.18A18.988,18.988,0,0,1,13.515,8.1a8.448,8.448,0,0,1,1.614,2.159,6.977,6.977,0,0,1,1.434,3.779,2.706,2.706,0,0,1-.719,1.977c-.178.182-.358.182-.535.362h-.18L1.322,21.587Z" transform="translate(130.411 1.979)" fill="#ff5a5f"/>
    <path id="Path-7" data-name="Path" d="M6.572.816C6.393,1,5.854.634,5.495.454,4.959.1,4.061-.264,3.525.275s-.359,1.079-.359,1.8v.539c-.359-.18-.539-.359-.9-.539-.536-.361-1.434-.9-2.15-.18A.849.849,0,0,0,.3,2.974a.543.543,0,0,0,.716,0c.18,0,.359.18.539.359a3.179,3.179,0,0,0,1.793.72H3.7c1.077-.361.9-1.62.9-2.159V1.534c.18,0,.18.18.359.18a2.733,2.733,0,0,0,2.329.361.658.658,0,0,0,.359-.9A.858.858,0,0,0,6.572.816Z" transform="translate(147.036 10.879)" fill="#ff5a5f"/>
    <path id="Path-8" data-name="Path" d="M1.016,4.974c.177,0,.357-.18.536-.18h.359a2.465,2.465,0,0,0,1.973-.18A1.97,1.97,0,0,0,4.6,2.456V1.918h.539A2.35,2.35,0,0,0,7.29,1.377c.359-.18.359-.72.18-1.079S6.752-.062,6.393.118A1.57,1.57,0,0,1,5.138.3,1.717,1.717,0,0,0,3.345.838a2.675,2.675,0,0,0-.359,1.618c0,.541,0,.72-.177.72-.18.18-.539,0-.9,0a1.424,1.424,0,0,0-1.793.359c-.18.359-.18.72.18.9C.657,4.974.836,4.974,1.016,4.974Z" transform="translate(144.524 4.2)" fill="#ff5a5f"/>
    <path id="Path-9" data-name="Path" d="M0,2.7c0,.182,0,.182.18.361h.18l.9-.361a.337.337,0,0,1,.539,0l.9.361c.18,0,.18,0,.359-.18V2.7l-.359-.9a.339.339,0,0,1,0-.541l.359-.9c0-.18,0-.18-.18-.359h-.18l-.9.359a.337.337,0,0,1-.539,0L.359,0C.18,0,.18,0,0,.18v.18l.359.9a.341.341,0,0,1,0,.541Z" transform="translate(146.974 0)" fill="#ff5a5f"/>
    <path id="Path-10" data-name="Path" d="M2.689,1.8V1.259l.359-.9c0-.182,0-.182-.18-.361h-.18l-.9.361H1.255L.359,0C.18,0,.18,0,0,.18V.361l.359.9V1.8L0,2.7c0,.18,0,.18.18.359h.18l.9-.359h.539l.9.359c.18,0,.18,0,.359-.18V2.7Z" transform="translate(155.221 6.836)" fill="#ff5a5f"/>
    <path id="Path-11" data-name="Path" d="M.3,2.338l.718-.361h.539l.716.361h.18v-.18l-.359-.72V.9L2.448.18V0h-.18L1.553.359H1.014L.3,0H.116V.18L.475.9v.541l-.359.72c-.177.18-.177.18.18.18Z" transform="translate(135.92 2.879)" fill="#ff5a5f"/>
    <path id="Path-12" data-name="Path" d="M161.9,136.545H0L29.583,0H191.66Z" transform="translate(20.62 40.624)" fill="#fff"/>
    <path id="Path-13" data-name="Path" d="M161.9,136.545H0L29.583,0H191.66Z" transform="translate(20.62 40.624)" fill="#d5dae8"/>
    <path id="Path-14" data-name="Path" d="M44.3,136.545H10.775A10.82,10.82,0,0,1,.018,125.21L5.4,0h8.068Z" transform="translate(44.805 40.624)" fill="url(#linear-gradient-2)"/>
    <path id="Path-15" data-name="Path" d="M197.578,136.545H35.678L8.245,31.661l-.177-.718L0,0H162.077l6.993,26.8,1.075,4.138.18.718Z" transform="translate(50.202 40.624)" fill="#fff"/>
    <path id="Path-16" data-name="Path" d="M170.145,30.943H8.068L0,0H162.077l6.993,26.8Z" transform="translate(50.202 40.624)" fill="#f3f5fa"/>
    <path id="Path-17" data-name="Path" d="M5.738,11.515A5.757,5.757,0,1,0,0,5.759,5.747,5.747,0,0,0,5.738,11.515Z" transform="translate(87.135 51.237)" fill="#172d45" opacity="0.3"/>
    <path id="Path-18" data-name="Path" d="M5.736,11.515A5.757,5.757,0,1,0,0,5.759,5.749,5.749,0,0,0,5.736,11.515Z" transform="translate(181.802 51.237)" fill="#172d45" opacity="0.3"/>
    <path id="Path-19" data-name="Path" d="M12.19,35.981a1.979,1.979,0,0,1,0-3.959c3.945,0,8.07-5.577,8.07-14.033v-.718c-.18-8.277-4.3-13.313-8.07-13.313-3.584,0-7.529,4.856-8.068,12.413H0C.539,7.015,5.736,0,12.013,0,18.647,0,23.844,7.736,24.2,17.63v.541C24.383,28.064,19,35.981,12.19,35.981Z" transform="translate(79.247 23.353)" fill="#1c3754"/>
    <path id="Path-20" data-name="Path" d="M12.19,35.981a1.979,1.979,0,0,1,0-3.959c3.945,0,8.068-5.577,8.068-14.033v-.718c-.18-8.277-4.3-13.313-8.068-13.313-3.586,0-7.529,4.856-8.068,12.413H0C.536,7.015,5.736,0,12.011,0,18.645,0,23.844,7.736,24.2,17.63v.541C24.383,28.064,19,35.981,12.19,35.981Z" transform="translate(173.375 23.353)" fill="#1c3754"/>
    <path id="Path-21" data-name="Path" d="M0,.5H260.869" transform="translate(0 176.488)" fill="none" stroke="#eaeef9" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"/>
    <path id="Path-22" data-name="Path" d="M1.793,3.6A1.8,1.8,0,0,1,0,1.8,1.7,1.7,0,0,1,1.793,0H7.35a1.8,1.8,0,0,1,0,3.6Z" transform="translate(186.642 108.446)" fill="#d5dae8"/>
    <path id="Shape-3" data-name="Shape" d="M35.322,82.574a2.157,2.157,0,0,1-1.794-1.26L30.66,70.7H16.5a1.8,1.8,0,0,1,0-3.6H29.584L25.1,51.27H11.474a1.8,1.8,0,0,1-1.794-1.8,1.7,1.7,0,0,1,1.794-1.8H24.027L19.543,31.844H6.633a1.8,1.8,0,0,1-1.792-1.8,1.7,1.7,0,0,1,1.792-1.8H18.646L14.165,12.413H1.794A1.8,1.8,0,0,1,0,10.612a1.7,1.7,0,0,1,1.794-1.8H13.088L11.295,2.339A1.852,1.852,0,0,1,12.551.18h.537a2.156,2.156,0,0,1,1.794,1.258l2.151,7.376H31.375a1.7,1.7,0,0,1,1.794,1.8,1.7,1.7,0,0,1-1.794,1.8H18.109l4.484,15.829H45.182L38.01,2.339A1.85,1.85,0,0,1,39.266.18H39.8A2.156,2.156,0,0,1,41.6,1.438l2.153,7.376h7.709a1.7,1.7,0,0,1,1.792,1.8,1.8,1.8,0,0,1-1.792,1.8H44.823L49.3,28.242H64.546a1.8,1.8,0,0,1,1.792,1.8,1.7,1.7,0,0,1-1.792,1.8H50.381l4.482,15.829H77.634L67.771,12.413H62.932a1.8,1.8,0,0,1,0-3.6H66.7L64.9,2.339A2.05,2.05,0,0,1,65.083.9,1.847,1.847,0,0,1,66.16,0H66.7A2.161,2.161,0,0,1,68.49,1.258l2.149,7.374h3.767a1.7,1.7,0,0,1,1.794,1.8,1.7,1.7,0,0,1-1.794,1.8H71.718L76.2,28.063H98.969L94.487,12.234H82.833a1.8,1.8,0,0,1,0-3.6h10.04L91.079,2.159A1.852,1.852,0,0,1,92.336,0h.537a2.159,2.159,0,0,1,1.794,1.258L96.82,8.632h22.769l-1.794-6.473A1.851,1.851,0,0,1,119.049,0h.539a2.164,2.164,0,0,1,1.794,1.258l2.149,7.374h13.987a1.7,1.7,0,0,1,1.792,1.8,1.7,1.7,0,0,1-1.792,1.8H124.07l4.482,15.829H142a1.8,1.8,0,0,1,1.794,1.8,1.7,1.7,0,0,1-1.794,1.8H129.629l4.482,15.831h12.908a1.8,1.8,0,0,1,1.794,1.8,1.7,1.7,0,0,1-1.794,1.8H135.185l4.484,15.829H151.86a1.8,1.8,0,0,1,0,3.6h-11.3l2.69,9.533A1.848,1.848,0,0,1,142,82.213h-.537a2.152,2.152,0,0,1-1.792-1.258L136.8,70.522H114.03l2.688,9.354a2.038,2.038,0,0,1-.178,1.438,1.829,1.829,0,0,1-1.077.9h-.537a2.155,2.155,0,0,1-1.794-1.258L110.263,70.7H87.494l2.69,9.356a1.85,1.85,0,0,1-1.256,2.157h-.537A2.152,2.152,0,0,1,86.6,80.954L83.731,70.7H60.959l2.69,9.356a1.846,1.846,0,0,1-1.256,2.157h-.535a2.155,2.155,0,0,1-1.794-1.258L57.2,70.7H34.423l2.692,9.533a2.05,2.05,0,0,1-.18,1.442,1.85,1.85,0,0,1-1.077.9Z" transform="translate(76.735 80.203)" fill="#d5dae8"/>
    <path id="Path-23" data-name="Path" d="M6.288,2.518A19.544,19.544,0,0,0,3.241,14.751a21.711,21.711,0,0,0,4.3,11.694,19.786,19.786,0,0,0,10.04,7.015,21.629,21.629,0,0,0,12.193,0,15.641,15.641,0,0,0,8.963-7.195,11.513,11.513,0,0,0,.18-10.795c-1.614-3.418-5.02-5.938-8.786-7.377a35.292,35.292,0,0,0-12.37-2.518A2.79,2.79,0,0,1,17.942,0,42.48,42.48,0,0,1,32.105,3.059C36.587,5.036,41.071,8.274,43.4,13.313a16.935,16.935,0,0,1,1.614,7.915,17.629,17.629,0,0,1-2.15,7.556A19.777,19.777,0,0,1,31.03,38.138a24.1,24.1,0,0,1-14.7-.359A24.692,24.692,0,0,1,4.675,28.963,25.068,25.068,0,0,1,.014,15.292,23.732,23.732,0,0,1,4.136,1.439a1.584,1.584,0,0,1,1.973-.359C6.468,1.079,6.647,1.979,6.288,2.518Z" transform="translate(119.216 99.092)" fill="#ff5a5f"/>
    <g transform="matrix(1, 0, 0, 1, -1, -36.44)" filter="url(#Path)">
      <path id="Path-24" data-name="Path" d="M67.8,41.411c-.18.539-.18,1.259-.359,1.8a23.948,23.948,0,0,1-1.97,5.4A33.771,33.771,0,0,1,43.96,67.316a5.477,5.477,0,0,1-1.436.361,33.439,33.439,0,0,1-15.238.359,34.361,34.361,0,1,1,31.2-57.927A34.689,34.689,0,0,1,67.8,41.411Z" transform="translate(201.8 154.76)" fill="url(#linear-gradient-3)"/>
    </g>
    <path id="Path-25" data-name="Path" d="M38.726,25a33.76,33.76,0,0,1-21.515,18.71L1.255,27.7,0,20.328l12.37-1.439L13.627,0Z" transform="translate(227.52 141.909)" fill="url(#linear-gradient-4)"/>
    <path id="Path-26" data-name="Path" d="M0,25.381c0-1.979,2.152-1.079,3.407-5.4C4.841,14.766,2.689,9.73,8.247,5.412a9.791,9.791,0,0,1,1.434-.9c.18-.18.359-.359.359-.541a3.6,3.6,0,1,1,7.172-.718v.359a.936.936,0,0,0,.357.72c3.945,1.979,5.559,5.4,5.738,9.715.716,9.713,2.868,8.274,4.123,9.895,1.077,1.618-.536,3.418-2.689,3.418C3.227,28.079,0,28.258,0,25.381Z" transform="translate(221.171 135.698)" fill="url(#linear-gradient-5)"/>
    <path id="Path-27" data-name="Path" d="M2.221,4.677A3.319,3.319,0,0,1,.248.361C.248.18.427,0,.784,0H6.523c.18,0,.539.18.539.361a3.676,3.676,0,0,1-.9,3.777A4.455,4.455,0,0,1,2.221,4.677Z" transform="translate(231.323 164.856)" fill="url(#linear-gradient-6)"/>
    <g id="Group_1" data-name="Group 1" transform="translate(-82.349 -40.626)">
      <path id="Path-28" data-name="Path" d="M41.4,20.667a22.757,22.757,0,0,1-.484,4.611V25.6A21.037,21.037,0,0,1,28.227,40.223c-.317.159-.467.159-.785.317a18.392,18.392,0,0,1-6.66,1.113A20.827,20.827,0,0,1,20.782,0,20.6,20.6,0,0,1,41.4,20.667Z" transform="translate(95.2 4.19)" fill="#ff5a5f"/>
      <path id="Path-29" data-name="Path" d="M26.642,14.307v.319A21.037,21.037,0,0,1,13.955,29.252c-.317.159-.467.159-.785.317L0,16.374,1.9,9.7l-.15-2.542L5.242,1.589,8.563,0l6.827,2.861Z" transform="translate(109.472 15.16)" fill="url(#linear-gradient-7)"/>
      <path id="Path-30" data-name="Path" d="M16.72,9.062H9.425a.684.684,0,0,1-.634-.636V1.113A1.182,1.182,0,0,0,7.84,0h-.15a8.993,8.993,0,0,0,2.537,17.806,8.783,8.783,0,0,0,7.612-7.63A1.2,1.2,0,0,0,16.72,9.062Z" transform="translate(107.108 16.514)" fill="url(#linear-gradient-8)"/>
    </g>
  </g>
</svg>
