<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="73.286" height="66.556" viewBox="0 0 73.286 66.556">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_3592" data-name="Rectangle 3592" width="7.334" height="5.063" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <rect id="Rectangle_3593" data-name="Rectangle 3593" width="4.165" height="12.106" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <rect id="Rectangle_3594" data-name="Rectangle 3594" width="7.182" height="5.117" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-4">
      <rect id="Rectangle_3595" data-name="Rectangle 3595" width="5.252" height="12.047" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-5">
      <rect id="Rectangle_3596" data-name="Rectangle 3596" width="6.144" height="5.689" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-6">
      <rect id="Rectangle_3597" data-name="Rectangle 3597" width="4.324" height="11.383" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-7">
      <rect id="Rectangle_3598" data-name="Rectangle 3598" width="13.102" height="14.798" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-8">
      <rect id="Rectangle_3599" data-name="Rectangle 3599" width="14.905" height="17.8" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_13095" data-name="Group 13095" transform="translate(0 0)">
    <g id="Group_13097" data-name="Group 13097">
      <g id="Group_13093" data-name="Group 13093" transform="translate(0 0)">
        <g id="Group_13096" data-name="Group 13096">
          <path id="Path_33600" data-name="Path 33600" d="M41.454,83.137a13.6,13.6,0,1,1,27.2,0,13.609,13.609,0,1,1-27.2,0" transform="translate(-18.54 -30.988)" fill="#fff"/>
          <g id="Group_13076" data-name="Group 13076" transform="translate(33.556 48.33)" opacity="0.129">
            <g id="Group_13075" data-name="Group 13075">
              <g id="Group_13074" data-name="Group 13074" clip-path="url(#clip-path)">
                <path id="Path_33601" data-name="Path 33601" d="M68.014,87.7a.436.436,0,0,0-.774-.052,8.276,8.276,0,0,1-.3,1.484,4.043,4.043,0,0,1-1.611,1.545,3.61,3.61,0,0,1-1.183.3c-1.076-.027-1.181-.034-2.083-.1a3.369,3.369,0,0,0-.837-.13.606.606,0,0,0-.08,1.181c.846.19,1.433.509,1.838.516a4.912,4.912,0,0,0,2.635-.29A5.274,5.274,0,0,0,67.689,89.8a5.2,5.2,0,0,0,.324-2.107" transform="translate(-60.706 -87.434)"/>
              </g>
            </g>
          </g>
          <g id="Group_13079" data-name="Group 13079" transform="translate(37.532 53.832)" opacity="0.129">
            <g id="Group_13078" data-name="Group 13078">
              <g id="Group_13077" data-name="Group 13077" clip-path="url(#clip-path-2)">
                <path id="Path_33602" data-name="Path 33602" d="M69.423,103.986c-.011-.079-.022-.159-.032-.238.085,1.161.281,2.836.376,3.449.057.764.1,1.529.146,2.294.392-.133.782-.274,1.171-.415.223-.081.447-.161.669-.244.1-.039.207-.084.311-.123,0-.035-.009-.075-.014-.108a35.318,35.318,0,0,0-.769-4.58,50.521,50.521,0,0,0-1.7-5.564,2.782,2.782,0,0,0-.323-.536c-.148-.167-.249-.405-.473-.478a.641.641,0,0,0-.8.908,5.168,5.168,0,0,1,.891,2.369c.262,1.318.452,2.516.542,3.266" transform="translate(-67.899 -97.386)"/>
              </g>
            </g>
          </g>
          <path id="Path_33603" data-name="Path 33603" d="M57.429,108.8H57.4a.422.422,0,0,1-.392-.448,30.558,30.558,0,0,1,1.614-7.966,8.5,8.5,0,0,1,1.47-2.864,3.082,3.082,0,0,1,2.927-1.167c1.61.369,2.419,2.239,2.888,3.615a42.689,42.689,0,0,1,1.884,8.071.422.422,0,0,1-.276.453l-.162.057a.421.421,0,0,1-.437-.692,41.785,41.785,0,0,0-1.806-7.618c-.632-1.856-1.378-2.859-2.279-3.066a2.292,2.292,0,0,0-2.1.9,7.765,7.765,0,0,0-1.31,2.585,29.7,29.7,0,0,0-1.57,7.749.421.421,0,0,1-.42.393" transform="translate(-25.496 -43.068)"/>
          <path id="Path_33604" data-name="Path 33604" d="M63.571,90.5a5.5,5.5,0,0,1-1.753-.289,3.07,3.07,0,0,1-2.107-2.176,4.389,4.389,0,0,1-.268-1.348,4.465,4.465,0,0,1,.307-1.653A5.451,5.451,0,0,1,61.457,82.6a4.158,4.158,0,0,1,2.945-.9h0a3.741,3.741,0,0,1,2.644,1.81,5.376,5.376,0,0,1,.816,2.941,4.434,4.434,0,0,1-.137,1.17,3.964,3.964,0,0,1-2.389,2.559,4.837,4.837,0,0,1-1.766.322m.462-7.978a3.374,3.374,0,0,0-2.056.741,4.613,4.613,0,0,0-1.437,2.061,3.623,3.623,0,0,0-.255,1.342,3.56,3.56,0,0,0,.22,1.091,2.242,2.242,0,0,0,1.581,1.656,4.36,4.36,0,0,0,2.942-.018A3.113,3.113,0,0,0,66.913,87.4a3.641,3.641,0,0,0,.107-.95,4.539,4.539,0,0,0-.68-2.483,3.018,3.018,0,0,0-2.024-1.432,2.682,2.682,0,0,0-.284-.014" transform="translate(-26.585 -36.531)"/>
          <path id="Path_33605" data-name="Path 33605" d="M54.746,97.318A14.268,14.268,0,1,1,69.014,83.05,14.284,14.284,0,0,1,54.746,97.318m0-27.694A13.426,13.426,0,1,0,68.172,83.05,13.441,13.441,0,0,0,54.746,69.624" transform="translate(-18.103 -30.762)"/>
          <path id="Path_33606" data-name="Path 33606" d="M82.274,24.354a13.6,13.6,0,1,1,27.2,0,13.609,13.609,0,1,1-27.2,0" transform="translate(-36.796 -4.762)" fill="#fff"/>
          <path id="Path_33607" data-name="Path 33607" d="M107.126,50.227a.421.421,0,0,1-.416-.363,41.854,41.854,0,0,0-1.824-7.722c-.632-1.856-1.378-2.858-2.279-3.065a2.288,2.288,0,0,0-2.1.9,7.772,7.772,0,0,0-1.31,2.585,30.577,30.577,0,0,0-1.486,7.157.421.421,0,0,1-.839-.075A31.412,31.412,0,0,1,98.4,42.287a8.493,8.493,0,0,1,1.47-2.863,3.082,3.082,0,0,1,2.928-1.168c1.61.369,2.418,2.24,2.887,3.615a42.7,42.7,0,0,1,1.86,7.878.421.421,0,0,1-.359.475.53.53,0,0,1-.059,0" transform="translate(-43.323 -17.084)"/>
          <path id="Path_33608" data-name="Path 33608" d="M102.328,45.527a.14.14,0,0,1-.1-.234c1.025-1.141,2.078-2.28,3.131-3.385a.142.142,0,0,1,.2,0,.14.14,0,0,1,0,.2c-1.051,1.1-2.1,2.24-3.125,3.379a.14.14,0,0,1-.1.046" transform="translate(-45.702 -18.723)"/>
          <path id="Path_33609" data-name="Path 33609" d="M100.494,50.551a.142.142,0,0,1-.091-.033.14.14,0,0,1-.017-.2,61.733,61.733,0,0,1,5.145-5.4.14.14,0,1,1,.191.206A61.333,61.333,0,0,0,100.6,50.5a.141.141,0,0,1-.107.05" transform="translate(-44.882 -20.074)"/>
          <path id="Path_33610" data-name="Path 33610" d="M100.849,55.151a.14.14,0,0,1-.1-.237l5.611-5.925a.14.14,0,1,1,.2.193l-5.611,5.925a.141.141,0,0,1-.1.044" transform="translate(-45.04 -21.89)"/>
          <path id="Path_33611" data-name="Path 33611" d="M106.249,57.586a.14.14,0,0,1-.1-.239c1.167-1.171,2.318-2.391,3.421-3.626a.141.141,0,0,1,.209.187c-1.106,1.238-2.26,2.461-3.431,3.636a.14.14,0,0,1-.1.041" transform="translate(-47.455 -24.005)"/>
          <g id="Group_13082" data-name="Group 13082" transform="translate(55.788 16.156)" opacity="0.129">
            <g id="Group_13081" data-name="Group 13081">
              <g id="Group_13080" data-name="Group 13080" clip-path="url(#clip-path-3)">
                <path id="Path_33612" data-name="Path 33612" d="M107.936,29.4a.613.613,0,0,0-.858,0c-.306.407-.118.546-.344,1.19a3.909,3.909,0,0,1-1.919,1.987,3.246,3.246,0,0,1-1.283.305,3.207,3.207,0,0,1-1.345-.174,1.984,1.984,0,0,1-.454-.209.54.54,0,1,0-.546.933c2.324,1.567,4.589,1.114,6.273-1.4a4.727,4.727,0,0,0,.616-1.877.794.794,0,0,0-.14-.759" transform="translate(-100.926 -29.227)"/>
              </g>
            </g>
          </g>
          <g id="Group_13085" data-name="Group 13085" transform="translate(58.432 21.322)" opacity="0.129">
            <g id="Group_13084" data-name="Group 13084">
              <g id="Group_13083" data-name="Group 13083" clip-path="url(#clip-path-4)">
                <path id="Path_33613" data-name="Path 33613" d="M107.831,44.094a23.262,23.262,0,0,1,.859,6.1q.023.154.045.308c0,.04.006.081.01.122.215-.027.429-.057.645-.083.381-.089.761-.187,1.139-.289.144-.07.287-.143.431-.213-.08-.6-.211-1.2-.343-1.8-.473-2.568-.789-5.258-2.074-7.578a4.36,4.36,0,0,0-1.651-2.007.854.854,0,0,0-.934,1.386,2.92,2.92,0,0,1,1,1.373,8.891,8.891,0,0,1,.87,2.681" transform="translate(-105.708 -38.573)"/>
              </g>
            </g>
          </g>
          <path id="Path_33614" data-name="Path 33614" d="M103.279,32.4a5.5,5.5,0,0,1-1.752-.289,3.068,3.068,0,0,1-2.107-2.176,4.377,4.377,0,0,1-.268-1.348,4.462,4.462,0,0,1,.306-1.652,5.449,5.449,0,0,1,1.707-2.434,4.156,4.156,0,0,1,2.945-.9,3.734,3.734,0,0,1,2.644,1.81,5.375,5.375,0,0,1,.817,2.941,4.472,4.472,0,0,1-.137,1.17,3.967,3.967,0,0,1-2.389,2.559,4.843,4.843,0,0,1-1.767.322m.462-7.978a3.37,3.37,0,0,0-2.055.741,4.606,4.606,0,0,0-1.437,2.061,3.647,3.647,0,0,0-.255,1.342,3.539,3.539,0,0,0,.221,1.09,2.242,2.242,0,0,0,1.581,1.657,4.358,4.358,0,0,0,2.941-.019,3.112,3.112,0,0,0,1.885-1.994,3.643,3.643,0,0,0,.108-.951,4.54,4.54,0,0,0-.68-2.482,3.021,3.021,0,0,0-2.024-1.432,2.712,2.712,0,0,0-.285-.014" transform="translate(-44.344 -10.547)"/>
          <path id="Path_33615" data-name="Path 33615" d="M95.224,38.421a14.268,14.268,0,1,1,14.268-14.268A14.284,14.284,0,0,1,95.224,38.421m0-27.694A13.426,13.426,0,1,0,108.65,24.153,13.441,13.441,0,0,0,95.224,10.727" transform="translate(-36.206 -4.421)"/>
          <path id="Path_33616" data-name="Path 33616" d="M.715,24.014A14.047,14.047,0,0,1,14.678,9.885a14.13,14.13,0,0,1,0,28.257A14.047,14.047,0,0,1,.715,24.014" transform="translate(-0.32 -4.421)" fill="#fff"/>
          <path id="Path_33617" data-name="Path 33617" d="M22.115,51.055a11.029,11.029,0,0,0,2.962-.4,9.359,9.359,0,0,0,1.153-.4,52.657,52.657,0,0,0-1.887-7.978c-.39-1.221-1.007-2.6-2.256-2.889a2.55,2.55,0,0,0-2.316.83,6.477,6.477,0,0,0-1.255,2.225,30.818,30.818,0,0,0-1.836,7.542,15.335,15.335,0,0,0,5.435,1.061" transform="translate(-7.46 -17.598)" fill="#ff5a5f"/>
          <path id="Path_33618" data-name="Path 33618" d="M26.08,50.211a.421.421,0,0,1-.391-.577,41.6,41.6,0,0,0-1.743-7.23c-.632-1.856-1.377-2.859-2.279-3.066a2.293,2.293,0,0,0-2.1.9,7.764,7.764,0,0,0-1.31,2.585,30.264,30.264,0,0,0-1.467,6.822.421.421,0,0,1-.838-.082,31.092,31.092,0,0,1,1.508-7.012,8.5,8.5,0,0,1,1.47-2.864,3.083,3.083,0,0,1,2.927-1.168c1.611.37,2.419,2.24,2.888,3.615A42.463,42.463,0,0,1,26.55,49.7a.42.42,0,0,1-.235.441l-.058.029a.418.418,0,0,1-.176.039" transform="translate(-7.132 -17.201)"/>
          <g id="Group_13088" data-name="Group 13088" transform="translate(11.699 16.055)" opacity="0.129">
            <g id="Group_13087" data-name="Group 13087">
              <g id="Group_13086" data-name="Group 13086" clip-path="url(#clip-path-5)">
                <path id="Path_33619" data-name="Path 33619" d="M27.11,29.351a.508.508,0,0,0-.9-.061c-.179.427.007.915-.044,1.364a3.373,3.373,0,0,1-.326,1.068c-.045.077.039-.039.068-.074-.036.045-.1.127-.134.179-.018.022-.036.044-.056.064-.305.3-.622.593-.938.883a3.259,3.259,0,0,1-.918.413,3.535,3.535,0,0,1-.379.091l-.032,0c-.426-.019-.853.025-1.279-.012l-.044-.006a.9.9,0,0,0-.658.039.634.634,0,0,0,.15,1.149,5.5,5.5,0,0,0,1.766.273,5.075,5.075,0,0,0,2.057-.786,3.891,3.891,0,0,0,1.669-4.587" transform="translate(-21.164 -29.044)"/>
              </g>
            </g>
          </g>
          <g id="Group_13091" data-name="Group 13091" transform="translate(14.821 21.763)" opacity="0.129">
            <g id="Group_13090" data-name="Group 13090">
              <g id="Group_13089" data-name="Group 13089" clip-path="url(#clip-path-6)">
                <path id="Path_33620" data-name="Path 33620" d="M29,48.072c.109,1.449.171,1.192.224,2.146.015.178.027.357.035.536.118-.029.237-.053.354-.085a9,9,0,0,0,1.331-.472c.066-.03.132-.063.2-.1-.114-.668-.251-1.391-.4-2.04-.305-1.38-.661-2.744-1.088-4.092a20.01,20.01,0,0,0-1.24-3.458,4.431,4.431,0,0,0-.559-.848.61.61,0,0,0-.5-.291.551.551,0,0,0-.386.932c1.589,1.983,1.7,5.429,2.025,7.769" transform="translate(-26.813 -39.371)"/>
              </g>
            </g>
          </g>
          <path id="Path_33621" data-name="Path 33621" d="M22.323,32.666a5.5,5.5,0,0,1-1.753-.289A3.071,3.071,0,0,1,18.464,30.2a4.377,4.377,0,0,1-.268-1.348A4.459,4.459,0,0,1,18.5,27.2a5.449,5.449,0,0,1,1.707-2.434,4.169,4.169,0,0,1,2.945-.9A3.735,3.735,0,0,1,25.8,25.674a5.385,5.385,0,0,1,.817,2.941,4.44,4.44,0,0,1-.137,1.17,3.965,3.965,0,0,1-2.389,2.559,4.84,4.84,0,0,1-1.767.322m.462-7.978a3.372,3.372,0,0,0-2.055.741,4.613,4.613,0,0,0-1.437,2.062,3.643,3.643,0,0,0-.255,1.342,3.554,3.554,0,0,0,.22,1.09,2.245,2.245,0,0,0,1.581,1.657,4.36,4.36,0,0,0,2.942-.019,3.112,3.112,0,0,0,1.884-1.994,3.637,3.637,0,0,0,.108-.951,4.543,4.543,0,0,0-.68-2.482A3.02,3.02,0,0,0,23.069,24.7a2.7,2.7,0,0,0-.284-.014" transform="translate(-8.138 -10.665)"/>
          <path id="Path_33622" data-name="Path 33622" d="M14.268,38.3A14.268,14.268,0,1,1,28.536,24.036,14.284,14.284,0,0,1,14.268,38.3m0-27.694A13.426,13.426,0,1,0,27.694,24.036,13.441,13.441,0,0,0,14.268,10.61" transform="translate(0 -4.369)"/>
          <path id="Path_33624" data-name="Path 33624" d="M67.137,2.662a.424.424,0,0,1-.132-.022c-1.045-.345-2.123-.647-3.2-.9a.421.421,0,1,1,.189-.82c1.107.255,2.211.563,3.28.917a.421.421,0,0,1-.132.821m-23.19-.044a.421.421,0,0,1-.13-.821C44.89,1.45,45.994,1.147,47.1.9a.421.421,0,0,1,.185.821c-1.082.244-2.162.54-3.21.879a.424.424,0,0,1-.129.02M60.583,1.151a.394.394,0,0,1-.057,0C59.432,1,58.317.9,57.211.846a.421.421,0,1,1,.038-.841c1.131.052,2.272.155,3.391.308a.421.421,0,0,1-.056.838M50.508,1.137A.421.421,0,0,1,50.454.3C51.574.15,52.714.05,53.844,0a.421.421,0,0,1,.036.842c-1.106.047-2.221.145-3.317.291a.48.48,0,0,1-.056,0" transform="translate(-19.466 0)"/>
          <g id="Group_13099" data-name="Group 13099" transform="translate(64.691 40.189) rotate(60)">
            <g id="Group_13098" data-name="Group 13098" transform="translate(0 0)" clip-path="url(#clip-path-7)">
              <path id="Path_33625" data-name="Path 33625" d="M7.313,14.976H7.282a.376.376,0,0,1-.343-.406A15.213,15.213,0,0,0,.558,1.035.376.376,0,0,1,.987.417a15.977,15.977,0,0,1,6.7,14.214.376.376,0,0,1-.374.345" transform="translate(-0.201 -0.177)"/>
              <path id="Path_33626" data-name="Path 33626" d="M1.718,4.568a.375.375,0,0,1-.361-.273A32.989,32.989,0,0,0,.03.523.376.376,0,0,1,.477.014L4.357,1.1a.376.376,0,0,1-.2.724L1.014.945c.406,1.028.763,2.083,1.066,3.144a.376.376,0,0,1-.259.464.394.394,0,0,1-.1.014" transform="translate(0 0)"/>
              <path id="Path_33627" data-name="Path 33627" d="M23.511,17.668l-.039,0a.376.376,0,0,1-.336-.413,18.561,18.561,0,0,0-1.614-9.7A.376.376,0,1,1,22.2,7.24a19.324,19.324,0,0,1,1.679,10.091.377.377,0,0,1-.374.337" transform="translate(-10.884 -3.556)"/>
              <path id="Path_33628" data-name="Path 33628" d="M7.313,14.976H7.282a.376.376,0,0,1-.343-.406A15.213,15.213,0,0,0,.558,1.035.376.376,0,0,1,.987.417a15.977,15.977,0,0,1,6.7,14.214.376.376,0,0,1-.374.345" transform="translate(-0.201 -0.177)"/>
              <path id="Path_33629" data-name="Path 33629" d="M1.718,4.568a.375.375,0,0,1-.361-.273A32.989,32.989,0,0,0,.03.523.376.376,0,0,1,.477.014L4.357,1.1a.376.376,0,0,1-.2.724L1.014.945c.406,1.028.763,2.083,1.066,3.144a.376.376,0,0,1-.259.464.394.394,0,0,1-.1.014" transform="translate(0 0)"/>
              <path id="Path_33630" data-name="Path 33630" d="M23.511,17.668l-.039,0a.376.376,0,0,1-.336-.413,18.561,18.561,0,0,0-1.614-9.7A.376.376,0,1,1,22.2,7.24a19.324,19.324,0,0,1,1.679,10.091.377.377,0,0,1-.374.337" transform="translate(-10.884 -3.556)"/>
            </g>
          </g>
          <g id="Group_13101" data-name="Group 13101" transform="translate(2.078 36.95)">
            <g id="Group_13100" data-name="Group 13100" transform="translate(0 0)" clip-path="url(#clip-path-8)">
              <path id="Path_33631" data-name="Path 33631" d="M18.242,17.957a.474.474,0,0,1-.3-.107A21.007,21.007,0,0,1,10.252,1.61a.477.477,0,0,1,.477-.476h0a.478.478,0,0,1,.476.479,20.045,20.045,0,0,0,7.337,15.5.477.477,0,0,1-.3.848" transform="translate(-3.829 -0.423)"/>
              <path id="Path_33632" data-name="Path 33632" d="M4.293,12.333a.475.475,0,0,1-.378-.185A18.878,18.878,0,0,1,0,.472.477.477,0,0,1,.478,0H.483A.478.478,0,0,1,.956.482,17.924,17.924,0,0,0,4.67,11.564a.477.477,0,0,1-.086.669.471.471,0,0,1-.291.1" transform="translate(0 0)"/>
              <path id="Path_33633" data-name="Path 33633" d="M19.669,24.919a.454.454,0,0,1-.1-.011,34.033,34.033,0,0,1-5.231-1.57.477.477,0,1,1,.347-.89,33.1,33.1,0,0,0,4.462,1.387,17.56,17.56,0,0,0-.887-4.142.477.477,0,1,1,.9-.311,18.6,18.6,0,0,1,.984,5.033.477.477,0,0,1-.477.5" transform="translate(-5.241 -7.119)"/>
            </g>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
