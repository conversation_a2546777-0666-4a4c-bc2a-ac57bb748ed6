name: modarby
description: A new Flutter project.

publish_to: "none"

version: 5.2.21+491
environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  top_modal_sheet: ^2.1.0
  flutter_timezone:
  timezone_to_country: ^2.1.0
  showcaseview: ^4.0.0
  dependency_validator:
  popover:

  table_calendar:
    git:
      url: https://github.com/mohammedhasna2015/table_calendar.git
      ref: v1.0.2
  flutter_svg:
  intl:
  percent_indicator: ^4.2.3
  dotted_border: ^2.0.0+3
  app_settings: ^5.0.0
  bubble: ^1.2.1
  get_it: ^8.0.3
  flutter_pagewise: ^2.0.4
  agora_rtc_engine: ^6.5.0
  dio: ^5.3.2
  path:
  store_redirect: ^2.0.1
  shimmer: ^3.0.0
  flutter_searchable_dropdown: ^2.0.0
  flutter_keyboard_visibility: ^6.0.0
  another_flushbar: ^1.12.29
  flag:
    git:
      url: https://github.com/mohammedhasna2015/flag_flutter.git
      ref: patch-2
  skeletons:
    git:
      url: https://github.com/mohammedhasna2015/skeletons.git
      ref: v1.0
  badges: ^3.0.2
  flutter_screenutil: ^5.9.0
  flutter_staggered_grid_view: ^0.7.0
  pin_input_text_field: ^4.5.0
  flutter_rating_bar: ^4.0.1
  flutter_cache_manager: ^3.3.1
  flutter_background_service: ^5.0.10
  vibration: ^2.0.1
  url_launcher: ^6.3.1
  app_links: ^6.3.3
  share_plus: ^10.1.3
  internet_connection_checker: ^1.0.0+1
  connectivity_plus: ^6.1.2
  http: ^0.13.5
  uuid: ^4.4.0
  geocoder2: ^1.4.0
  google_maps_webservice_ex: ^0.0.1+2
  webview_flutter: ^4.10.0
  file_picker: ^8.1.7
  image_picker: ^1.0.4
  image_cropper: ^4.0.1
  chewie: ^1.4.0
  video_player: ^2.6.0
  youtube_player_flutter: ^9.1.1
  record: ^5.2.0
  just_audio: ^0.9.32
  package_info_plus:
  device_info_plus: ^10.1.2
  path_provider: ^2.0.14
  permission_handler:
  in_app_review: ^2.0.6
  flutter_app_badge_control: ^0.0.2
  chopper: ^6.1.1
  envied: ^1.0.0
  envied_generator: ^1.0.0
  super_tooltip: ^2.0.9
  provider: ^6.0.5
  flutter_spinkit: ^5.2.1
  flutter_callkit_incoming:
    git:
      url: https://github.com/mohammedhasna2015/flutter_callkit_incoming.git
      ref: v1.0.5
  shared_preferences: ^2.0.20
  audioplayers: ^6.1.0
  firebase_core: ^3.12.1
  firebase_database: ^11.3.4
  firebase_messaging: ^15.2.4
  firebase_crashlytics: ^4.3.4
  firebase_analytics: ^11.4.4
  flutter_local_notifications: ^18.0.1
  collection: ^1.15.0-nullsafety.4
  flutter_lints: ^5.0.0
  gif_view: ^0.3.1
  whatsapp_unilink: ^2.1.0
  in_app_update: ^4.2.2
  slide_countdown: ^2.0.2
  carousel_slider: ^5.0.0
  dots_indicator: ^3.0.0
  audio_waveforms: ^1.0.4
  readmore: ^3.0.0
  lottie:
dev_dependencies:
  flutter_test:
    sdk: flutter

  build_runner: ^2.3.3
  chopper_generator: ^6.0.0

  flutter_launcher_icons: ^0.14.1
  change_app_package_name: ^1.1.0

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/app_icon.webp"
  remove_alpha_ios: true
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/images/app_icon.webp"
  adaptive_icon_monochrome: "assets/images/app_icon.webp"

analyzer:
  enable-experiment:
    - non-nullable

flutter:

  uses-material-design: true
  assets:
    - assets/content/
    - assets/images/
    - assets/images/warning_message/
    - assets/images/actions_message/
    - assets/images/chat_message/
    - assets/images/search/
    - assets/images/player/
    - assets/icons/
    - assets/audio/
    - assets/images/booking/
    - assets/images/auth/
    - assets/images/emergency_help/
    - assets/images/live_tutors/
    - assets/images/instructor_images/
    - assets/animation/
    - assets/images/cv_upload/
    - assets/images/voice_call/
    - assets/
    - assets/fonts/
    - assets/fonts/Cairo/
    - assets/tunes/



  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo/Cairo-Black.ttf
        - asset: assets/fonts/Cairo/Cairo-Bold.ttf
          weight: 600
        - asset: assets/fonts/Cairo/Cairo-ExtraBold.ttf
          weight: 700
        - asset: assets/fonts/Cairo/Cairo-ExtraLight.ttf
        - asset: assets/fonts/Cairo/Cairo-Light.ttf
          weight: 300
        - asset: assets/fonts/Cairo/Cairo-Medium.ttf
        - asset: assets/fonts/Cairo/Cairo-Regular.ttf
          weight: 400
        - asset: assets/fonts/Cairo/Cairo-SemiBold.ttf
          weight: 500











